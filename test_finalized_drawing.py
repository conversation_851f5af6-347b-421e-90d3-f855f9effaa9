import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing
import importlib.util
import sys

# Directly import the generate_finalized_drawing function from utils.py
spec = importlib.util.spec_from_file_location("utils", "api/utils.py")
utils_module = importlib.util.module_from_spec(spec)
sys.modules["utils"] = utils_module
spec.loader.exec_module(utils_module)

# Get a drawing that has symbols
drawing = Drawing.objects.filter(processed=True).first()
if drawing:
    print(f"Found drawing: {drawing.id} - {drawing.original_filename}")
    # Generate the finalized image
    finalized_image_path = utils_module.generate_finalized_drawing(drawing)
    if finalized_image_path:
        print(f"Generated finalized image: {finalized_image_path}")
        drawing.finalized_image = finalized_image_path
        drawing.save()
        print("Drawing updated with new finalized image")
    else:
        print("Failed to generate finalized image")
else:
    print("No processed drawings found")
