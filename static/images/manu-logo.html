<!DOCTYPE html>
<html>
<head>
    <title>Convert SVG to PNG</title>
    <script>
        window.onload = function() {
            const svg = document.getElementById('manuLogo');
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas dimensions
            canvas.width = 200;
            canvas.height = 200;
            
            // Create an image from the SVG
            const img = new Image();
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                // Draw the image on the canvas
                ctx.drawImage(img, 0, 0);
                
                // Convert canvas to PNG
                const pngUrl = canvas.toDataURL('image/png');
                
                // Display the PNG
                document.getElementById('pngOutput').src = pngUrl;
                document.getElementById('downloadLink').href = pngUrl;
                document.getElementById('downloadLink').style.display = 'block';
                
                // Clean up
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        };
    </script>
</head>
<body>
    <svg id="manuLogo" width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="#e30613"/>
        <path d="M100 40 L160 70 L160 130 L100 160 L40 130 L40 70 Z" stroke="white" stroke-width="4" fill="none"/>
        <path d="M100 60 L140 80 L140 120 L100 140 L60 120 L60 80 Z" stroke="white" stroke-width="4" fill="none"/>
        <path d="M60 80 L100 60 L140 80 M100 60 L100 140" stroke="white" stroke-width="4" fill="none"/>
        <text x="100" y="180" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">MANU</text>
    </svg>
    
    <div style="margin-top: 20px;">
        <h3>PNG Output:</h3>
        <img id="pngOutput" alt="PNG version of the logo" style="border: 1px solid #ccc;">
        <a id="downloadLink" download="manu-logo.png" style="display: none; margin-top: 10px;">Download PNG</a>
    </div>
</body>
</html>
