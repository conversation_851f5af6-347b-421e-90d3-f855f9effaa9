import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Segment, Symbol
from django.conf import settings
from PIL import Image, ImageDraw, ImageFont
import time

# Get the drawing
drawing_id = 28
drawing = Drawing.objects.get(id=drawing_id)
print(f"Testing image generation for drawing {drawing_id}: {drawing.original_filename}")

# Generate a new finalized image with a timestamp
def generate_test_image(drawing):
    timestamp = int(time.time())

    # Load the original drawing image
    image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
    if not os.path.exists(image_path):
        print(f"Original image not found at {image_path}")
        return None

    print(f"Loading original image from {image_path}")

    # Open the image with PIL
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)

    # Try to load a font, fall back to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 40)  # Larger font for visibility
        print("Using Arial font")
    except IOError:
        try:
            # Try system fonts
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 40)
            print("Using Helvetica font")
        except IOError:
            font = ImageFont.load_default()
            print("Using default font")

    # Get all segments for this drawing
    segments = Segment.objects.filter(drawing=drawing)

    # Define the segment order (2, 1, 3, 4, ...)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    # Initialize a counter for symbol numbering
    symbol_counter = 1

    # Process each segment in the specified order
    for segment in segment_order:
        print(f"Processing segment {segment.segment_number}")
        # Get all marked symbols for this segment
        symbols = Symbol.objects.filter(segment=segment, is_marked=True)
        print(f"  Found {symbols.count()} marked symbols")

        # Calculate the offset for this segment
        x_offset = segment.segment_x_offset or 0
        y_offset = segment.segment_y_offset or 0
        print(f"  Segment offset: ({x_offset}, {y_offset})")

        # Draw each symbol with its sequential number
        for symbol in symbols:
            # Calculate the absolute position in the full drawing
            abs_x = x_offset + symbol.x_coordinate
            abs_y = y_offset + symbol.y_coordinate
            print(f"  Symbol {symbol_counter}: {symbol.value} at ({abs_x}, {abs_y})")

            # Draw a circle for the symbol - make it larger and more visible
            circle_radius = 30
            draw.ellipse(
                [(abs_x - circle_radius, abs_y - circle_radius),
                 (abs_x + circle_radius, abs_y + circle_radius)],
                fill="blue",
                outline="blue",
                width=5
            )

            # Draw the symbol number
            try:
                # For newer PIL versions
                left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                text_width = right - left
                text_height = bottom - top
            except AttributeError:
                # Fallback for older PIL versions
                try:
                    text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                except:
                    text_width, text_height = 20, 20  # Default fallback values

            draw.text(
                (abs_x - text_width/2, abs_y - text_height/2),
                str(symbol_counter),
                fill="white",
                font=font
            )

            # Increment the counter
            symbol_counter += 1

    # Save the finalized image with timestamp
    output_path = os.path.join(settings.MEDIA_ROOT, f"test_finalized_{drawing_id}_{timestamp}.png")
    img.save(output_path)
    print(f"Saved test image to {output_path}")

    return output_path

# Generate the test image
test_image_path = generate_test_image(drawing)

# Check if the test image exists
if test_image_path and os.path.exists(test_image_path):
    print(f"Test image created successfully at {test_image_path}")
    # Check file size
    size = os.path.getsize(test_image_path)
    print(f"File size: {size} bytes")
else:
    print("Failed to create test image")
