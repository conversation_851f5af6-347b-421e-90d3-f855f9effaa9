<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f0f0;
        }
        .logo-container {
            width: 200px;
            height: 200px;
            background-color: #e30613;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .logo-text {
            color: white;
            font-family: Arial, sans-serif;
            font-size: 24px;
            font-weight: bold;
            position: absolute;
            bottom: 20px;
        }
        .hexagon-outer {
            width: 120px;
            height: 120px;
            position: relative;
        }
        .hexagon-inner {
            width: 80px;
            height: 80px;
            position: absolute;
            top: 20px;
            left: 20px;
        }
        .hexagon-outer:before, .hexagon-inner:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid white;
            box-sizing: border-box;
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        }
        .m-shape {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 80px;
            height: 80px;
        }
        .m-shape:before, .m-shape:after {
            content: "";
            position: absolute;
            background-color: transparent;
            border: 4px solid white;
        }
        .m-shape:before {
            top: 0;
            left: 40px;
            width: 0;
            height: 80px;
        }
        .m-shape:after {
            top: 0;
            left: 0;
            width: 80px;
            height: 0;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <div class="hexagon-outer"></div>
        <div class="hexagon-inner"></div>
        <div class="m-shape"></div>
        <div class="logo-text">MANU</div>
    </div>
</body>
</html>
