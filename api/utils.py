import os
import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>, Border, Side, Font, PatternFill
from openpyxl.utils import get_column_letter
from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
from api.models import Segment, Symbol

# Fix PIL image size limit to prevent decompression bomb warnings
Image.MAX_IMAGE_PIXELS = None

def generate_finalized_drawing(drawing):
    """
    Generate a finalized drawing with numbered bubbles.
    The numbering follows the specified segment order: 2, 1, 3, 4.
    """
    # Load the original drawing image
    image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
    if not os.path.exists(image_path):
        return None

    # Open the image with PIL
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)

    # Try to load a font, fall back to default if not available
    # Increased font size from 40 to 60 for better visibility
    font_size = 60
    try:
        # Try to load Arial Bold first for better visibility
        font = ImageFont.truetype("arialbd.ttf", font_size)
    except IOError:
        try:
            # Try regular Arial
            font = ImageFont.truetype("arial.ttf", font_size)
        except IOError:
            try:
                # Try system fonts - Helvetica Bold
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica-Bold.ttc", font_size)
            except IOError:
                try:
                    # Try regular Helvetica
                    font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                except IOError:
                    font = ImageFont.load_default()

    # Get all segments for this drawing
    segments = Segment.objects.filter(drawing=drawing)

    # Define the segment order (2, 1, 3, 4, ...)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    # Initialize a counter for symbol numbering across all segments
    symbol_counter = 1

    # Process each segment in the specified order (2, 1, 3, 4)
    for segment in segment_order:
        # Get all marked symbols for this segment
        symbols = Symbol.objects.filter(segment=segment, is_marked=True)

        # Calculate the offset for this segment
        x_offset = segment.segment_x_offset or 0
        y_offset = segment.segment_y_offset or 0

        # Draw each symbol with its sequential number
        for symbol in symbols:
            # Calculate the absolute position in the full drawing
            abs_x = x_offset + symbol.x_coordinate
            abs_y = y_offset + symbol.y_coordinate

            # Draw a circle for the symbol - larger blue bubble with fill for better contrast
            circle_radius = 50  # Larger radius for better visibility
            draw.ellipse(
                [(abs_x - circle_radius, abs_y - circle_radius),
                 (abs_x + circle_radius, abs_y + circle_radius)],
                fill="blue",  # Fill with blue for better contrast
                outline="blue",
                width=5  # Thicker outline
            )

            # Add inner circle with white outline for better text visibility
            inner_radius = int(circle_radius * 0.9)
            draw.ellipse(
                [(abs_x - inner_radius, abs_y - inner_radius),
                 (abs_x + inner_radius, abs_y + inner_radius)],
                fill="blue",
                outline="white",
                width=2
            )

            # Draw the symbol number
            # PIL.ImageDraw.Draw.textsize is deprecated, use textbbox or textlength instead
            try:
                # For newer PIL versions
                left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                text_width = right - left
                text_height = bottom - top
            except AttributeError:
                # Fallback for older PIL versions
                try:
                    text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                except:
                    text_width, text_height = 10, 10  # Default fallback values

            # Draw white text with segment number - add stroke for better visibility
            text_x = abs_x - text_width/2
            text_y = abs_y - text_height/2

            # Draw text stroke (outline) in black for better contrast
            stroke_width = 2
            for dx in range(-stroke_width, stroke_width + 1):
                for dy in range(-stroke_width, stroke_width + 1):
                    if dx != 0 or dy != 0:  # Don't draw at the center position
                        draw.text(
                            (text_x + dx, text_y + dy),
                            str(symbol_counter),
                            fill="black",
                            font=font
                        )

            # Draw the main white text on top
            draw.text(
                (text_x, text_y),
                str(symbol_counter),
                fill="white",
                font=font
            )

            # Increment the counter
            symbol_counter += 1

    # Save the finalized image
    output_path = os.path.join(settings.MEDIA_ROOT, f"finalized_{drawing.id}.png")
    img.save(output_path)

    # Return the relative path
    return f"finalized_{drawing.id}.png"

def generate_measurement_excel(drawing):
    """
    Generate an Excel file with measurements from the drawing.
    The Excel file will have the following columns:
    1. S.No. (Sequential number)
    2. Measurement (Symbol value)
    3. Min Value
    4. Max Value
    5. Instrument Value (Yes/No dropdown)
    6. Remarks (Empty for user to fill)
    """
    # Create a new workbook and select the active worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Measurements"

    # Define column headers
    headers = ["S.No.", "Measurement", "Min Value", "Max Value", "Instrument Value (Yes/No)", "Remarks"]

    # Set column headers with styling
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    header_font = Font(bold=True, name='Calibri', size=11)
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # Get all segments for this drawing in the specified order (2, 1, 3, 4)
    segments = Segment.objects.filter(drawing=drawing)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    # Initialize row counter and symbol counter
    row_idx = 2  # Start from row 2 (after headers)
    symbol_counter = 1  # Start from 1 and continue across all segments

    # Process each segment in the specified order
    for segment in segment_order:
        # Get all marked symbols for this segment
        symbols = Symbol.objects.filter(segment=segment, is_marked=True).order_by('value')

        for symbol in symbols:
            # Ensure proper Unicode handling for symbol value
            symbol_value = str(symbol.value) if symbol.value else ""

            # Clean and normalize Unicode characters for better Excel compatibility
            # Handle various encoding issues that can occur with special characters

            # Fix diameter symbol variations
            symbol_value = symbol_value.replace('\u00e2\u0082\u00ac', '\u00d8')  # â‚¬ -> Ø
            symbol_value = symbol_value.replace('â‚¬', 'Ø')                      # Direct replacement
            symbol_value = symbol_value.replace('âˆ…', 'Ø')                      # Another diameter variant

            # Fix plus-minus symbol variations
            symbol_value = symbol_value.replace('\u00c2\u00b1', '\u00b1')        # Â± -> ±
            symbol_value = symbol_value.replace('Â±', '±')                       # Direct replacement

            # Fix degree symbol variations
            symbol_value = symbol_value.replace('\u00c2\u00b0', '\u00b0')        # Â° -> °
            symbol_value = symbol_value.replace('Â°', '°')                       # Direct replacement

            # Fix minus symbol variations
            symbol_value = symbol_value.replace('\u00e2\u0088\u0092', '-')       # âˆ' -> -
            symbol_value = symbol_value.replace('\u2212', '-')                   # Unicode minus -> -

            # Additional common encoding fixes for quotes and other characters
            symbol_value = symbol_value.replace('\u201c', '"')                   # Left double quote
            symbol_value = symbol_value.replace('\u201d', '"')                   # Right double quote
            symbol_value = symbol_value.replace('\u2019', "'")                   # Right single quote
            symbol_value = symbol_value.replace('\u2018', "'")                   # Left single quote

            # Add symbol data to the worksheet with sequential numbering
            s_no_cell = ws.cell(row=row_idx, column=1, value=symbol_counter)  # S.No.
            measurement_cell = ws.cell(row=row_idx, column=2, value=symbol_value)    # Measurement

            # Set font for all cells to ensure proper Unicode rendering
            cell_font = Font(name='Calibri', size=10)
            s_no_cell.font = cell_font
            measurement_cell.font = cell_font

            # Try to parse min and max values from the measurement
            try:
                # Handle different formats like "0.4±0.1", "Ø11.112", etc.
                value = symbol_value

                # Handle diameter symbol
                if value.startswith('Ø') or value.startswith('ø'):
                    value = value[1:]

                # Handle plus/minus tolerance format (e.g., "0.4±0.1")
                if '±' in value:
                    base_value, tolerance = value.split('±')
                    base_value = float(base_value)
                    tolerance = float(tolerance)
                    min_value = base_value - tolerance
                    max_value = base_value + tolerance
                # Handle range format with ± (e.g., "58.4 ±0.25")
                elif '±' in value.replace(' ', ''):
                    parts = value.replace(' ', '').split('±')
                    base_value = float(parts[0])
                    tolerance = float(parts[1])
                    min_value = base_value - tolerance
                    max_value = base_value + tolerance
                # Handle simple numeric values
                else:
                    try:
                        # Try to convert to float
                        numeric_value = float(value)
                        min_value = numeric_value
                        max_value = numeric_value
                    except ValueError:
                        # If not a simple number, just use the value as is
                        min_value = value
                        max_value = value
            except Exception:
                # If parsing fails, just use the original value
                min_value = symbol_value
                max_value = symbol_value

            # Set min and max values with proper font
            min_cell = ws.cell(row=row_idx, column=3, value=min_value)  # Min Value
            max_cell = ws.cell(row=row_idx, column=4, value=max_value)  # Max Value
            min_cell.font = cell_font
            max_cell.font = cell_font

            # Center align numeric values
            center_alignment = Alignment(horizontal='center', vertical='center')
            s_no_cell.alignment = center_alignment
            min_cell.alignment = center_alignment
            max_cell.alignment = center_alignment

            # Set default Value to "Yes"
            value_cell = ws.cell(row=row_idx, column=5, value="Yes")
            value_cell.font = cell_font
            value_cell.alignment = center_alignment

            # Leave Remarks empty
            remarks_cell = ws.cell(row=row_idx, column=6, value="")
            remarks_cell.font = cell_font

            # Apply borders to all cells in the row
            for col_idx in range(1, 7):
                ws.cell(row=row_idx, column=col_idx).border = thin_border

            # Increment counters
            row_idx += 1
            symbol_counter += 1

    # Add data validation for Yes/No dropdown in column 5 (Instrument Value)
    # This creates a dropdown with Yes/No options for all rows with data
    if row_idx > 2:  # If we have at least one data row
        dv = openpyxl.worksheet.datavalidation.DataValidation(
            type="list",
            formula1='"Yes,No"',
            allow_blank=True
        )
        dv.add(f'E2:E{row_idx-1}')  # Apply to all data rows in column E
        ws.add_data_validation(dv)

    # Auto-adjust column widths
    column_widths = [8, 20, 12, 12, 18, 15]  # Custom widths for each column
    for col_idx, width in enumerate(column_widths, 1):
        column_letter = get_column_letter(col_idx)
        ws.column_dimensions[column_letter].width = width

    # Save the workbook with proper encoding
    excel_path = os.path.join(settings.MEDIA_ROOT, f"measurements_{drawing.id}.xlsx")

    # Ensure the workbook is saved with proper Unicode support
    try:
        wb.save(excel_path)
    except Exception as e:
        print(f"Error saving Excel file: {e}")
        # Try alternative save method
        with open(excel_path, 'wb') as f:
            wb.save(f)

    # Return the relative path
    return f"measurements_{drawing.id}.xlsx"
