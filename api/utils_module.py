import os
import base64
import logging
import requests
import json
from io import BytesIO
from PIL import Image
import numpy as np
from django.conf import settings

logger = logging.getLogger(__name__)

def encode_image_to_base64(image_path):
    """
    Encode an image file to base64 string.

    Args:
        image_path: Path to the image file

    Returns:
        Base64 encoded string of the image
    """
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        logger.error(f"Error encoding image to base64: {e}")
        return None

def call_gpt41_api(image_base64, segment_number=1):
    """
    Call the GPT-4.1 API with an image and the specialized technical drawing prompt.

    Args:
        image_base64: Base64 encoded image
        segment_number: The segment number being analyzed

    Returns:
        The API response text
    """
    endpoint = "https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview"
    api_key = "EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG"

    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }

    # The specialized prompt for technical drawing analysis
    prompt = f"""
    You are a precise vision assistant specialized in analyzing technical drawings with coordinate grids.
    Your task is to identify specific engineering symbols AND determine their location.

    Look at this image which contains a small section (segment #{segment_number}) of a technical drawing.

    1. Scan the image for the following engineering symbols and their associated FULL measurement values:
       - Diameter (prefixed with Ø or ⌀)
       - Radius (prefixed with R, followed by a number)
       - Tolerance (containing ±)
       - Degree (ending with °)
       - Boxed Numbers (enclosed in |...|)
       - Area measurements (containing mm², cm², or similar area units)
       - Cross-section values (containing "cross-section" or similar terms with numerical values)
       - Dimensions with tolerance values
       - Nominal dimensions or measurements with units (mm, m, etc.)
       - Don't read single digit numbers
       - There would be no commas (,) only decimals (.)

    2. CRITICAL INSTRUCTIONS FOR COMBINED VALUES:
       - ALWAYS INCLUDE THE FULL VALUE WITH TOLERANCES in a single symbol
       - When you see a dimension with a tolerance stacked above or below it (like 0.30 with +0.100 above it), COMBINE them as ONE value (e.g., "0.30 +0.100")
       - When you see a diameter with tolerance like "Ø1.95 ±0.05", report the ENTIRE string as ONE diameter value
       - When you see a dimension with tolerance like "4.519 ±0.075", report the ENTIRE string as ONE value
       - When you see "Nominal cross-section 6.517 mm²", report the ENTIRE string including units
       - NEVER separate a measurement from its tolerance or units - they belong together
       - Example: "Ø1.95 ±0.05" should be ONE symbol of type "diameter" with value "Ø1.95 ±0.05"
       - Example: "4.519 ±0.075" should be ONE symbol of type "tolerance" with value "4.519 ±0.075"
       - Example: "6.517 mm²" should be ONE symbol of type "text" with value "6.517 mm²"
       - Example: When "0.30" has "+0.100" stacked above it, report as ONE symbol of type "tolerance" with value "0.30 +0.100"
       - If you see a value like "Ø1.95" and nearby "±0.05", combine them as "Ø1.95 ±0.05"

    3. For each symbol you find, provide:
       - The symbol type (diameter, radius, tolerance, degree, boxed, text)
       - The EXACT and COMPLETE value (including ALL parts - main value, tolerance, and units if present)

    Important: You MUST identify any engineering symbols present in the image, even if they're partial or at the edges.
    Look carefully for dimension values, tolerances, diameters, and other technical measurements.
    Pay special attention to tolerance values that might be stacked above or below main dimension values.
    """

    # Construct the payload
    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 4000,
        "response_format": {"type": "json_object"}
    }

    try:
        # Make the API call
        response = requests.post(endpoint, json=payload, headers=headers, timeout=60.0)
        response.raise_for_status()  # Raise exception for HTTP errors

        # Extract and return the response content
        response_data = response.json()
        if 'choices' in response_data and len(response_data['choices']) > 0:
            content = response_data['choices'][0]['message']['content']
            return content
        else:
            logger.error(f"Unexpected API response format: {response_data}")
            return None
    except Exception as e:
        logger.error(f"Error calling GPT-4.1 API: {e}")
        return None

def parse_gpt41_response(response_text):
    """
    Parse the GPT-4.1 API response into a structured format.

    Args:
        response_text: The JSON response from the API

    Returns:
        List of symbol dictionaries
    """
    try:
        # Parse the JSON response
        response_data = json.loads(response_text)

        # Extract symbols from the response
        symbols = []

        if 'symbols' in response_data:
            for symbol in response_data['symbols']:
                # Get the symbol type, defaulting to 'other' if not found
                symbol_type = symbol.get('symbol_type', 'other').lower()

                # Map 'text' type to 'other' for consistency with our database model
                if symbol_type == 'text':
                    symbol_type = 'other'

                # Get the value
                value = symbol.get('value', '')

                # Get coordinates from symbol if provided, otherwise use default position
                x = symbol.get('x_coordinate', symbol.get('x', 100))
                y = symbol.get('y_coordinate', symbol.get('y', 100))

                # If coordinates are still not valid, use a default position
                # This should be updated by EasyOCR or GPT Vision later
                if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
                    x = 100
                    y = 100

                # Add to symbols list
                symbols.append({
                    'type': symbol_type,
                    'value': value,
                    'x': int(x),
                    'y': int(y),
                    'marked': True  # Mark all detected symbols as significant
                })

        return symbols
    except Exception as e:
        logger.error(f"Error parsing GPT-4.1 response: {e}")
        return []

def segment_image(image_path, num_segments=4):
    """
    Segment an image into equal parts.

    Args:
        image_path: Path to the image file
        num_segments: Number of segments (must be a perfect square)

    Returns:
        List of dictionaries with segment information
    """
    try:
        # Open the image
        img = Image.open(image_path)
        width, height = img.size

        # Calculate grid dimensions
        grid_size = int(np.sqrt(num_segments))
        segment_width = width // grid_size
        segment_height = height // grid_size

        segments = []
        segment_number = 1

        # Create segments
        for y in range(grid_size):
            for x in range(grid_size):
                # Calculate segment coordinates
                left = x * segment_width
                upper = y * segment_height
                right = left + segment_width
                lower = upper + segment_height

                # Crop the segment
                segment_img = img.crop((left, upper, right, lower))

                # Create directory for temporary files if it doesn't exist
                import os
                temp_dir = os.path.join(os.path.dirname(image_path), 'temp_segments')
                os.makedirs(temp_dir, exist_ok=True)

                # Save segment to a temporary file
                segment_path = os.path.join(temp_dir, f"temp_segment_{segment_number}.png")
                segment_img.save(segment_path)

                # Add segment info to list
                segments.append({
                    'number': segment_number,
                    'x_offset': left,
                    'y_offset': upper,
                    'width': segment_width,
                    'height': segment_height,
                    'path': segment_path
                })

                segment_number += 1

        return segments
    except Exception as e:
        logger.error(f"Error segmenting image: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []
