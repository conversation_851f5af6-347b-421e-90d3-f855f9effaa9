from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .utils.direct_bubble_generator import generate_bubbles_direct
from .utils.download_bubbles import download_bubbles
from .utils.overlay_bubbles import overlay_bubbles

router = DefaultRouter()
router.register(r'companies', views.CompanyViewSet)
router.register(r'cage-types', views.CageTypeViewSet)
router.register(r'templates', views.TemplateViewSet)

urlpatterns = [
    # Router URLs
    path('', include(router.urls)),

    # Test endpoint
    path('test/', views.test_view, name='test-view'),

    # Drawing management
    path('upload/', views.upload_drawing, name='upload-drawing'),
    path('upload-process-b/', views.upload_drawing_process_b, name='upload-drawing-process-b'),
    path('analyze/<int:drawing_id>/', views.analyze_drawing, name='analyze-drawing'),
    path('segment-only/<int:drawing_id>/', views.segment_only, name='segment-only'),
    path('results/<int:drawing_id>/', views.get_drawing_results, name='get-drawing-results'),
    path('finalize/<int:drawing_id>/', views.finalize_drawing, name='finalize-drawing'),
    path('create-excel/<int:drawing_id>/', views.create_excel_sheet, name='create-excel-sheet'),
    path('adjust-bubbles/<int:drawing_id>/', views.adjust_final_bubbles, name='adjust-final-bubbles'),
    path('mark-as-template/<int:drawing_id>/', views.mark_as_template, name='mark-as-template'),

    # Segment management
    path('segment/<int:segment_id>/', views.get_segment_details, name='get-segment-details'),
    path('segment/<int:segment_id>/save/', views.save_segment_with_bubble_size, name='save-segment-with-bubble-size'),
    path('segment/<int:segment_id>/similar/', views.find_similar_segments_view, name='find-similar-segments'),
    path('segment/<int:segment_id>/apply-similar/<int:similar_id>/', views.apply_similar_segment, name='apply-similar-segment'),
    path('segment/<int:segment_id>/preview-similar/<int:similar_id>/', views.preview_similar_segment, name='preview-similar-segment'),
    path('segment/<int:segment_id>/confirm-similarity/', views.confirm_segment_similarity, name='confirm-segment-similarity'),

    # Symbol management
    path('symbol/mark/<int:symbol_id>/', views.mark_symbol, name='mark-symbol'),
    path('symbol/unmark/<int:symbol_id>/', views.unmark_symbol, name='unmark-symbol'),
    path('symbol/position/<int:symbol_id>/', views.update_symbol_position, name='update-symbol-position'),
    path('symbol/edit/<int:symbol_id>/', views.edit_symbol, name='edit-symbol'),
    path('symbol/delete/<int:symbol_id>/', views.delete_symbol, name='delete-symbol'),
    path('symbol/bulk-delete/', views.bulk_delete_symbols, name='bulk-delete-symbols'),
    path('segment/<int:segment_id>/add-symbol/', views.add_symbol, name='add-symbol'),
    path('segment/<int:segment_id>/add-symbol-at-click/', views.add_symbol_at_click, name='add-symbol-at-click'),
    path('segment/<int:segment_id>/find-positions-with-easyocr/', views.find_positions_with_easyocr, name='find-positions-with-easyocr'),
    path('segment/<int:segment_id>/find-positions-with-gpt-vision/', views.find_positions_with_gpt_vision, name='find-positions-with-gpt-vision'),
    path('segment/<int:segment_id>/easyocr-detections/', views.get_easyocr_detections, name='get-easyocr-detections'),
    path('segment/<int:segment_id>/gpt-vision-detections/', views.get_gpt_vision_detections, name='get-gpt-vision-detections'),
    path('segment/<int:segment_id>/gpt41-detections/', views.get_gpt41_detections, name='get-gpt41-detections'),
    path('segment/<int:segment_id>/gpt-vision-matches/', views.get_gpt_vision_matches, name='get-gpt-vision-matches'),
    path('segment/<int:segment_id>/delete-simple-values/', views.delete_simple_values, name='delete-simple-values'),
    path('segment/<int:segment_id>/delete-duplicate-orientation-symbols/', views.delete_duplicate_orientation_symbols, name='delete-duplicate-orientation-symbols'),
    path('segment/<int:segment_id>/remove-spaces-from-values/', views.remove_spaces_from_values, name='remove-spaces-from-values'),
    path('segment/<int:segment_id>/distribute-duplicate-value-symbols/', views.distribute_duplicate_value_symbols, name='distribute-duplicate-value-symbols'),
    path('segment/<int:segment_id>/fix-positions/', views.fix_symbol_positions_endpoint, name='fix-symbol-positions'),

    # Process B specific endpoints
    path('segment/<int:segment_id>/generate-bounding-boxes/', views.generate_bounding_boxes, name='generate-bounding-boxes'),
    path('segment/<int:segment_id>/get-bounding-boxes/', views.get_bounding_boxes, name='get-bounding-boxes'),
    path('segment/<int:segment_id>/process-bounding-box/<int:box_id>/', views.process_bounding_box, name='process-bounding-box'),
    path('segment/<int:segment_id>/process-multiple-bounding-boxes-parallel/', views.process_multiple_bounding_boxes_parallel, name='process-multiple-bounding-boxes-parallel'),
    path('segment/<int:segment_id>/confirm-symbol/', views.confirm_symbol_from_bounding_box, name='confirm-symbol-from-bounding-box'),

    # Logs
    path('log/<int:drawing_id>/', views.get_analysis_logs, name='get-analysis-logs'),

    # Direct bubble generator
    path('generate-bubbles-direct/<int:drawing_id>/', generate_bubbles_direct, name='generate-bubbles-direct'),

    # Download bubbles
    path('download-bubbles/<int:drawing_id>/', download_bubbles, name='download-bubbles'),

    # Overlay bubbles
    path('overlay-bubbles/<int:drawing_id>/', overlay_bubbles, name='overlay-bubbles'),
]
