# Generated by Django 5.2 on 2025-06-02 20:24

import api.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Drawing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=api.models.get_drawing_upload_path)),
                ('original_filename', models.CharField(max_length=255)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed', models.<PERSON>oleanField(default=False)),
                ('finalized', models.<PERSON>oleanField(default=False)),
                ('is_template', models.BooleanField(default=False)),
                ('final_image', models.FileField(blank=True, null=True, upload_to='final_outputs')),
                ('finalized_image', models.FileField(blank=True, null=True, upload_to='final_outputs')),
                ('measurements_excel', models.FileField(blank=True, null=True, upload_to='final_outputs')),
                ('summary_file', models.FileField(blank=True, null=True, upload_to='final_outputs')),
                ('process_type', models.CharField(choices=[('A', 'Process A - Automatic AI Analysis'), ('B', 'Process B - Manual Symbol Detection')], default='A', max_length=1)),
            ],
        ),
        migrations.CreateModel(
            name='CageType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cage_types', to='api.company')),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('message', models.TextField()),
                ('drawing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='api.drawing')),
            ],
            options={
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Segment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('segment_number', models.IntegerField()),
                ('image', models.ImageField(upload_to=api.models.get_segment_upload_path)),
                ('grid_image', models.ImageField(blank=True, null=True, upload_to=api.models.get_segment_upload_path)),
                ('annotated_image', models.ImageField(blank=True, null=True, upload_to=api.models.get_segment_upload_path)),
                ('marked_image', models.ImageField(blank=True, null=True, upload_to=api.models.get_segment_upload_path)),
                ('easyocr_debug_image', models.ImageField(blank=True, null=True, upload_to=api.models.get_segment_upload_path)),
                ('segment_x_offset', models.IntegerField(default=0)),
                ('segment_y_offset', models.IntegerField(default=0)),
                ('drawing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='segments', to='api.drawing')),
            ],
            options={
                'ordering': ['segment_number'],
                'unique_together': {('drawing', 'segment_number')},
            },
        ),
        migrations.CreateModel(
            name='Symbol',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_marked', models.BooleanField(default=False)),
                ('symbol_type', models.CharField(choices=[('diameter', 'Diameter'), ('radius', 'Radius'), ('tolerance', 'Tolerance'), ('degree', 'Degree'), ('boxed', 'Boxed Value'), ('area', 'Area'), ('cross-section', 'Cross-Section'), ('dimension', 'Dimension'), ('surface_roughness', 'Surface Roughness'), ('thread', 'Thread Specification'), ('material', 'Material Specification'), ('text', 'Text'), ('other', 'Other')], default='other', max_length=20)),
                ('value', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('x_coordinate', models.IntegerField()),
                ('y_coordinate', models.IntegerField()),
                ('normalized_x', models.FloatField(default=0.0, help_text='X coordinate normalized to image width (0.0 to 1.0)')),
                ('normalized_y', models.FloatField(default=0.0, help_text='Y coordinate normalized to image height (0.0 to 1.0)')),
                ('display_order', models.IntegerField(default=0)),
                ('position_source', models.CharField(choices=[('gpt', 'GPT-4.1'), ('user', 'User Placed'), ('easyocr', 'EasyOCR'), ('gpt_vision', 'Gpt_vision'), ('distributed', 'Distributed')], default='gpt', max_length=15)),
                ('orientation', models.CharField(choices=[('normal', 'Normal'), ('rotated', 'Rotated 90°')], default='normal', max_length=10)),
                ('segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='symbols', to='api.segment')),
            ],
            options={
                'ordering': ['display_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='EasyOCRDetection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('detected_text', models.CharField(max_length=200)),
                ('confidence', models.FloatField()),
                ('center_x', models.IntegerField()),
                ('center_y', models.IntegerField()),
                ('normalized_x', models.FloatField(default=0.0, help_text='X coordinate normalized to image width (0.0 to 1.0)')),
                ('normalized_y', models.FloatField(default=0.0, help_text='Y coordinate normalized to image height (0.0 to 1.0)')),
                ('orientation', models.CharField(choices=[('normal', 'Normal'), ('rotated', 'Rotated 90°')], default='normal', max_length=10)),
                ('similarity_score', models.FloatField(default=0.0)),
                ('is_matched', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='easyocr_detections', to='api.segment')),
                ('matched_symbol', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='easyocr_detections', to='api.symbol')),
            ],
            options={
                'ordering': ['-confidence', 'detected_text'],
            },
        ),
        migrations.CreateModel(
            name='BoundingBox',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('x', models.IntegerField(help_text='Top-left X coordinate')),
                ('y', models.IntegerField(help_text='Top-left Y coordinate')),
                ('width', models.IntegerField(help_text='Width of bounding box')),
                ('height', models.IntegerField(help_text='Height of bounding box')),
                ('center_x', models.IntegerField(help_text='Center X coordinate')),
                ('center_y', models.IntegerField(help_text='Center Y coordinate')),
                ('confidence', models.FloatField(default=0.0)),
                ('detected_text', models.CharField(blank=True, max_length=200)),
                ('processed', models.BooleanField(default=False, help_text='Whether this box has been processed by GPT Vision')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bounding_boxes', to='api.segment')),
                ('confirmed_symbol', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='source_bounding_box', to='api.symbol')),
            ],
            options={
                'ordering': ['x', 'y'],
            },
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('cage_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='templates', to='api.cagetype')),
            ],
        ),
        migrations.AddField(
            model_name='drawing',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='drawings', to='api.template'),
        ),
        migrations.CreateModel(
            name='SegmentSimilarity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('similarity_score', models.FloatField()),
                ('user_confirmed', models.BooleanField(default=False)),
                ('user_rating', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='similarities', to='api.segment')),
                ('similar_segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='similar_to', to='api.segment')),
            ],
            options={
                'ordering': ['-similarity_score'],
                'unique_together': {('segment', 'similar_segment')},
            },
        ),
    ]
