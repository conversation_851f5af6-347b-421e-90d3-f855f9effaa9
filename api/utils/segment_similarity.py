import cv2
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import logging
from django.conf import settings
import os

logger = logging.getLogger(__name__)

def calculate_segment_similarity(segment1_path, segment2_path):
    """
    Calculate visual similarity between two segment images.
    Returns a similarity score between 0 and 1.
    """
    try:
        # Load images
        img1 = cv2.imread(segment1_path)
        img2 = cv2.imread(segment2_path)

        if img1 is None or img2 is None:
            logger.error(f"Failed to load images: {segment1_path} or {segment2_path}")
            return 0.0

        # Resize if dimensions don't match
        if img1.shape[:2] != img2.shape[:2]:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # Calculate histograms
        hist1 = cv2.calcHist([img1], [0, 1, 2], None, [8, 8, 8], [0, 256, 0, 256, 0, 256])
        hist1 = cv2.normalize(hist1, hist1).flatten()

        hist2 = cv2.calcHist([img2], [0, 1, 2], None, [8, 8, 8], [0, 256, 0, 256, 0, 256])
        hist2 = cv2.normalize(hist2, hist2).flatten()

        # Calculate similarity
        similarity = cosine_similarity([hist1], [hist2])[0][0]

        return float(similarity)
    except Exception as e:
        logger.error(f"Error calculating segment similarity: {e}")
        return 0.0

def find_similar_segments(segment, max_results=5, min_similarity=0.7):
    """
    Find segments from other drawings that are visually similar to the given segment.
    Returns a list of (segment, similarity_score) tuples ordered by similarity.
    Only returns segments that have symbols.
    """
    from ..models import Segment, Drawing, SegmentSimilarity, Symbol

    try:
        # Get all finalized drawings (excluding the current one)
        finalized_drawings = Drawing.objects.filter(
            finalized=True
        ).exclude(id=segment.drawing.id)

        if not finalized_drawings.exists():
            logger.info(f"No finalized drawings found to compare with segment {segment.id}")
            return []

        # Get segments with the same segment number from finalized drawings that have symbols
        # First, get segments with symbols
        segments_with_symbols = Segment.objects.filter(
            symbols__isnull=False,
            symbols__is_marked=True  # Only consider segments with marked symbols
        ).distinct()

        # Then filter for segments from finalized drawings with the same segment number
        candidate_segments = segments_with_symbols.filter(
            drawing__in=finalized_drawings,
            segment_number=segment.segment_number
        )

        if not candidate_segments.exists():
            logger.info(f"No candidate segments with symbols found with segment number {segment.segment_number}")
            return []

        # Get the path to the current segment image
        segment_path = os.path.join(settings.MEDIA_ROOT, str(segment.image))
        if not os.path.exists(segment_path):
            logger.error(f"Segment image not found: {segment_path}")
            return []

        # Calculate similarity for each candidate
        similarities = []

        for candidate in candidate_segments:
            try:
                # Get the path to the candidate segment image
                candidate_path = os.path.join(settings.MEDIA_ROOT, str(candidate.image))
                if not os.path.exists(candidate_path):
                    logger.warning(f"Candidate segment image not found: {candidate_path}")
                    continue

                # Calculate similarity
                similarity_score = calculate_segment_similarity(segment_path, candidate_path)

                # Only include if similarity is above threshold
                if similarity_score >= min_similarity:
                    # Create or update similarity record
                    similarity_obj, _ = SegmentSimilarity.objects.update_or_create(
                        segment=segment,
                        similar_segment=candidate,
                        defaults={'similarity_score': similarity_score}
                    )

                    similarities.append((candidate, similarity_score, similarity_obj))
            except Exception as e:
                logger.error(f"Error processing candidate segment {candidate.id}: {e}")
                continue

        # Sort by similarity (highest first)
        similarities.sort(key=lambda x: x[1], reverse=True)

        # Return top results
        return similarities[:max_results]
    except Exception as e:
        logger.error(f"Error finding similar segments: {e}")
        return []
