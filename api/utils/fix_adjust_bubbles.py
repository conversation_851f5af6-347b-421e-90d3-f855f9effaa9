"""
<PERSON><PERSON><PERSON> to fix the adjust_final_bubbles function.
"""

import os
import sys
import django
import json
import traceback
import time
from PIL import Image, ImageDraw, ImageFont

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from django.conf import settings
from api.models import Drawing, Segment, Symbol
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404

def fixed_adjust_bubbles(drawing_id, bubble_size=60):
    """
    Fixed implementation of the adjust_final_bubbles function.
    """
    try:
        # Get the drawing
        drawing = Drawing.objects.get(id=drawing_id)
        print(f"Found drawing: {drawing}")
        print(f"Original image path: {drawing.image.path}")
        print(f"Current finalized_image: {drawing.finalized_image}")
        
        # Check if the original image exists
        if not os.path.exists(drawing.image.path):
            print(f"ERROR: Original image file does not exist at {drawing.image.path}")
            return None
        
        # Open the image with PIL
        img = Image.open(drawing.image.path)
        print(f"Opened image: {img.size}, mode: {img.mode}")
        
        # Convert to RGB mode if needed
        if img.mode != 'RGB':
            print(f"Converting image from {img.mode} to RGB")
            img = img.convert('RGB')
        
        draw = ImageDraw.Draw(img)
        
        # Calculate font size based on bubble size
        font_size = int(bubble_size * 0.7)  # Font size proportional to bubble size
        print(f"Using font size: {font_size}")
        
        # Try to load a font, fall back to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
            print("Using Arial font")
        except IOError:
            try:
                # Try system fonts
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                print("Using Helvetica font")
            except IOError:
                font = ImageFont.load_default()
                print("Using default font")
        
        # Get all segments for this drawing
        segments = Segment.objects.filter(drawing=drawing)
        print(f"Found {segments.count()} segments")
        
        # Define the segment order (2, 1, 3, 4, ...)
        segment_order = []
        
        # First, try to find segments with numbers 2, 1, 3, 4
        for segment_number in [2, 1, 3, 4]:
            segment = segments.filter(segment_number=segment_number).first()
            if segment:
                segment_order.append(segment)
                print(f"Added segment {segment_number} to order")
        
        # Add any remaining segments in their natural order
        for segment in segments:
            if segment not in segment_order:
                segment_order.append(segment)
                print(f"Added additional segment {segment.segment_number} to order")
        
        # Initialize a counter for symbol numbering across all segments
        symbol_counter = 1
        total_symbols = 0
        
        # Process each segment in the specified order (2, 1, 3, 4)
        for segment in segment_order:
            # Get all marked symbols for this segment
            symbols = Symbol.objects.filter(segment=segment, is_marked=True).order_by('display_order', 'id')
            print(f"Segment {segment.segment_number}: Found {symbols.count()} marked symbols")
            total_symbols += symbols.count()
            
            # Calculate the offset for this segment
            x_offset = segment.segment_x_offset or 0
            y_offset = segment.segment_y_offset or 0
            print(f"Segment {segment.segment_number} offset: ({x_offset}, {y_offset})")
            
            # Draw each symbol with its sequential number
            for symbol in symbols:
                # Calculate the absolute position in the full drawing
                abs_x = x_offset + symbol.x_coordinate
                abs_y = y_offset + symbol.y_coordinate
                print(f"Symbol {symbol_counter}: type={symbol.symbol_type}, value={symbol.value}, pos=({abs_x}, {abs_y})")
                
                # Draw a circle for the symbol - using the specified bubble size
                circle_radius = int(bubble_size / 2)  # Radius is half the diameter
                print(f"Using circle radius: {circle_radius}")
                
                # Draw outer circle in blue
                draw.ellipse(
                    [(abs_x - circle_radius, abs_y - circle_radius),
                     (abs_x + circle_radius, abs_y + circle_radius)],
                    fill="blue",
                    outline="blue",
                    width=max(1, int(bubble_size * 0.05))  # Width proportional to bubble size
                )
                
                # Draw inner circle in blue with white outline for better visibility
                inner_radius = int(circle_radius * 0.9)
                draw.ellipse(
                    [(abs_x - inner_radius, abs_y - inner_radius),
                     (abs_x + inner_radius, abs_y + inner_radius)],
                    fill="blue",
                    outline="white",
                    width=max(1, int(bubble_size * 0.02))  # Width proportional to bubble size
                )
                
                # Draw the symbol number
                try:
                    # For newer PIL versions
                    left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                    text_width = right - left
                    text_height = bottom - top
                    print(f"Text dimensions: {text_width}x{text_height}")
                except AttributeError:
                    # Fallback for older PIL versions
                    try:
                        text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                        print(f"Text dimensions (fallback): {text_width}x{text_height}")
                    except:
                        text_width, text_height = font_size // 2, font_size // 2  # Default fallback values
                        print(f"Text dimensions (default): {text_width}x{text_height}")
                
                # Draw white text with symbol number
                draw.text(
                    (abs_x - text_width/2, abs_y - text_height/2),
                    str(symbol_counter),
                    fill="white",
                    font=font
                )
                
                # Increment the counter
                symbol_counter += 1
        
        print(f"Total symbols drawn: {total_symbols}")
        
        # Save the finalized image with a timestamp to avoid caching issues
        timestamp = int(time.time())
        output_filename = f"finalized_{drawing.id}_{timestamp}.png"
        output_path = os.path.join(settings.MEDIA_ROOT, output_filename)
        print(f"Saving image to {output_path}")
        img.save(output_path, format='PNG')
        print(f"Image saved successfully")
        
        # Update the drawing record with the new image
        old_finalized_image = drawing.finalized_image
        drawing.finalized_image = output_filename
        drawing.save()
        print(f"Updated drawing.finalized_image from {old_finalized_image} to {output_filename}")
        
        # Return the output filename
        return output_filename
    
    except Exception as e:
        print(f"ERROR: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python fix_adjust_bubbles.py <drawing_id> [bubble_size]")
        sys.exit(1)
    
    drawing_id = int(sys.argv[1])
    bubble_size = int(sys.argv[2]) if len(sys.argv) > 2 else 60
    
    print(f"Running fixed adjust_bubbles for drawing {drawing_id} with bubble size {bubble_size}")
    output_filename = fixed_adjust_bubbles(drawing_id, bubble_size)
    
    if output_filename:
        print(f"Success! Fixed adjust_bubbles complete. Image saved as {output_filename}")
    else:
        print("Failed to run fixed adjust_bubbles")
