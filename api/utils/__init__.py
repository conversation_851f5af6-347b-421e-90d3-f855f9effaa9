import os
import base64
import logging
import requests
import json
import re
import time
from io import BytesIO
from PIL import Image
import numpy as np
from django.conf import settings

logger = logging.getLogger(__name__)

def encode_image_to_base64(image_path):
    """
    Encode an image file to base64 string.

    Args:
        image_path: Path to the image file

    Returns:
        Base64 encoded string of the image
    """
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        logger.error(f"Error encoding image to base64: {e}")
        return None

def call_gpt41_api(image_base64, segment_number=1, orientation='normal', batch_id=0, batch_size=2, batch_delay=1.0):
    """
    Call the GPT-4.1 API with an image and the specialized technical drawing prompt.

    Args:
        image_base64: Base64 encoded image
        segment_number: The segment number being analyzed
        orientation: The orientation of the image ('normal' or 'rotated')
        batch_id: The batch ID for rate limiting
        batch_size: The batch size for rate limiting
        batch_delay: The delay between batches in seconds

    Returns:
        The API response text and orientation
    """
    # Get API settings from Django settings
    endpoint = settings.GPT41_ENDPOINT
    api_key = settings.GPT41_API_KEY
    model = settings.GPT41_MODEL

    # Set up headers based on whether we're using Azure or OpenAI directly
    if 'azure' in endpoint.lower():
        # Azure OpenAI headers
        headers = {
            "Content-Type": "application/json",
            "api-key": api_key
        }
    else:
        # Standard OpenAI headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

    # The specialized prompt for technical drawing analysis
    orientation_note = "rotated 90 degrees clockwise" if orientation == 'rotated' else "normal orientation"
    prompt = f"""
    You are a precise vision assistant specialized in analyzing technical drawings with coordinate grids.
    Your task is to identify specific engineering symbols AND determine their location.

    Look at this image which contains a small section (segment #{segment_number}) of a technical drawing in {orientation_note}.

    1. Scan the image for the following engineering symbols and their associated FULL measurement values:
       - Diameter (prefixed with Ø or ⌀)
       - Radius (prefixed with R, followed by a number)
       - Tolerance (containing ±)
       - Degree (ending with °)
       - Boxed Numbers (enclosed in |...|)
       - Area measurements (containing mm², cm², or similar area units)
       - Cross-section values (containing "cross-section" or similar terms with numerical values)
       - Dimensions with tolerance values
       - Nominal dimensions or measurements with units (mm, m, etc.)
       - Don't read single digit numbers
       - There would be no commas (,) only decimals (.)

    2. CRITICAL INSTRUCTIONS FOR COMBINED VALUES:
       - ALWAYS INCLUDE THE FULL VALUE WITH TOLERANCES in a single symbol
       - When you see a dimension with a tolerance stacked above or below it (like 0.30 with +0.100 above it), COMBINE them as ONE value (e.g., "0.30 +0.100")
       - When you see a diameter with tolerance like "Ø1.95 ±0.05", report the ENTIRE string as ONE diameter value
       - When you see a dimension with tolerance like "4.519 ±0.075", report the ENTIRE string as ONE value
       - When you see "Nominal cross-section 6.517 mm²", report the ENTIRE string including units
       - NEVER separate a measurement from its tolerance or units - they belong together
       - Example: "Ø1.95 ±0.05" should be ONE symbol of type "diameter" with value "Ø1.95 ±0.05"
       - Example: "4.519 ±0.075" should be ONE symbol of type "tolerance" with value "4.519 ±0.075"
       - Example: "6.517 mm²" should be ONE symbol of type "text" with value "6.517 mm²"
       - Example: When "0.30" has "+0.100" stacked above it, report as ONE symbol of type "tolerance" with value "0.30 +0.100"
       - If you see a value like "Ø1.95" and nearby "±0.05", combine them as "Ø1.95 ±0.05"

    3. For each symbol you find, provide:
       - The symbol type (diameter, radius, tolerance, degree, boxed, text)
       - The EXACT and COMPLETE value (including ALL parts - main value, tolerance, and units if present)

    Important: You MUST identify any engineering symbols present in the image, even if they're partial or at the edges.
    Look carefully for dimension values, tolerances, diameters, and other technical measurements.
    Pay special attention to tolerance values that might be stacked above or below main dimension values.

    RESPONSE FORMAT: Please provide your response in JSON format with a 'symbols' array containing objects with 'symbol_type' and 'value' properties.
    """

    # Construct the payload
    # For Azure OpenAI, we don't need to include the model in the payload
    # as it's already in the endpoint URL
    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 4000
    }

    # Only add response_format for non-Azure endpoints
    if 'azure' not in endpoint.lower():
        payload["response_format"] = {"type": "json_object"}

    # Log the API request details
    logger.info(f"Calling GPT-4.1 API at endpoint: {endpoint} for segment {segment_number} in {orientation} orientation (batch {batch_id+1}/{batch_size})")

    # Add delay if not the first batch
    if batch_id > 0:
        import time
        logger.info(f"Waiting {batch_delay} seconds before sending next batch request...")
        time.sleep(batch_delay)

    try:
        # Make the API call
        logger.info("Sending request to GPT-4.1 API...")
        response = requests.post(endpoint, json=payload, headers=headers, timeout=60.0)

        # Log the response status
        logger.info(f"GPT-4.1 API response status code: {response.status_code}")

        # Check for HTTP errors
        if response.status_code != 200:
            logger.error(f"GPT-4.1 API error: {response.status_code} - {response.text}")
            return None, orientation

        # Parse the response
        try:
            response_data = response.json()
            logger.info("Successfully parsed JSON response")

            # Extract the content
            if 'choices' in response_data and len(response_data['choices']) > 0:
                content = response_data['choices'][0]['message']['content']
                logger.info(f"Successfully extracted content from response: {content[:100]}...")
                return content, orientation
            else:
                logger.error(f"Unexpected API response format: {response_data}")
                return None, orientation
        except json.JSONDecodeError as json_err:
            logger.error(f"Failed to parse JSON response: {json_err}")
            logger.error(f"Response text: {response.text[:500]}")
            return None, orientation
    except requests.exceptions.RequestException as req_err:
        logger.error(f"Request error calling GPT-4.1 API: {req_err}")
        return None, orientation
    except Exception as e:
        logger.error(f"Unexpected error calling GPT-4.1 API: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None, orientation

def parse_gpt41_response(response_text, orientation='normal'):
    """
    Parse the GPT-4.1 API response into a structured format.
    Handles responses that are wrapped in code blocks.

    Args:
        response_text: The JSON response from the API
        orientation: The orientation of the image ('normal' or 'rotated')

    Returns:
        List of symbol dictionaries
    """
    try:
        # If we have a valid response, parse it
        if not response_text:
            logger.warning("No response text provided to parse_gpt41_response")
            return []

        # Check if the response is wrapped in code blocks and extract the JSON
        if "```json" in response_text or "```" in response_text:
            # Extract content between code blocks
            code_block_pattern = r"```(?:json)?(.*?)```"
            matches = re.findall(code_block_pattern, response_text, re.DOTALL)
            if matches:
                # Use the first match
                response_text = matches[0].strip()
                logger.info(f"Extracted JSON from code block: {response_text[:100]}...")

        # Parse the JSON response
        try:
            response_data = json.loads(response_text)
            logger.info(f"Successfully parsed JSON: {str(response_data)[:100]}...")
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Response text: {response_text[:500]}")
            return []

        # Extract symbols from the response
        symbols = []

        if 'symbols' in response_data:
            for symbol in response_data['symbols']:
                # Get the symbol type, defaulting to 'other' if not found
                symbol_type = symbol.get('symbol_type', 'other').lower()

                # Map 'text' type to 'other' for consistency with our database model
                if symbol_type == 'text':
                    symbol_type = 'other'

                # Get the value
                value = symbol.get('value', '')

                # Get coordinates from symbol if provided, otherwise use default position
                x = symbol.get('x_coordinate', symbol.get('x', 100))
                y = symbol.get('y_coordinate', symbol.get('y', 100))

                # If coordinates are still not valid, use a default position
                # This should be updated by EasyOCR or GPT Vision later
                if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
                    x = 100
                    y = 100

                # Add to symbols list with orientation information
                symbols.append({
                    'type': symbol_type,
                    'value': value,
                    'x': int(x),
                    'y': int(y),
                    'marked': True,  # Mark all detected symbols as significant
                    'orientation': orientation  # Add orientation information
                })

            logger.info(f"Parsed {len(symbols)} symbols from GPT-4.1 response in {orientation} orientation")
            return symbols
        else:
            logger.warning("No 'symbols' key found in GPT-4.1 response")
            return []

    except Exception as e:
        logger.error(f"Error in parse_gpt41_response: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def segment_image(image_path, num_segments=4):
    """
    Segment an image into equal parts (legacy function - no overlap).

    Args:
        image_path: Path to the image file
        num_segments: Number of segments (must be a perfect square)

    Returns:
        List of dictionaries with segment information
    """
    try:
        # Open the image
        img = Image.open(image_path)
        width, height = img.size

        # Calculate grid dimensions
        grid_size = int(np.sqrt(num_segments))
        segment_width = width // grid_size
        segment_height = height // grid_size

        segments = []
        segment_number = 1

        # Create segments
        for y in range(grid_size):
            for x in range(grid_size):
                # Calculate segment coordinates
                left = x * segment_width
                upper = y * segment_height
                right = left + segment_width
                lower = upper + segment_height

                # Crop the segment
                segment_img = img.crop((left, upper, right, lower))

                # Create directory for temporary files if it doesn't exist
                import os
                temp_dir = os.path.join(os.path.dirname(image_path), 'temp_segments')
                os.makedirs(temp_dir, exist_ok=True)

                # Save segment to a temporary file
                segment_path = os.path.join(temp_dir, f"temp_segment_{segment_number}.png")
                segment_img.save(segment_path)

                # Add segment info to list
                segments.append({
                    'number': segment_number,
                    'x_offset': left,
                    'y_offset': upper,
                    'width': segment_width,
                    'height': segment_height,
                    'path': segment_path
                })

                segment_number += 1

        return segments
    except Exception as e:
        logger.error(f"Error segmenting image: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []


def segment_image_with_overlap(image_path, num_segments=4, overlap_percent=10):
    """
    Segment an image into equal parts with overlap for better symbol detection.

    Args:
        image_path: Path to the image file
        num_segments: Number of segments (must be a perfect square)
        overlap_percent: Percentage of overlap between segments (default: 10%)

    Returns:
        List of dictionaries with segment information
    """
    try:
        # Open the image
        img = Image.open(image_path)
        width, height = img.size

        # Calculate grid dimensions
        grid_size = int(np.sqrt(num_segments))

        # Calculate base segment dimensions
        base_segment_width = width // grid_size
        base_segment_height = height // grid_size

        # Calculate overlap in pixels
        overlap_x = int(base_segment_width * (overlap_percent / 100))
        overlap_y = int(base_segment_height * (overlap_percent / 100))

        # Calculate step size (distance between segment starts)
        step_x = base_segment_width - overlap_x
        step_y = base_segment_height - overlap_y

        # Calculate actual segment size (with overlap)
        segment_width = base_segment_width + overlap_x
        segment_height = base_segment_height + overlap_y

        segments = []
        segment_number = 1

        print(f"🔧 Segmenting with {overlap_percent}% overlap:")
        print(f"   Image size: {width}x{height}")
        print(f"   Base segment: {base_segment_width}x{base_segment_height}")
        print(f"   Overlap: {overlap_x}x{overlap_y} pixels")
        print(f"   Step size: {step_x}x{step_y}")
        print(f"   Final segment size: {segment_width}x{segment_height}")

        # Create segments with overlap
        for y in range(grid_size):
            for x in range(grid_size):
                # Calculate segment coordinates with overlap
                left = x * step_x
                upper = y * step_y
                right = min(left + segment_width, width)
                lower = min(upper + segment_height, height)

                # Crop the segment
                segment_img = img.crop((left, upper, right, lower))

                # Create directory for temporary files if it doesn't exist
                import os
                temp_dir = os.path.join(os.path.dirname(image_path), 'temp_segments')
                os.makedirs(temp_dir, exist_ok=True)

                # Save segment to a temporary file
                segment_path = os.path.join(temp_dir, f"temp_segment_{segment_number}.png")
                segment_img.save(segment_path)

                print(f"   Segment {segment_number}: ({left},{upper}) → ({right},{lower}) = {right-left}x{lower-upper}")

                # Add segment info to list
                segments.append({
                    'number': segment_number,
                    'x_offset': left,
                    'y_offset': upper,
                    'width': right - left,
                    'height': lower - upper,
                    'path': segment_path,
                    'overlap_info': {
                        'overlap_percent': overlap_percent,
                        'overlap_x': overlap_x,
                        'overlap_y': overlap_y,
                        'step_x': step_x,
                        'step_y': step_y
                    }
                })

                segment_number += 1

        return segments
    except Exception as e:
        logger.error(f"Error segmenting image with overlap: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []


def call_llama4_api(image_base64, segment_number=1, orientation='normal', batch_id=0, batch_size=2, batch_delay=1.0):
    """
    Call the Llama-4 Maverick API with an image and the specialized technical drawing prompt.

    Args:
        image_base64: Base64 encoded image
        segment_number: The segment number being analyzed
        orientation: The orientation of the image ('normal' or 'rotated')
        batch_id: The batch ID for rate limiting
        batch_size: The batch size for rate limiting
        batch_delay: The delay between batches in seconds

    Returns:
        The API response text and orientation
    """
    # Get API settings from Django settings
    endpoint = settings.LLAMA4_ENDPOINT
    api_key = settings.LLAMA4_API_KEY
    model = settings.LLAMA4_MODEL

    # Set up headers for Azure AI Hub
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    # Create the superior combined prompt for technical drawing analysis
    prompt = f"""You are a precise vision assistant specialized in analyzing technical engineering drawings with coordinate grids. Your task is to identify specific engineering symbols AND determine their location with extreme accuracy.

Look at this technical drawing segment #{segment_number} which contains a small section of a technical drawing.

1. **SCAN FOR ENGINEERING SYMBOLS AND FULL MEASUREMENT VALUES:**
   - **Diameter measurements** (prefixed with Ø or ⌀)
   - **Radius measurements** (prefixed with R, followed by a number)
   - **Tolerance values** (containing ± symbols with values)
   - **Angles** (numbers followed by ° symbol)
   - **Boxed Numbers** (enclosed in |...|)
   - **Area measurements** (containing mm², cm², or similar area units)
   - **Cross-section values** (containing "cross-section" or similar terms with numerical values)
   - **Dimensions with tolerance values**
   - **Nominal dimensions** or measurements with units (mm, m, etc.)
   - **Surface roughness** (Ra, Rz values)
   - **Geometric tolerances** (symbols in boxes)
   - **Thread specifications** (M followed by numbers)
   - **Material specifications** or part numbers
   - **Linear dimensions** (numbers with or without units)
   - **Any other numerical values or engineering annotations**

2. **CRITICAL INSTRUCTIONS FOR COMBINED VALUES:**
   - **ALWAYS INCLUDE THE FULL VALUE WITH TOLERANCES** in a single symbol
   - When you see a dimension with a tolerance stacked above or below it (like 0.30 with +0.100 above it), **COMBINE them as ONE value** (e.g., "0.30 +0.100")
   - When you see a diameter with tolerance like "Ø1.95 ±0.05", report the **ENTIRE string as ONE diameter value**
   - When you see a dimension with tolerance like "4.519 ±0.075", report the **ENTIRE string as ONE value**
   - When you see "Nominal cross-section 6.517 mm²", report the **ENTIRE string including units**
   - **NEVER separate a measurement from its tolerance or units** - they belong together
   - If you see a value like "Ø1.95" and nearby "±0.05", **combine them as "Ø1.95 ±0.05"**

3. **IMPORTANT FILTERING RULES:**
   - **Don't read single digit numbers** (like 1, 2, 3, etc.)
   - **There would be no commas (,)** only decimals (.)
   - **Include ALL visible engineering values**, even if they seem minor
   - **Look carefully for small text, symbols, and annotations** that might be easily missed

4. **EXAMPLES OF PROPER COMBINATION:**
   - "Ø1.95 ±0.05" should be ONE symbol of type "diameter" with value "Ø1.95 ±0.05"
   - "4.519 ±0.075" should be ONE symbol of type "tolerance" with value "4.519 ±0.075"
   - "6.517 mm²" should be ONE symbol of type "area" with value "6.517 mm²"
   - When "0.30" has "+0.100" stacked above it, report as ONE symbol of type "tolerance" with value "0.30 +0.100"

For each identified value, provide:
- The **exact text/value as it appears** (with full tolerances and units combined)
- The **type of measurement** (diameter, radius, angle, tolerance, dimension, area, surface_roughness, thread, material, other)
- A **brief description** of what it represents

Return your response in JSON format with this structure:
{{
    "symbols": [
        {{
            "value": "exact text as shown with full tolerances and units",
            "type": "diameter|radius|angle|tolerance|dimension|area|surface_roughness|thread|material|other",
            "description": "brief description of the engineering measurement"
        }}
    ]
}}

**Be extremely thorough and precise.** This is critical for engineering accuracy.

Image orientation: {orientation}"""

    # Rate limiting: add delay for batches
    if batch_id > 0 and batch_id % batch_size == 0:
        time.sleep(batch_delay)
        logger.info(f"Rate limiting: Waiting {batch_delay}s after batch {batch_id // batch_size}")

    # Construct the payload
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 4000
    }

    logger.info(f"Calling Llama-4 Maverick API for segment {segment_number} (orientation: {orientation})")

    try:
        # Make the API call
        logger.info("Sending request to Llama-4 Maverick API...")
        response = requests.post(endpoint, json=payload, headers=headers, timeout=60.0)

        # Log the response status
        logger.info(f"Llama-4 Maverick API response status code: {response.status_code}")

        # Check for HTTP errors
        if response.status_code != 200:
            logger.error(f"Llama-4 Maverick API error: {response.status_code} - {response.text}")
            return None, orientation

        # Parse the response
        try:
            response_data = response.json()
            logger.info("Successfully parsed JSON response")

            # Extract the content from the response
            if 'choices' in response_data and len(response_data['choices']) > 0:
                content = response_data['choices'][0]['message']['content']
                logger.info(f"Llama-4 Maverick API response content length: {len(content)}")
                return content, orientation
            else:
                logger.error("No choices found in Llama-4 Maverick API response")
                return None, orientation

        except json.JSONDecodeError as json_error:
            logger.error(f"Failed to parse Llama-4 Maverick API JSON response: {json_error}")
            logger.error(f"Raw response: {response.text}")
            return None, orientation

    except Exception as e:
        logger.error(f"Error calling Llama-4 Maverick API: {str(e)}")
        return None, orientation