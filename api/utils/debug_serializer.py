"""
Debug script for the serializer issue.
"""

import os
import sys
import django
import json

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from django.conf import settings
from api.models import Drawing
from api.serializers import DrawingSerializer

def debug_serializer(drawing_id):
    """
    Debug the serializer for a drawing.
    """
    try:
        # Get the drawing
        drawing = Drawing.objects.get(id=drawing_id)
        print(f"Found drawing: {drawing}")
        
        # Check the raw fields
        print("\nRaw fields:")
        print(f"image: {drawing.image}")
        print(f"final_image: {drawing.final_image}")
        print(f"finalized_image: {drawing.finalized_image}")
        
        # Serialize the drawing
        serializer = DrawingSerializer(drawing)
        data = serializer.data
        
        # Print the serialized data
        print("\nSerialized data:")
        print(json.dumps(data, indent=2))
        
        # Check specific fields
        print("\nSpecific fields:")
        print(f"image URL: {data.get('image')}")
        print(f"final_image URL: {data.get('final_image')}")
        print(f"finalized_image URL: {data.get('finalized_image')}")
        
        return data
    
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python debug_serializer.py <drawing_id>")
        sys.exit(1)
    
    drawing_id = int(sys.argv[1])
    
    print(f"Debugging serializer for drawing {drawing_id}")
    data = debug_serializer(drawing_id)
    
    if data:
        print("\nSuccess! Serializer debug complete.")
    else:
        print("\nFailed to debug serializer.")
