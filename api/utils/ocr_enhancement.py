"""
Utility functions to enhance OCR detection for engineering drawings.
This module provides functions to improve the detection of complex values
like measurements with tolerances, diameters, etc.
"""

import re
import math
import logging
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)

def normalize_value(value):
    """
    Normalize a value for comparison by removing spaces, parentheses, and standardizing formats.

    Args:
        value: String value to normalize

    Returns:
        string: Normalized value for comparison
    """
    import re

    # Remove parentheses and brackets
    normalized = value.lower()
    normalized = re.sub(r'[\(\)\[\]\{\}]', '', normalized)

    # Remove all spaces
    normalized = normalized.replace(" ", "")

    # Replace common variations
    normalized = normalized.replace("±", "+/-")

    # Handle diameter symbol variations
    normalized = normalized.replace("ø", "∅")  # Standardize to ∅

    # Handle plus-minus symbol variations - normalize all to ±
    normalized = normalized.replace("+/-", "±")
    normalized = normalized.replace("+-", "±")
    # Handle single + as ± for tolerance values
    # Pattern: number+number should become number±number
    single_plus_pattern = r'(\d+\.?\d*)([+])(\d+\.?\d*)'
    normalized = re.sub(single_plus_pattern, r'\1±\3', normalized)

    # Special handling for values with trailing zeros in decimal places
    # e.g., "0.10" should match with "0.1"
    def normalize_number(match):
        num = match.group(0)
        if '.' in num:
            return num.rstrip('0').rstrip('.') if '.' in num else num
        return num

    normalized = re.sub(r'\d+\.\d+', normalize_number, normalized)

    # Handle specific case of "R0.2+0.1 0" vs "R0.2 +0.10"
    # Look for patterns like "r0.2+0.10" where the trailing 0 might be separate
    pattern = r'([r]?\d+\.\d+)([+\-]\d+\.\d+)(\d)?'
    match = re.match(pattern, normalized)

    if match:
        # Extract the components
        base = match.group(1)
        tolerance = match.group(2)
        trailing_digit = match.group(3)

        # If there's a trailing digit, it might be part of the tolerance
        if trailing_digit:
            # Try to combine it with the tolerance
            tolerance_without_zeros = tolerance.rstrip('0').rstrip('.')
            normalized = base + tolerance_without_zeros + trailing_digit

    # Handle tolerance values with space between + and -
    # e.g., "r+0.44 -0.22" vs "r+0.44-0.22"
    tolerance_pattern = r'([r]?\d*\.?\d*)([+]\d+\.?\d*)[\s-]+(\d+\.?\d*)'
    match = re.search(tolerance_pattern, normalized)
    if match:
        base = match.group(1)
        pos_tolerance = match.group(2)
        neg_tolerance = match.group(3)
        normalized = f"{base}{pos_tolerance}-{neg_tolerance}"

    return normalized

def calculate_distance(point1, point2):
    """
    Calculate Euclidean distance between two points.

    Args:
        point1: Tuple (x, y) for first point
        point2: Tuple (x, y) for second point

    Returns:
        float: Euclidean distance
    """
    return math.sqrt((point2[0] - point1[0])**2 + (point2[1] - point1[1])**2)

def merge_nearby_detections(detections, max_distance=50):
    """
    Merge OCR detections that are close to each other.

    Args:
        detections: List of EasyOCR detections, each being a tuple (bbox, text, score)
        max_distance: Maximum distance between centers of bounding boxes to consider merging

    Returns:
        list: List of merged detections
    """
    if not detections:
        return []

    # Extract center points and create a working copy of detections
    centers = []
    working_detections = []

    for detection in detections:
        bbox, text, score = detection

        # Calculate center point
        center_x = int((bbox[0][0] + bbox[2][0]) / 2)
        center_y = int((bbox[0][1] + bbox[2][1]) / 2)
        centers.append((center_x, center_y))

        # Add to working detections with center point
        working_detections.append({
            'bbox': bbox,
            'text': text,
            'score': score,
            'center': (center_x, center_y),
            'merged': False  # Flag to track if this detection has been merged
        })

    # Sort detections by x-coordinate (left to right)
    working_detections.sort(key=lambda d: d['center'][0])

    merged_results = []

    # Process each detection
    for i, detection in enumerate(working_detections):
        # Skip if already merged
        if detection['merged']:
            continue

        current_text = detection['text']
        current_score = detection['score']
        current_bbox = detection['bbox']
        current_center = detection['center']

        # Mark as merged
        detection['merged'] = True

        # Check nearby detections for potential merges
        nearby_indices = []
        for j, other in enumerate(working_detections):
            if i == j or other['merged']:
                continue

            # Calculate distance between centers
            distance = calculate_distance(current_center, other['center'])

            if distance <= max_distance:
                nearby_indices.append(j)

        # If no nearby detections, add the current one as is
        if not nearby_indices:
            merged_results.append((current_bbox, current_text, current_score))
            continue

        # Sort nearby detections by their position relative to current detection
        # For horizontal text: sort by x-coordinate
        # For potential vertical text: consider y-coordinate
        nearby_detections = [working_detections[j] for j in nearby_indices]

        # Determine if the text is likely horizontal or vertical
        x_spread = max(d['center'][0] for d in nearby_detections) - min(d['center'][0] for d in nearby_detections)
        y_spread = max(d['center'][1] for d in nearby_detections) - min(d['center'][1] for d in nearby_detections)

        # Sort by x if horizontal, by y if vertical
        if x_spread >= y_spread:  # Horizontal arrangement
            nearby_detections.sort(key=lambda d: d['center'][0])
        else:  # Vertical arrangement
            nearby_detections.sort(key=lambda d: d['center'][1])

        # Combine texts
        combined_text = current_text
        combined_score = current_score

        # Create a new bounding box that encompasses all merged detections
        min_x = min(current_bbox[0][0], current_bbox[3][0])
        min_y = min(current_bbox[0][1], current_bbox[1][1])
        max_x = max(current_bbox[1][0], current_bbox[2][0])
        max_y = max(current_bbox[2][1], current_bbox[3][1])

        for nearby in nearby_detections:
            # Mark as merged
            working_detections[working_detections.index(nearby)]['merged'] = True

            # Add space between horizontal text, newline for vertical
            if x_spread >= y_spread:
                combined_text += " " + nearby['text']
            else:
                combined_text += "\n" + nearby['text']

            # Average the confidence scores
            combined_score = (combined_score + nearby['score']) / 2

            # Update bounding box
            nearby_bbox = nearby['bbox']
            min_x = min(min_x, nearby_bbox[0][0], nearby_bbox[3][0])
            min_y = min(min_y, nearby_bbox[0][1], nearby_bbox[1][1])
            max_x = max(max_x, nearby_bbox[1][0], nearby_bbox[2][0])
            max_y = max(max_y, nearby_bbox[2][1], nearby_bbox[3][1])

        # Create new bounding box
        combined_bbox = [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]]

        # Apply pattern recognition to the combined text
        enhanced_text = apply_pattern_recognition(combined_text)

        # Add the merged detection
        merged_results.append((combined_bbox, enhanced_text, combined_score))

    return merged_results

def apply_pattern_recognition(text):
    """
    Apply pattern recognition to improve detection of engineering measurements.

    Args:
        text: String of detected text

    Returns:
        string: Enhanced text with corrected patterns
    """
    import logging
    logger = logging.getLogger(__name__)

    # Remove unwanted spaces
    text = text.strip()

    # Remove parentheses and brackets that might be around values
    # Store the original text for logging
    original_text = text
    text = re.sub(r'^\s*[\(\[\{](.+?)[\)\]\}]\s*$', r'\1', text)
    if text != original_text:
        logger.info(f"Removed surrounding brackets/parentheses: '{original_text}' -> '{text}'")

    # Normalize commas to periods in numeric values
    # This helps with OCR errors where periods are detected as commas
    text = re.sub(r'(\d),(\d)', r'\1.\2', text)

    # Pattern 1: Value with tolerance (e.g., "34.66 ± 0.15" or "34.66 ±0.15")
    tolerance_pattern = r'(\d+\.?\d*)\s*([±+\-])\s*(\d+\.?\d*)'
    match = re.search(tolerance_pattern, text)
    if match:
        value, symbol, tolerance = match.groups()
        # Standardize the symbol to + for positive tolerances
        if symbol == '±':
            return f"{value}±{tolerance}"
        else:
            return f"{value} {symbol}{tolerance}"

    # Pattern 2: Diameter symbol with value (e.g., "Ø 10.5" or "ø10.5")
    diameter_pattern = r'([Øø])\s*(\d+\.?\d*)'
    match = re.search(diameter_pattern, text)
    if match:
        symbol, value = match.groups()
        return f"Ø{value}"

    # Pattern 3: Degree symbol with value (e.g., "45 °" or "45°")
    degree_pattern = r'(\d+\.?\d*)\s*([°])'
    match = re.search(degree_pattern, text)
    if match:
        value, symbol = match.groups()
        return f"{value}°"

    # Pattern 4: Value with unit (e.g., "10 mm" or "10mm")
    unit_pattern = r'(\d+\.?\d*)\s*(mm|cm|m|in)'
    match = re.search(unit_pattern, text)
    if match:
        value, unit = match.groups()
        return f"{value}{unit}"

    # Pattern 5: Boxed values (e.g., "| 45 |" or "|45|")
    boxed_pattern = r'[\|\[\(]\s*(\d+\.?\d*)\s*[\|\]\)]'
    match = re.search(boxed_pattern, text)
    if match:
        value = match.group(1)
        return f"|{value}|"

    # Pattern 6: Radius values (e.g., "R0.6" or "R 0.6" or "Ro,6")
    radius_pattern = r'([Rr][Oo]?)[,\s]*(\d+\.?\d*)'
    match = re.search(radius_pattern, text)
    if match:
        symbol, value = match.groups()
        return f"R{value}"

    # Pattern 7: Radius with tolerance (e.g., "R0.6 +0.4" or "R0,6+0,4")
    radius_tolerance_pattern = r'([Rr][Oo]?)[,\s]*(\d+\.?\d*)\s*([+\-])\s*(\d+\.?\d*)'
    match = re.search(radius_tolerance_pattern, text)
    if match:
        symbol, value, tol_sign, tolerance = match.groups()
        return f"R{value} {tol_sign}{tolerance}"

    # Pattern 7b: Radius with positive and negative tolerance (e.g., "r+0.44 -0.22")
    radius_dual_tolerance_pattern = r'([Rr][Oo]?)[,\s]*([+]\d+\.?\d*)\s*[-]\s*(\d+\.?\d*)'
    match = re.search(radius_dual_tolerance_pattern, text)
    if match:
        symbol, pos_tolerance, neg_tolerance = match.groups()
        return f"R{pos_tolerance}-{neg_tolerance}"

    # Pattern 8: Number with prefix (e.g., "2 R0.6" or "2R0.6")
    prefix_number_pattern = r'(\d+)\s*([Rr][Oo]?)[,\s]*(\d+\.?\d*)'
    match = re.search(prefix_number_pattern, text)
    if match:
        prefix, symbol, value = match.groups()
        return f"R{value}"  # Ignore the prefix number as it's often a count

    # If no patterns match, return the original text
    return text

def find_best_symbol_match(detected_text, symbols, similarity_threshold=0.5, position=None, already_matched_symbols=None):
    """
    Find the best matching symbol for a detected text.

    Args:
        detected_text: String of detected text
        symbols: List of Symbol objects
        similarity_threshold: Minimum similarity score to consider a match
        position: Optional tuple (x, y) of the detected text position
        already_matched_symbols: Optional set of symbol IDs that have already been matched

    Returns:
        tuple: (best_match, similarity_score) or (None, 0) if no match found
    """
    import logging
    logger = logging.getLogger(__name__)

    best_match = None
    best_similarity = 0

    # Apply pattern recognition to standardize the detected text
    standardized_text = apply_pattern_recognition(detected_text)

    # Log the standardization if it changed the text
    if standardized_text != detected_text:
        logger.info(f"Standardized OCR text: '{detected_text}' -> '{standardized_text}'")
        detected_text = standardized_text

    # Filter out already matched symbols if provided
    available_symbols = symbols
    if already_matched_symbols:
        available_symbols = [s for s in symbols if s.id not in already_matched_symbols]

    # First pass: look for exact matches with the same value
    exact_matches = []
    for symbol in available_symbols:
        # Calculate similarity between detected text and symbol value
        similarity = SequenceMatcher(None, detected_text, symbol.value).ratio()

        # Also try with standardized symbol value
        standardized_symbol = apply_pattern_recognition(symbol.value)
        if standardized_symbol != symbol.value:
            similarity_standardized = SequenceMatcher(None, detected_text, standardized_symbol).ratio()
            similarity = max(similarity, similarity_standardized)

        # If similarity is very high (truly exact match)
        if similarity >= 0.99:
            exact_matches.append((symbol, similarity))
            logger.info(f"Found exact match: '{detected_text}' -> '{symbol.value}' with similarity {similarity:.2f}")
        # Also check for perfect normalized matches
        elif similarity > 0.9:
            # Check if the normalized values are identical
            normalized_detected = normalize_value(detected_text).lower().strip()
            normalized_symbol = normalize_value(symbol.value).lower().strip()
            if normalized_detected == normalized_symbol:
                exact_matches.append((symbol, 1.0))  # Perfect normalized match
                logger.info(f"Found exact normalized match: '{detected_text}' -> '{symbol.value}' (normalized: '{normalized_detected}')")

    # If we have multiple exact matches and a position
    if len(exact_matches) > 1 and position:
        # Group exact matches by value
        value_groups = {}
        for symbol, similarity in exact_matches:
            # Normalize the value for grouping
            value = normalize_value(symbol.value).lower().strip()
            if value not in value_groups:
                value_groups[value] = []
            value_groups[value].append((symbol, similarity))

        # For each group of identical values, find the best match based on position
        best_matches_by_value = []

        for value, matches in value_groups.items():
            if len(matches) == 1:
                # Only one match for this value, add it directly
                best_matches_by_value.append(matches[0])
            else:
                # Multiple matches for the same value
                # Check if any already have positions
                positioned_matches = [(s, sim) for s, sim in matches
                                     if (s.x_coordinate != 0 or s.y_coordinate != 0)]

                if positioned_matches:
                    # Calculate distances to all positioned matches
                    match_distances = []
                    for symbol, similarity in positioned_matches:
                        distance = math.sqrt((symbol.x_coordinate - position[0])**2 +
                                           (symbol.y_coordinate - position[1])**2)
                        match_distances.append((symbol, similarity, distance))

                    # Sort by distance (ascending)
                    match_distances.sort(key=lambda x: x[2])

                    # Check if the closest match is too close (might be the same symbol detected twice)
                    min_distance_threshold = 50  # Minimum distance in pixels to consider it a different symbol

                    if match_distances[0][2] < min_distance_threshold:
                        # Too close, likely the same symbol - skip this detection
                        logger.info(f"Skipping detection at ({position[0]}, {position[1]}) as it's too close to existing symbol at ({match_distances[0][0].x_coordinate}, {match_distances[0][0].y_coordinate})")
                        return None, 0

                    # Find the match that's furthest away from all other positioned matches
                    if len(match_distances) > 1:
                        # For each match, calculate sum of distances to all other matches
                        isolation_scores = []
                        for i, (symbol, similarity, _) in enumerate(match_distances):
                            total_distance = sum(d for _, _, d in match_distances if d > min_distance_threshold)
                            isolation_scores.append((symbol, similarity, total_distance))

                        # Sort by isolation score (descending) - prefer more isolated symbols
                        isolation_scores.sort(key=lambda x: x[2], reverse=True)
                        best_matches_by_value.append((isolation_scores[0][0], isolation_scores[0][1]))
                    else:
                        # Only one positioned match
                        best_matches_by_value.append((match_distances[0][0], match_distances[0][1]))
                else:
                    # No positioned matches, just take the first one
                    best_matches_by_value.append(matches[0])

        # Now find the closest match among the best matches for each value
        if best_matches_by_value:
            closest_match = None
            min_distance = float('inf')

            for symbol, similarity in best_matches_by_value:
                # Skip symbols without positions
                if symbol.x_coordinate == 0 and symbol.y_coordinate == 0:
                    continue

                # Calculate distance to the detected position
                distance = math.sqrt((symbol.x_coordinate - position[0])**2 +
                                   (symbol.y_coordinate - position[1])**2)

                if distance < min_distance:
                    min_distance = distance
                    closest_match = symbol
                    best_similarity = similarity

            if closest_match:
                logger.info(f"Selected closest match by position: '{detected_text}' -> '{closest_match.value}' at distance {min_distance:.2f}")
                return closest_match, best_similarity

            # If no positioned matches, just take the first one
            if not closest_match and best_matches_by_value:
                logger.info(f"No positioned matches, selecting first match: '{detected_text}' -> '{best_matches_by_value[0][0].value}'")
                return best_matches_by_value[0][0], best_matches_by_value[0][1]

    # If we have exactly one exact match, return it
    if len(exact_matches) == 1:
        logger.info(f"Selected single exact match: '{detected_text}' -> '{exact_matches[0][0].value}'")
        return exact_matches[0][0], exact_matches[0][1]

    # Second pass: try more aggressive matching for radius values and tolerances
    # This is especially important for cases like "2 Ro,6+0,4" vs "R0.6 +0.4"
    for symbol in available_symbols:
        # Extract numeric values from both strings for comparison
        detected_numbers = re.findall(r'\d+\.?\d*', detected_text)
        symbol_numbers = re.findall(r'\d+\.?\d*', symbol.value)

        # Check if the numeric values match
        if detected_numbers and symbol_numbers and len(detected_numbers) == len(symbol_numbers):
            # Compare each number
            numbers_match = True
            for i, num in enumerate(detected_numbers):
                # Convert to float for comparison to handle "0,6" vs "0.6"
                try:
                    detected_num = float(num.replace(',', '.'))
                    symbol_num = float(symbol_numbers[i].replace(',', '.'))
                    # Allow small difference due to rounding
                    if abs(detected_num - symbol_num) > 0.01:
                        numbers_match = False
                        break
                except ValueError:
                    numbers_match = False
                    break

            if numbers_match:
                # Check if both contain radius indicator
                is_radius_detected = bool(re.search(r'[Rr]', detected_text))
                is_radius_symbol = bool(re.search(r'[Rr]', symbol.value))

                if is_radius_detected and is_radius_symbol:
                    similarity = 0.85  # High similarity for matching radius values
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = symbol
                        logger.info(f"Matched by numeric values: '{detected_text}' -> '{symbol.value}' with similarity {similarity:.2f}")

    # Third pass: find the best match among all symbols using standard similarity
    if not best_match:
        for symbol in available_symbols:
            # Calculate similarity between detected text and symbol value
            similarity = SequenceMatcher(None, detected_text, symbol.value).ratio()

            # Also try with standardized symbol value
            standardized_symbol = apply_pattern_recognition(symbol.value)
            if standardized_symbol != symbol.value:
                similarity_standardized = SequenceMatcher(None, detected_text, standardized_symbol).ratio()
                similarity = max(similarity, similarity_standardized)

            # If similarity is above threshold and better than previous matches
            if similarity > similarity_threshold and similarity > best_similarity:
                best_similarity = similarity
                best_match = symbol
                logger.info(f"Matched by similarity: '{detected_text}' -> '{symbol.value}' with similarity {similarity:.2f}")

    return best_match, best_similarity
