"""
Coordinate normalization utilities for resolution-independent positioning.

This module provides functions to convert between absolute pixel coordinates
and normalized coordinates (0.0 to 1.0) for creating resolution-independent
position templates that work across different image sizes.
"""

import logging
from PIL import Image
from typing import Tuple, Optional

logger = logging.getLogger(__name__)


def normalize_coordinates(absolute_x: int, absolute_y: int, image_width: int, image_height: int) -> <PERSON><PERSON>[float, float]:
    """
    Convert absolute pixel coordinates to normalized coordinates (0.0 to 1.0).

    Args:
        absolute_x: X coordinate in pixels
        absolute_y: Y coordinate in pixels
        image_width: Width of the image in pixels
        image_height: Height of the image in pixels

    Returns:
        Tuple of (normalized_x, normalized_y) where values are between 0.0 and 1.0

    Example:
        Image: 1200x800 pixels, position at (600, 400)
        Result: (0.5, 0.5) - center of the image
    """
    if image_width <= 0 or image_height <= 0:
        logger.warning(f"Invalid image dimensions: {image_width}x{image_height}")
        return 0.0, 0.0

    normalized_x = absolute_x / image_width
    normalized_y = absolute_y / image_height

    # Clamp values to valid range [0.0, 1.0]
    normalized_x = max(0.0, min(1.0, normalized_x))
    normalized_y = max(0.0, min(1.0, normalized_y))

    logger.debug(f"Normalized ({absolute_x}, {absolute_y}) in {image_width}x{image_height} to ({normalized_x:.4f}, {normalized_y:.4f})")

    return normalized_x, normalized_y


def denormalize_coordinates(normalized_x: float, normalized_y: float, image_width: int, image_height: int) -> Tuple[int, int]:
    """
    Convert normalized coordinates (0.0 to 1.0) back to absolute pixel coordinates.

    Args:
        normalized_x: X coordinate as fraction of image width (0.0 to 1.0)
        normalized_y: Y coordinate as fraction of image height (0.0 to 1.0)
        image_width: Width of the target image in pixels
        image_height: Height of the target image in pixels

    Returns:
        Tuple of (absolute_x, absolute_y) in pixels

    Example:
        Normalized: (0.5, 0.5), Target image: 2400x1600 pixels
        Result: (1200, 800) - center of the new image
    """
    if image_width <= 0 or image_height <= 0:
        logger.warning(f"Invalid image dimensions: {image_width}x{image_height}")
        return 0, 0

    # Clamp normalized values to valid range [0.0, 1.0]
    normalized_x = max(0.0, min(1.0, normalized_x))
    normalized_y = max(0.0, min(1.0, normalized_y))

    absolute_x = int(normalized_x * image_width)
    absolute_y = int(normalized_y * image_height)

    logger.debug(f"Denormalized ({normalized_x:.4f}, {normalized_y:.4f}) in {image_width}x{image_height} to ({absolute_x}, {absolute_y})")

    return absolute_x, absolute_y


def get_image_dimensions(image_path: str) -> Tuple[int, int]:
    """
    Get the dimensions of an image file.

    Args:
        image_path: Path to the image file

    Returns:
        Tuple of (width, height) in pixels
    """
    try:
        import os

        # Check if file exists
        if not os.path.exists(image_path):
            logger.error(f"Image file does not exist: {image_path}")
            return 0, 0

        # Check if file is readable
        if not os.access(image_path, os.R_OK):
            logger.error(f"Image file is not readable: {image_path}")
            return 0, 0

        with Image.open(image_path) as img:
            width, height = img.size
            logger.debug(f"Image dimensions for {image_path}: {width}x{height}")

            # Validate dimensions
            if width <= 0 or height <= 0:
                logger.error(f"Invalid image dimensions: {width}x{height} for {image_path}")
                return 0, 0

            return width, height

    except FileNotFoundError:
        logger.error(f"Image file not found: {image_path}")
        return 0, 0
    except PermissionError:
        logger.error(f"Permission denied accessing image file: {image_path}")
        return 0, 0
    except Exception as e:
        logger.error(f"Error getting image dimensions for {image_path}: {e}")
        return 0, 0


def normalize_symbol_position(symbol, segment_image_path: str) -> bool:
    """
    Calculate and update normalized coordinates for a symbol based on its segment image.

    Args:
        symbol: Symbol instance with x_coordinate and y_coordinate
        segment_image_path: Path to the segment image file

    Returns:
        bool: True if normalization was successful, False otherwise
    """
    try:
        # Get image dimensions
        image_width, image_height = get_image_dimensions(segment_image_path)

        if image_width <= 0 or image_height <= 0:
            logger.error(f"Invalid image dimensions for symbol normalization: {image_width}x{image_height}")
            return False

        # Calculate normalized coordinates
        normalized_x, normalized_y = normalize_coordinates(
            symbol.x_coordinate,
            symbol.y_coordinate,
            image_width,
            image_height
        )

        # Update symbol with normalized coordinates
        symbol.normalized_x = normalized_x
        symbol.normalized_y = normalized_y

        logger.info(f"Normalized symbol '{symbol.value}' position: ({symbol.x_coordinate}, {symbol.y_coordinate}) → ({normalized_x:.4f}, {normalized_y:.4f})")

        return True

    except Exception as e:
        logger.error(f"Error normalizing symbol position: {e}")
        return False


def denormalize_symbol_position(symbol, target_image_width: int, target_image_height: int) -> Tuple[int, int]:
    """
    Calculate absolute coordinates for a symbol based on its normalized coordinates and target image dimensions.

    Args:
        symbol: Symbol instance with normalized_x and normalized_y
        target_image_width: Width of the target image in pixels
        target_image_height: Height of the target image in pixels

    Returns:
        Tuple of (absolute_x, absolute_y) in pixels for the target image
    """
    try:
        absolute_x, absolute_y = denormalize_coordinates(
            symbol.normalized_x,
            symbol.normalized_y,
            target_image_width,
            target_image_height
        )

        logger.debug(f"Denormalized symbol '{symbol.value}' position: ({symbol.normalized_x:.4f}, {symbol.normalized_y:.4f}) → ({absolute_x}, {absolute_y})")

        return absolute_x, absolute_y

    except Exception as e:
        logger.error(f"Error denormalizing symbol position: {e}")
        return 0, 0


def find_similar_normalized_positions(target_normalized_x: float, target_normalized_y: float,
                                    tolerance: float = 0.05) -> dict:
    """
    Find symbols with similar normalized positions within a tolerance range.

    Args:
        target_normalized_x: Target normalized X coordinate (0.0 to 1.0)
        target_normalized_y: Target normalized Y coordinate (0.0 to 1.0)
        tolerance: Tolerance range for matching (default: 0.05 = 5% of image dimensions)

    Returns:
        dict: Query parameters for filtering symbols by normalized position
    """
    min_x = max(0.0, target_normalized_x - tolerance)
    max_x = min(1.0, target_normalized_x + tolerance)
    min_y = max(0.0, target_normalized_y - tolerance)
    max_y = min(1.0, target_normalized_y + tolerance)

    return {
        'normalized_x__gte': min_x,
        'normalized_x__lte': max_x,
        'normalized_y__gte': min_y,
        'normalized_y__lte': max_y
    }


def calculate_normalized_distance(pos1_x: float, pos1_y: float, pos2_x: float, pos2_y: float) -> float:
    """
    Calculate the Euclidean distance between two normalized positions.

    Args:
        pos1_x, pos1_y: First position (normalized coordinates)
        pos2_x, pos2_y: Second position (normalized coordinates)

    Returns:
        float: Distance between the positions (0.0 to ~1.414)
    """
    import math

    dx = pos2_x - pos1_x
    dy = pos2_y - pos1_y
    distance = math.sqrt(dx * dx + dy * dy)

    return distance


def update_existing_symbols_normalized_coordinates():
    """
    Update normalized coordinates for existing symbols that don't have them.
    This is useful for migrating existing data after adding normalized coordinate fields.

    Returns:
        dict: Summary of the update process
    """
    from api.models import Symbol, Segment

    updated_count = 0
    error_count = 0
    total_symbols = 0

    # Get all symbols that don't have normalized coordinates
    symbols_to_update = Symbol.objects.filter(
        normalized_x=0.0,
        normalized_y=0.0,
        x_coordinate__gt=0,  # Only symbols with actual positions
        y_coordinate__gt=0
    )

    total_symbols = symbols_to_update.count()
    logger.info(f"Found {total_symbols} symbols to update with normalized coordinates")

    for symbol in symbols_to_update:
        try:
            # Get the segment image path
            if symbol.segment and symbol.segment.image:
                segment_image_path = symbol.segment.image.path

                # Update normalized coordinates
                if normalize_symbol_position(symbol, segment_image_path):
                    symbol.save()
                    updated_count += 1
                    logger.debug(f"Updated symbol {symbol.id}: '{symbol.value}'")
                else:
                    error_count += 1
                    logger.warning(f"Failed to update symbol {symbol.id}: '{symbol.value}'")
            else:
                error_count += 1
                logger.warning(f"Symbol {symbol.id} has no segment image")

        except Exception as e:
            error_count += 1
            logger.error(f"Error updating symbol {symbol.id}: {e}")

    summary = {
        'total_symbols': total_symbols,
        'updated_count': updated_count,
        'error_count': error_count,
        'success_rate': (updated_count / total_symbols * 100) if total_symbols > 0 else 0
    }

    logger.info(f"Normalized coordinate update complete: {summary}")
    return summary


def update_existing_easyocr_detections_normalized_coordinates():
    """
    Update normalized coordinates for existing EasyOCR detections that don't have them.
    This is useful for migrating existing data after adding normalized coordinate fields.

    Returns:
        dict: Summary of the update process
    """
    from api.models import EasyOCRDetection

    updated_count = 0
    error_count = 0
    total_detections = 0

    # Get all EasyOCR detections that don't have normalized coordinates
    detections_to_update = EasyOCRDetection.objects.filter(
        normalized_x=0.0,
        normalized_y=0.0,
        center_x__gt=0,  # Only detections with actual positions
        center_y__gt=0
    )

    total_detections = detections_to_update.count()
    logger.info(f"Found {total_detections} EasyOCR detections to update with normalized coordinates")

    for detection in detections_to_update:
        try:
            # Get the segment image path
            if detection.segment and detection.segment.image:
                segment_image_path = detection.segment.image.path

                # Get image dimensions
                image_width, image_height = get_image_dimensions(segment_image_path)

                if image_width > 0 and image_height > 0:
                    # Calculate normalized coordinates
                    normalized_x, normalized_y = normalize_coordinates(
                        detection.center_x,
                        detection.center_y,
                        image_width,
                        image_height
                    )

                    # Update detection
                    detection.normalized_x = normalized_x
                    detection.normalized_y = normalized_y
                    detection.save()

                    updated_count += 1
                    logger.debug(f"Updated EasyOCR detection {detection.id}: '{detection.detected_text}'")
                else:
                    error_count += 1
                    logger.warning(f"Invalid image dimensions for detection {detection.id}")
            else:
                error_count += 1
                logger.warning(f"EasyOCR detection {detection.id} has no segment image")

        except Exception as e:
            error_count += 1
            logger.error(f"Error updating EasyOCR detection {detection.id}: {e}")

    summary = {
        'total_detections': total_detections,
        'updated_count': updated_count,
        'error_count': error_count,
        'success_rate': (updated_count / total_detections * 100) if total_detections > 0 else 0
    }

    logger.info(f"EasyOCR normalized coordinate update complete: {summary}")
    return summary
