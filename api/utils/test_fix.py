"""
Test script for the fixed parse_gpt41_response function.
"""

import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.utils import parse_gpt41_response, call_gpt41_api, encode_image_to_base64
from api.models import Segment, Symbol
from fix_parse_gpt41 import fixed_parse_gpt41_response

def test_with_sample_response():
    """Test the fixed function with a sample response."""
    sample_response = '''```json
{
  "symbols": [
    {
      "symbol_type": "boxed",
      "value": "|0.1|"
    },
    {
      "symbol_type": "degree",
      "value": "71°"
    },
    {
      "symbol_type": "tolerance",
      "value": "max. 0.46"
    },
    {
      "symbol_type": "text",
      "value": "11.67 cm²"
    },
    {
      "symbol_type": "text",
      "value": "0.767 g"
    }
  ]
}
```'''

    print("Testing with sample response...")
    print("Original function result:")
    original_result = parse_gpt41_response(sample_response)
    print(f"Parsed {len(original_result)} symbols: {original_result}")

    print("\nFixed function result:")
    fixed_result = fixed_parse_gpt41_response(sample_response)
    print(f"Parsed {len(fixed_result)} symbols: {fixed_result}")

def test_with_real_segment():
    """Test the fixed function with a real segment from the database."""
    try:
        # Get segment 4 from the most recent drawing
        segment = Segment.objects.filter(segment_number=4).order_by('-id').first()
        if not segment:
            print("No segment 4 found in the database.")
            return

        print(f"Testing with segment 4 from drawing {segment.drawing.id}...")
        
        # Encode the image to base64
        segment_base64 = encode_image_to_base64(segment.image.path)
        
        # Call the GPT-4.1 API
        print("Calling GPT-4.1 API...")
        response = call_gpt41_api(segment_base64, 4)
        
        print("\nAPI Response:")
        print(response[:500] + "..." if len(response) > 500 else response)
        
        print("\nOriginal function result:")
        original_result = parse_gpt41_response(response)
        print(f"Parsed {len(original_result)} symbols: {original_result}")
        
        print("\nFixed function result:")
        fixed_result = fixed_parse_gpt41_response(response)
        print(f"Parsed {len(fixed_result)} symbols: {fixed_result}")
        
        # If the fixed function found symbols but the original didn't, update the database
        if fixed_result and not original_result:
            print("\nUpdating database with symbols found by fixed function...")
            for symbol_data in fixed_result:
                Symbol.objects.create(
                    segment=segment,
                    symbol_type=symbol_data['type'],
                    value=symbol_data['value'],
                    x_coordinate=symbol_data['x'],
                    y_coordinate=symbol_data['y'],
                    is_marked=symbol_data['marked']
                )
            print(f"Added {len(fixed_result)} symbols to segment 4.")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_with_sample_response()
    print("\n" + "="*50 + "\n")
    test_with_real_segment()
