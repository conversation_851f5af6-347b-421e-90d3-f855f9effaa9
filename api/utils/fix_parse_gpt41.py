"""
This module contains a fixed version of the parse_gpt41_response function
that properly handles code blocks in the API response.
"""

import json
import logging
import re
import random

logger = logging.getLogger(__name__)

def fixed_parse_gpt41_response(response_text):
    """
    Parse the GPT-4.1 API response into a structured format.
    Handles responses that are wrapped in code blocks.

    Args:
        response_text: The JSON response from the API

    Returns:
        List of symbol dictionaries
    """
    try:
        # If we have a valid response, parse it
        if not response_text:
            logger.warning("No response text provided to parse_gpt41_response")
            return []

        # Check if the response is wrapped in code blocks and extract the JSON
        if "```json" in response_text or "```" in response_text:
            # Extract content between code blocks
            code_block_pattern = r"```(?:json)?(.*?)```"
            matches = re.findall(code_block_pattern, response_text, re.DOTALL)
            if matches:
                # Use the first match
                response_text = matches[0].strip()
                logger.info(f"Extracted JSON from code block: {response_text[:100]}...")

        # Parse the JSON response
        try:
            response_data = json.loads(response_text)
            logger.info(f"Successfully parsed JSON: {str(response_data)[:100]}...")
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Response text: {response_text[:500]}")
            return []

        # Extract symbols from the response
        symbols = []

        if 'symbols' in response_data:
            for symbol in response_data['symbols']:
                # Get the symbol type, defaulting to 'other' if not found
                symbol_type = symbol.get('symbol_type', 'other').lower()

                # Map 'text' type to 'other' for consistency with our database model
                if symbol_type == 'text':
                    symbol_type = 'other'

                # Get the value
                value = symbol.get('value', '')

                # Generate random coordinates if not provided
                # In a real implementation, we would use the actual coordinates from the image
                x = random.randint(50, 350)
                y = random.randint(50, 350)

                # Add to symbols list
                symbols.append({
                    'type': symbol_type,
                    'value': value,
                    'x': x,
                    'y': y,
                    'marked': True  # Mark all detected symbols as significant
                })

            logger.info(f"Parsed {len(symbols)} symbols from GPT-4.1 response")
            return symbols
        else:
            logger.warning("No 'symbols' key found in GPT-4.1 response")
            return []

    except Exception as e:
        logger.error(f"Error in parse_gpt41_response: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []
