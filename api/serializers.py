from rest_framework import serializers
from .models import Drawing, Segment, Symbol, AnalysisLog, Company, CageType, Template, SegmentSimilarity, BoundingBox

class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'

class CageTypeSerializer(serializers.ModelSerializer):
    company_name = serializers.ReadOnlyField(source='company.name')

    class Meta:
        model = CageType
        fields = '__all__'

class TemplateSerializer(serializers.ModelSerializer):
    cage_type_name = serializers.ReadOnlyField(source='cage_type.name')
    company_name = serializers.ReadOnlyField(source='cage_type.company.name')

    class Meta:
        model = Template
        fields = '__all__'

class SymbolSerializer(serializers.ModelSerializer):
    symbol_type_display = serializers.ReadOnlyField(source='get_symbol_type_display')
    position_source_display = serializers.ReadOnlyField(source='get_position_source_display')
    orientation_display = serializers.ReadOnlyField(source='get_orientation_display')

    class Meta:
        model = Symbol
        fields = '__all__'

class SegmentSerializer(serializers.ModelSerializer):
    symbols_count = serializers.SerializerMethodField()
    detected_symbols_count = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    grid_image = serializers.SerializerMethodField()
    annotated_image = serializers.SerializerMethodField()
    marked_image = serializers.SerializerMethodField()

    class Meta:
        model = Segment
        fields = '__all__'

    def get_symbols_count(self, obj):
        return obj.symbols.count()

    def get_detected_symbols_count(self, obj):
        return obj.symbols.filter(is_marked=True).count()

    def get_image(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def get_grid_image(self, obj):
        if obj.grid_image:
            return obj.grid_image.url
        return None

    def get_annotated_image(self, obj):
        if obj.annotated_image:
            return obj.annotated_image.url
        return None

    def get_marked_image(self, obj):
        if obj.marked_image:
            return obj.marked_image.url
        return None

class SegmentDetailSerializer(serializers.ModelSerializer):
    symbols = SymbolSerializer(many=True, read_only=True)
    image = serializers.SerializerMethodField()
    grid_image = serializers.SerializerMethodField()
    annotated_image = serializers.SerializerMethodField()
    marked_image = serializers.SerializerMethodField()
    process_type = serializers.CharField(source='drawing.process_type', read_only=True)

    class Meta:
        model = Segment
        fields = '__all__'

    def get_image(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def get_grid_image(self, obj):
        if obj.grid_image:
            return obj.grid_image.url
        return None

    def get_annotated_image(self, obj):
        if obj.annotated_image:
            return obj.annotated_image.url
        return None

    def get_marked_image(self, obj):
        if obj.marked_image:
            return obj.marked_image.url
        return None

class DrawingSerializer(serializers.ModelSerializer):
    segments_count = serializers.SerializerMethodField()
    template_name = serializers.ReadOnlyField(source='template.name', default=None)
    image = serializers.SerializerMethodField()
    final_image = serializers.SerializerMethodField()
    finalized_image = serializers.SerializerMethodField()
    measurements_excel = serializers.SerializerMethodField()
    summary_file = serializers.SerializerMethodField()

    class Meta:
        model = Drawing
        fields = '__all__'

    def get_segments_count(self, obj):
        return obj.segments.count()

    def get_image(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def get_final_image(self, obj):
        if obj.final_image:
            return obj.final_image.url
        return None

    def get_finalized_image(self, obj):
        if obj.finalized_image:
            return obj.finalized_image.url
        return None

    def get_measurements_excel(self, obj):
        if obj.measurements_excel:
            return obj.measurements_excel.url
        return None

    def get_summary_file(self, obj):
        if obj.summary_file:
            return obj.summary_file.url
        return None

class DrawingDetailSerializer(serializers.ModelSerializer):
    segments = SegmentSerializer(many=True, read_only=True)
    template_name = serializers.ReadOnlyField(source='template.name', default=None)
    image = serializers.SerializerMethodField()
    final_image = serializers.SerializerMethodField()
    finalized_image = serializers.SerializerMethodField()
    measurements_excel = serializers.SerializerMethodField()
    summary_file = serializers.SerializerMethodField()

    class Meta:
        model = Drawing
        fields = '__all__'

    def get_image(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def get_final_image(self, obj):
        if obj.final_image:
            return obj.final_image.url
        return None

    def get_finalized_image(self, obj):
        if obj.finalized_image:
            return obj.finalized_image.url
        return None

    def get_measurements_excel(self, obj):
        if obj.measurements_excel:
            return obj.measurements_excel.url
        return None

    def get_summary_file(self, obj):
        if obj.summary_file:
            return obj.summary_file.url
        return None

class AnalysisLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = AnalysisLog
        fields = '__all__'

class SegmentSimilaritySerializer(serializers.ModelSerializer):
    similar_segment_image = serializers.SerializerMethodField()
    similar_segment_marked_image = serializers.SerializerMethodField()
    similar_segment_number = serializers.ReadOnlyField(source='similar_segment.segment_number')
    similar_segment_drawing_id = serializers.ReadOnlyField(source='similar_segment.drawing.id')
    similar_segment_symbols_count = serializers.SerializerMethodField()

    class Meta:
        model = SegmentSimilarity
        fields = '__all__'

    def get_similar_segment_image(self, obj):
        if obj.similar_segment.image:
            return obj.similar_segment.image.url
        return None

    def get_similar_segment_marked_image(self, obj):
        if obj.similar_segment.marked_image:
            return obj.similar_segment.marked_image.url
        return None

    def get_similar_segment_symbols_count(self, obj):
        return obj.similar_segment.symbols.filter(is_marked=True).count()

class UploadDrawingSerializer(serializers.Serializer):
    image = serializers.ImageField()
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all(), required=False)
    cage_type = serializers.PrimaryKeyRelatedField(queryset=CageType.objects.all(), required=False)
    template = serializers.PrimaryKeyRelatedField(queryset=Template.objects.all(), required=False, allow_null=True)
    process_type = serializers.ChoiceField(choices=[('A', 'Process A'), ('B', 'Process B')], default='A')

    def validate_image(self, value):
        """
        Check that the image is valid
        """
        # Check if the file is an image
        if not value.content_type.startswith('image/'):
            raise serializers.ValidationError("File is not an image")

        # Check if the file size is too large (10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("Image file too large (> 10MB)")

        return value

    def validate(self, data):
        """
        Check that the template is valid for the selected company and cage type
        """
        company = data.get('company')
        cage_type = data.get('cage_type')
        template = data.get('template')

        if template and cage_type and template.cage_type.id != cage_type.id:
            raise serializers.ValidationError("Template does not belong to the selected cage type")

        if cage_type and company and cage_type.company.id != company.id:
            raise serializers.ValidationError("Cage type does not belong to the selected company")

        return data

class AnalyzeDrawingSerializer(serializers.Serializer):
    ai_model = serializers.ChoiceField(choices=['claude', 'gpt4o', 'gpt41', 'llama4'], default='gpt41')

class MarkSymbolSerializer(serializers.Serializer):
    display_order = serializers.IntegerField(required=False)

class UpdateSymbolPositionSerializer(serializers.Serializer):
    x_coordinate = serializers.IntegerField()
    y_coordinate = serializers.IntegerField()

class EditSymbolSerializer(serializers.Serializer):
    value = serializers.CharField(required=False)
    symbol_type = serializers.ChoiceField(choices=[choice[0] for choice in Symbol.SYMBOL_TYPES], required=False)
    description = serializers.CharField(required=False, allow_blank=True)

class AddSymbolSerializer(serializers.Serializer):
    value = serializers.CharField()
    symbol_type = serializers.ChoiceField(choices=[choice[0] for choice in Symbol.SYMBOL_TYPES], default='other')
    x_coordinate = serializers.IntegerField()
    y_coordinate = serializers.IntegerField()
    description = serializers.CharField(required=False, allow_blank=True)

class BulkDeleteSymbolsSerializer(serializers.Serializer):
    symbol_ids = serializers.ListField(child=serializers.IntegerField())

class AdjustBubbleSizeSerializer(serializers.Serializer):
    bubble_size = serializers.IntegerField(min_value=20, max_value=150)

class AddSymbolAtClickSerializer(serializers.Serializer):
    clicked_x = serializers.IntegerField()
    clicked_y = serializers.IntegerField()
    ai_model = serializers.ChoiceField(choices=['claude', 'gpt4o', 'gpt41'], default='gpt41', required=False)

class ConfirmSimilaritySerializer(serializers.Serializer):
    similar_segment_id = serializers.IntegerField()
    is_similar = serializers.BooleanField()
    similarity_score = serializers.FloatField(required=False)
    user_rating = serializers.IntegerField(min_value=1, max_value=5, required=False)

# Process B Serializers
class BoundingBoxSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoundingBox
        fields = '__all__'

class ProcessBSegmentationSerializer(serializers.Serializer):
    """Serializer for Process B segmentation only"""
    pass

class ProcessBBoundingBoxDetectionSerializer(serializers.Serializer):
    """Serializer for Process B EasyOCR bounding box detection"""
    pass

class ProcessBSymbolConfirmationSerializer(serializers.Serializer):
    """Serializer for Process B symbol confirmation"""
    bounding_box_id = serializers.IntegerField()
    confirmed = serializers.BooleanField()
    symbol_value = serializers.CharField(required=False, allow_blank=True)
    symbol_type = serializers.ChoiceField(choices=[choice[0] for choice in Symbol.SYMBOL_TYPES], required=False)
    description = serializers.CharField(required=False, allow_blank=True)
    combined_values = serializers.ListField(child=serializers.CharField(), required=False, allow_null=True)
    # New fields for bubble positioning
    orientation = serializers.CharField(required=False, allow_blank=True)  # 'normal' or 'rotated_90'
    value_index = serializers.IntegerField(required=False, allow_null=True)  # 1, 2, 3, etc.
    total_values = serializers.IntegerField(required=False, allow_null=True)  # Total values in this bounding box
