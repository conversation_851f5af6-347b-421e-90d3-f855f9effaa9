#!/usr/bin/env python
"""
Script to recreate marked images for all segments in the database.
This is useful when adding the marked_image functionality to an existing project.
"""

import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Segment, Symbol
from api.views import recreate_marked_image
from django.conf import settings

def recreate_all_marked_images():
    """Recreate marked images for all segments with marked symbols"""
    print("Starting to recreate marked images for all segments...")
    
    # Get all segments
    segments = Segment.objects.all()
    total_segments = segments.count()
    print(f"Found {total_segments} segments in the database")
    
    # Counter for successful recreations
    success_count = 0
    
    # Process each segment
    for i, segment in enumerate(segments, 1):
        # Check if segment has marked symbols
        marked_symbols = Symbol.objects.filter(segment=segment, is_marked=True)
        if marked_symbols.exists():
            print(f"Processing segment {segment.id} ({i}/{total_segments}) - has {marked_symbols.count()} marked symbols")
            
            # Recreate marked image
            if recreate_marked_image(segment):
                success_count += 1
                print(f"✓ Successfully recreated marked image for segment {segment.id}")
            else:
                print(f"✗ Failed to recreate marked image for segment {segment.id}")
        else:
            print(f"Skipping segment {segment.id} ({i}/{total_segments}) - no marked symbols")
    
    print(f"\nCompleted processing {total_segments} segments")
    print(f"Successfully recreated {success_count} marked images")

if __name__ == "__main__":
    recreate_all_marked_images()
