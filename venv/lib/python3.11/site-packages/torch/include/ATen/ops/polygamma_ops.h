#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API polygamma_out {
  using schema = at::Tensor & (int64_t, const at::Tensor &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::polygamma";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "polygamma.out(int n, Tensor self, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(int64_t n, const at::Tensor & self, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, int64_t n, const at::Tensor & self, at::Tensor & out);
};

struct TORCH_API polygamma {
  using schema = at::Tensor (int64_t, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::polygamma";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "polygamma(int n, Tensor self) -> Tensor";
  static at::Tensor call(int64_t n, const at::Tensor & self);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, int64_t n, const at::Tensor & self);
};

struct TORCH_API polygamma_ {
  using schema = at::Tensor & (at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::polygamma_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "polygamma_(Tensor(a!) self, int n) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, int64_t n);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, int64_t n);
};

}} // namespace at::_ops
