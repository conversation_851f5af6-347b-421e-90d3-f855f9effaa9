#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API sym_constrain_range_for_size {
  using schema = void (const at::Scalar &, ::std::optional<int64_t>, ::std::optional<int64_t>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::sym_constrain_range_for_size";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "sym_constrain_range_for_size(Scalar size, *, int? min=None, int? max=None) -> ()";
  static void call(const at::Scalar & size, ::std::optional<int64_t> min, ::std::optional<int64_t> max);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, const at::Scalar & size, ::std::optional<int64_t> min, ::std::optional<int64_t> max);
};

}} // namespace at::_ops
