#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/nonzero_numpy_ops.h>

namespace at {


// aten::nonzero_numpy(Tensor self) -> Tensor[]
inline ::std::vector<at::Tensor> nonzero_numpy(const at::Tensor & self) {
    return at::_ops::nonzero_numpy::call(self);
}

}
