#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_nested_select_backward_ops.h>

namespace at {


// aten::_nested_select_backward(Tensor grad_output, Tensor self, int dim, SymInt index) -> Tensor
inline at::Tensor _nested_select_backward(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, int64_t index) {
    return at::_ops::_nested_select_backward::call(grad_output, self, dim, index);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor _nested_select_backward(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, int64_t index) {
    return at::_ops::_nested_select_backward::call(grad_output, self, dim, index);
  }
}

// aten::_nested_select_backward(Tensor grad_output, Tensor self, int dim, SymInt index) -> Tensor
inline at::Tensor _nested_select_backward_symint(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, c10::SymInt index) {
    return at::_ops::_nested_select_backward::call(grad_output, self, dim, index);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor _nested_select_backward(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, c10::SymInt index) {
    return at::_ops::_nested_select_backward::call(grad_output, self, dim, index);
  }
}

}
