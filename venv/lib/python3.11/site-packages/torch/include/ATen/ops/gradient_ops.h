#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API gradient_scalarint {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, const ::std::optional<at::Scalar> &, ::std::optional<int64_t>, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "scalarint";
  static constexpr const char* schema_str = "gradient.scalarint(Tensor self, *, <PERSON>ala<PERSON>? spacing=None, int? dim=None, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, const ::std::optional<at::Scalar> & spacing, ::std::optional<int64_t> dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const ::std::optional<at::Scalar> & spacing, ::std::optional<int64_t> dim, int64_t edge_order);
};

struct TORCH_API gradient_scalararray {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, const at::Scalar &, at::IntArrayRef, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "scalararray";
  static constexpr const char* schema_str = "gradient.scalararray(Tensor self, *, Scalar spacing, int[] dim, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, const at::Scalar & spacing, at::IntArrayRef dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Scalar & spacing, at::IntArrayRef dim, int64_t edge_order);
};

struct TORCH_API gradient_array {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::IntArrayRef, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "array";
  static constexpr const char* schema_str = "gradient.array(Tensor self, *, int[] dim, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, at::IntArrayRef dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::IntArrayRef dim, int64_t edge_order);
};

struct TORCH_API gradient_scalarrayint {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::ArrayRef<at::Scalar>, ::std::optional<int64_t>, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "scalarrayint";
  static constexpr const char* schema_str = "gradient.scalarrayint(Tensor self, *, Scalar[] spacing, int? dim=None, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, ::std::optional<int64_t> dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, ::std::optional<int64_t> dim, int64_t edge_order);
};

struct TORCH_API gradient_scalarrayarray {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::ArrayRef<at::Scalar>, at::IntArrayRef, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "scalarrayarray";
  static constexpr const char* schema_str = "gradient.scalarrayarray(Tensor self, *, Scalar[] spacing, int[] dim, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, at::IntArrayRef dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, at::IntArrayRef dim, int64_t edge_order);
};

struct TORCH_API gradient_tensorarrayint {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::TensorList, ::std::optional<int64_t>, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "tensorarrayint";
  static constexpr const char* schema_str = "gradient.tensorarrayint(Tensor self, *, Tensor[] spacing, int? dim=None, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, at::TensorList spacing, ::std::optional<int64_t> dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::TensorList spacing, ::std::optional<int64_t> dim, int64_t edge_order);
};

struct TORCH_API gradient_tensorarray {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::TensorList, at::IntArrayRef, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::gradient";
  static constexpr const char* overload_name = "tensorarray";
  static constexpr const char* schema_str = "gradient.tensorarray(Tensor self, *, Tensor[] spacing, int[] dim, int edge_order=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & self, at::TensorList spacing, at::IntArrayRef dim, int64_t edge_order);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::TensorList spacing, at::IntArrayRef dim, int64_t edge_order);
};

}} // namespace at::_ops
