#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/acos_ops.h>

namespace at {


// aten::acos(Tensor self) -> Tensor
inline at::Tensor acos(const at::Tensor & self) {
    return at::_ops::acos::call(self);
}

// aten::acos_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & acos_(at::Tensor & self) {
    return at::_ops::acos_::call(self);
}

// aten::acos.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & acos_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::acos_out::call(self, out);
}
// aten::acos.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & acos_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::acos_out::call(self, out);
}

}
