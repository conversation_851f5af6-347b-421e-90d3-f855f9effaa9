#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API reshape {
  using schema = at::Tensor (const at::Tensor &, c10::SymIntArrayRef);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::reshape";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "reshape(Tensor(a) self, SymInt[] shape) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, c10::SymIntArrayRef shape);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, c10::SymIntArrayRef shape);
};

}} // namespace at::_ops
