#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API stride_int {
  using schema = int64_t (const at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::stride";
  static constexpr const char* overload_name = "int";
  static constexpr const char* schema_str = "stride.int(Tensor self, int dim) -> int";
  static int64_t call(const at::Tensor & self, int64_t dim);
  static int64_t redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t dim);
};

struct TORCH_API stride_Dimname {
  using schema = int64_t (const at::Tensor &, at::Dimname);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::stride";
  static constexpr const char* overload_name = "Dimname";
  static constexpr const char* schema_str = "stride.Dimname(Tensor self, Dimname dim) -> int";
  static int64_t call(const at::Tensor & self, at::Dimname dim);
  static int64_t redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::Dimname dim);
};

}} // namespace at::_ops
