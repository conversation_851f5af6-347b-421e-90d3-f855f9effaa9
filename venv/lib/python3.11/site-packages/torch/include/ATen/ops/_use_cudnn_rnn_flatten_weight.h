#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_use_cudnn_rnn_flatten_weight_ops.h>

namespace at {


// aten::_use_cudnn_rnn_flatten_weight() -> bool
inline bool _use_cudnn_rnn_flatten_weight() {
    return at::_ops::_use_cudnn_rnn_flatten_weight::call();
}

}
