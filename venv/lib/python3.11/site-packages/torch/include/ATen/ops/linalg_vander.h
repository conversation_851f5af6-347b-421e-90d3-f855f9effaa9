#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/linalg_vander_ops.h>

namespace at {


// aten::linalg_vander(Tensor x, *, SymInt? N=None) -> Tensor
inline at::Tensor linalg_vander(const at::Tensor & x, ::std::optional<int64_t> N=::std::nullopt) {
    return at::_ops::linalg_vander::call(x, N.has_value() ? ::std::make_optional(c10::SymInt(*N)) : ::std::nullopt);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor linalg_vander(const at::Tensor & x, ::std::optional<int64_t> N=::std::nullopt) {
    return at::_ops::linalg_vander::call(x, N.has_value() ? ::std::make_optional(c10::SymInt(*N)) : ::std::nullopt);
  }
}

// aten::linalg_vander(Tensor x, *, SymInt? N=None) -> Tensor
inline at::Tensor linalg_vander_symint(const at::Tensor & x, ::std::optional<c10::SymInt> N=::std::nullopt) {
    return at::_ops::linalg_vander::call(x, N);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor linalg_vander(const at::Tensor & x, ::std::optional<c10::SymInt> N=::std::nullopt) {
    return at::_ops::linalg_vander::call(x, N);
  }
}

}
