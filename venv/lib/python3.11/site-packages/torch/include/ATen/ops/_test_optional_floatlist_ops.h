#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _test_optional_floatlist {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<at::ArrayRef<double>>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_test_optional_floatlist";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_test_optional_floatlist(Tensor values, float[]? addends) -> Tensor";
  static at::Tensor call(const at::Tensor & values, ::std::optional<at::ArrayRef<double>> addends);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & values, ::std::optional<at::ArrayRef<double>> addends);
};

struct TORCH_API _test_optional_floatlist_out {
  using schema = at::Tensor & (const at::Tensor &, ::std::optional<at::ArrayRef<double>>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_test_optional_floatlist";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_test_optional_floatlist.out(Tensor values, float[]? addends, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & values, ::std::optional<at::ArrayRef<double>> addends, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & values, ::std::optional<at::ArrayRef<double>> addends, at::Tensor & out);
};

}} // namespace at::_ops
