#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/vstack_ops.h>

namespace at {


// aten::vstack(Tensor[] tensors) -> Tensor
inline at::Tensor vstack(at::TensorList tensors) {
    return at::_ops::vstack::call(tensors);
}

// aten::vstack.out(Tensor[] tensors, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & vstack_out(at::Tensor & out, at::TensorList tensors) {
    return at::_ops::vstack_out::call(tensors, out);
}
// aten::vstack.out(Tensor[] tensors, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & vstack_outf(at::TensorList tensors, at::Tensor & out) {
    return at::_ops::vstack_out::call(tensors, out);
}

}
