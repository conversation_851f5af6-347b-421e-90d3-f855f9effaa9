#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_foreach_div_ops.h>

namespace at {


// aten::_foreach_div.Scalar(Tensor[] self, Scalar scalar) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_div(at::TensorList self, const at::Scalar & scalar) {
    return at::_ops::_foreach_div_Scalar::call(self, scalar);
}

// aten::_foreach_div_.Scalar(Tensor(a!)[] self, Scalar scalar) -> ()
inline void _foreach_div_(at::TensorList self, const at::Scalar & scalar) {
    return at::_ops::_foreach_div__Scalar::call(self, scalar);
}

// aten::_foreach_div.List(Tensor[] self, Tensor[] other) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_div(at::TensorList self, at::TensorList other) {
    return at::_ops::_foreach_div_List::call(self, other);
}

// aten::_foreach_div_.List(Tensor(a!)[] self, Tensor[] other) -> ()
inline void _foreach_div_(at::TensorList self, at::TensorList other) {
    return at::_ops::_foreach_div__List::call(self, other);
}

// aten::_foreach_div.ScalarList(Tensor[] self, Scalar[] scalars) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_div(at::TensorList self, at::ArrayRef<at::Scalar> scalars) {
    return at::_ops::_foreach_div_ScalarList::call(self, scalars);
}

// aten::_foreach_div_.ScalarList(Tensor(a!)[] self, Scalar[] scalars) -> ()
inline void _foreach_div_(at::TensorList self, at::ArrayRef<at::Scalar> scalars) {
    return at::_ops::_foreach_div__ScalarList::call(self, scalars);
}

// aten::_foreach_div.Tensor(Tensor[] self, Tensor other) -> Tensor[]
inline ::std::vector<at::Tensor> _foreach_div(at::TensorList self, const at::Tensor & other) {
    return at::_ops::_foreach_div_Tensor::call(self, other);
}

// aten::_foreach_div_.Tensor(Tensor(a!)[] self, Tensor other) -> ()
inline void _foreach_div_(at::TensorList self, const at::Tensor & other) {
    return at::_ops::_foreach_div__Tensor::call(self, other);
}

// aten::_foreach_div.Scalar_out(Tensor[] self, Scalar scalar, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_out(at::TensorList out, at::TensorList self, const at::Scalar & scalar) {
    return at::_ops::_foreach_div_Scalar_out::call(self, scalar, out);
}
// aten::_foreach_div.Scalar_out(Tensor[] self, Scalar scalar, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_outf(at::TensorList self, const at::Scalar & scalar, at::TensorList out) {
    return at::_ops::_foreach_div_Scalar_out::call(self, scalar, out);
}

// aten::_foreach_div.List_out(Tensor[] self, Tensor[] other, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_out(at::TensorList out, at::TensorList self, at::TensorList other) {
    return at::_ops::_foreach_div_List_out::call(self, other, out);
}
// aten::_foreach_div.List_out(Tensor[] self, Tensor[] other, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_outf(at::TensorList self, at::TensorList other, at::TensorList out) {
    return at::_ops::_foreach_div_List_out::call(self, other, out);
}

// aten::_foreach_div.ScalarList_out(Tensor[] self, Scalar[] scalars, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_out(at::TensorList out, at::TensorList self, at::ArrayRef<at::Scalar> scalars) {
    return at::_ops::_foreach_div_ScalarList_out::call(self, scalars, out);
}
// aten::_foreach_div.ScalarList_out(Tensor[] self, Scalar[] scalars, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_outf(at::TensorList self, at::ArrayRef<at::Scalar> scalars, at::TensorList out) {
    return at::_ops::_foreach_div_ScalarList_out::call(self, scalars, out);
}

// aten::_foreach_div.Tensor_out(Tensor[] self, Tensor other, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_out(at::TensorList out, at::TensorList self, const at::Tensor & other) {
    return at::_ops::_foreach_div_Tensor_out::call(self, other, out);
}
// aten::_foreach_div.Tensor_out(Tensor[] self, Tensor other, *, Tensor(a!)[] out) -> ()
inline void _foreach_div_outf(at::TensorList self, const at::Tensor & other, at::TensorList out) {
    return at::_ops::_foreach_div_Tensor_out::call(self, other, out);
}

}
