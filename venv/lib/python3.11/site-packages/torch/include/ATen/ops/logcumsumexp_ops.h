#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API logcumsumexp {
  using schema = at::Tensor (const at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::logcumsumexp";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "logcumsumexp(Tensor self, int dim) -> Tensor";
  static at::Tensor call(const at::Tensor & self, int64_t dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t dim);
};

struct TORCH_API logcumsumexp_out {
  using schema = at::Tensor & (const at::Tensor &, int64_t, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::logcumsumexp";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "logcumsumexp.out(Tensor self, int dim, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, int64_t dim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t dim, at::Tensor & out);
};

struct TORCH_API logcumsumexp_dimname {
  using schema = at::Tensor (const at::Tensor &, at::Dimname);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::logcumsumexp";
  static constexpr const char* overload_name = "dimname";
  static constexpr const char* schema_str = "logcumsumexp.dimname(Tensor self, Dimname dim) -> Tensor";
  static at::Tensor call(const at::Tensor & self, at::Dimname dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::Dimname dim);
};

struct TORCH_API logcumsumexp_dimname_out {
  using schema = at::Tensor & (const at::Tensor &, at::Dimname, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::logcumsumexp";
  static constexpr const char* overload_name = "dimname_out";
  static constexpr const char* schema_str = "logcumsumexp.dimname_out(Tensor self, Dimname dim, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, at::Dimname dim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::Dimname dim, at::Tensor & out);
};

}} // namespace at::_ops
