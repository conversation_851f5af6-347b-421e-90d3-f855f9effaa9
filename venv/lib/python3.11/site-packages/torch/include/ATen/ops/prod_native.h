#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/prod_meta.h>

namespace at {
namespace native {
TORCH_API at::Tensor & prod_out(const at::Tensor & self, ::std::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor prod(const at::Tensor & self, ::std::optional<at::ScalarType> dtype=::std::nullopt);
struct TORCH_API structured_prod_out : public at::meta::structured_prod_dim_int {
void impl(const at::Tensor & self, int64_t dim, bool keepdim, ::std::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor prod(const at::Tensor & self, at::Dimname dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt);
TORCH_API at::Tensor & prod_out(const at::Tensor & self, at::Dimname dim, bool keepdim, ::std::optional<at::ScalarType> dtype, at::Tensor & out);
} // namespace native
} // namespace at
