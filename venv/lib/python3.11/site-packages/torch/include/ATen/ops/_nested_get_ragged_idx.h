#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_nested_get_ragged_idx_ops.h>

namespace at {


// aten::_nested_get_ragged_idx(Tensor self) -> int
inline int64_t _nested_get_ragged_idx(const at::Tensor & self) {
    return at::_ops::_nested_get_ragged_idx::call(self);
}

}
