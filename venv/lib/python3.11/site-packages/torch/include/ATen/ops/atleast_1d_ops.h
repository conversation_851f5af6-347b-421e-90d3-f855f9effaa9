#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API atleast_1d {
  using schema = at::Tensor (const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::atleast_1d";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "atleast_1d(Tensor self) -> Tensor";
  static at::Tensor call(const at::Tensor & self);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self);
};

struct TORCH_API atleast_1d_Sequence {
  using schema = ::std::vector<at::Tensor> (at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::atleast_1d";
  static constexpr const char* overload_name = "Sequence";
  static constexpr const char* schema_str = "atleast_1d.Sequence(Tensor[] tensors) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList tensors);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList tensors);
};

}} // namespace at::_ops
