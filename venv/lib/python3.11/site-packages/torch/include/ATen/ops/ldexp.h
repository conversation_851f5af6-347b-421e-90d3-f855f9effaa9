#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/ldexp_ops.h>

namespace at {


// aten::ldexp.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor ldexp(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::ldexp_Tensor::call(self, other);
}

// aten::ldexp_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & ldexp_(at::Tensor & self, const at::Tensor & other) {
    return at::_ops::ldexp_::call(self, other);
}

// aten::ldexp.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & ldexp_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::ldexp_out::call(self, other, out);
}
// aten::ldexp.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & ldexp_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::ldexp_out::call(self, other, out);
}

}
