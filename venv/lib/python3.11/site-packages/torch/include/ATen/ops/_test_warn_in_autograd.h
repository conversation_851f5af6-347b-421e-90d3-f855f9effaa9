#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_test_warn_in_autograd_ops.h>

namespace at {


// aten::_test_warn_in_autograd(Tensor self) -> Tensor
inline at::Tensor _test_warn_in_autograd(const at::Tensor & self) {
    return at::_ops::_test_warn_in_autograd::call(self);
}

// aten::_test_warn_in_autograd.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _test_warn_in_autograd_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::_test_warn_in_autograd_out::call(self, out);
}
// aten::_test_warn_in_autograd.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _test_warn_in_autograd_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::_test_warn_in_autograd_out::call(self, out);
}

}
