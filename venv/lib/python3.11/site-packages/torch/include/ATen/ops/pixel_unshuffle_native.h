#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & pixel_unshuffle_out(const at::Tensor & self, int64_t downscale_factor, at::Tensor & out);
TORCH_API at::Tensor pixel_unshuffle_cpu(const at::Tensor & self, int64_t downscale_factor);
TORCH_API at::Tensor math_pixel_unshuffle(const at::Tensor & self, int64_t downscale_factor);
} // namespace native
} // namespace at
