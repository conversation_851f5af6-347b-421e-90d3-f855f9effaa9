#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/mul_ops.h>

namespace at {


// aten::mul.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor mul(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::mul_Tensor::call(self, other);
}

// aten::mul.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & mul_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::mul_out::call(self, other, out);
}
// aten::mul.out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & mul_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::mul_out::call(self, other, out);
}

// aten::mul.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor mul(const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::mul_Scalar::call(self, other);
}

// aten::mul.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & mul_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::mul_Scalar_out::call(self, other, out);
}
// aten::mul.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & mul_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out) {
    return at::_ops::mul_Scalar_out::call(self, other, out);
}

}
