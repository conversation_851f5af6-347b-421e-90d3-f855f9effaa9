/* Generated by hsaap.py */
/* Copyright (c) 2018-2022 Advanced Micro Devices, Inc.

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE. */


/* HSA API tracing primitives
 'CoreApi', header 'hsa.h', 125 funcs
 'AmdExt', header 'hsa_ext_amd.h', 70 funcs
 'ImageExt', header 'hsa_ext_image.h', 13 funcs
 'AmdExt', header 'hsa_api_trace.h', 70 funcs
 */

#ifndef HSA_PROF_STR_H_
#define HSA_PROF_STR_H_

/* section: API ID enumeration */

enum hsa_api_id_t {
  /* block: CoreApi API */
  HSA_API_ID_hsa_init = 0,
  HSA_API_ID_hsa_shut_down = 1,
  HSA_API_ID_hsa_system_get_info = 2,
  HSA_API_ID_hsa_system_extension_supported = 3,
  HSA_API_ID_hsa_system_get_extension_table = 4,
  HSA_API_ID_hsa_iterate_agents = 5,
  HSA_API_ID_hsa_agent_get_info = 6,
  HSA_API_ID_hsa_queue_create = 7,
  HSA_API_ID_hsa_soft_queue_create = 8,
  HSA_API_ID_hsa_queue_destroy = 9,
  HSA_API_ID_hsa_queue_inactivate = 10,
  HSA_API_ID_hsa_queue_load_read_index_scacquire = 11,
  HSA_API_ID_hsa_queue_load_read_index_relaxed = 12,
  HSA_API_ID_hsa_queue_load_write_index_scacquire = 13,
  HSA_API_ID_hsa_queue_load_write_index_relaxed = 14,
  HSA_API_ID_hsa_queue_store_write_index_relaxed = 15,
  HSA_API_ID_hsa_queue_store_write_index_screlease = 16,
  HSA_API_ID_hsa_queue_cas_write_index_scacq_screl = 17,
  HSA_API_ID_hsa_queue_cas_write_index_scacquire = 18,
  HSA_API_ID_hsa_queue_cas_write_index_relaxed = 19,
  HSA_API_ID_hsa_queue_cas_write_index_screlease = 20,
  HSA_API_ID_hsa_queue_add_write_index_scacq_screl = 21,
  HSA_API_ID_hsa_queue_add_write_index_scacquire = 22,
  HSA_API_ID_hsa_queue_add_write_index_relaxed = 23,
  HSA_API_ID_hsa_queue_add_write_index_screlease = 24,
  HSA_API_ID_hsa_queue_store_read_index_relaxed = 25,
  HSA_API_ID_hsa_queue_store_read_index_screlease = 26,
  HSA_API_ID_hsa_agent_iterate_regions = 27,
  HSA_API_ID_hsa_region_get_info = 28,
  HSA_API_ID_hsa_agent_get_exception_policies = 29,
  HSA_API_ID_hsa_agent_extension_supported = 30,
  HSA_API_ID_hsa_memory_register = 31,
  HSA_API_ID_hsa_memory_deregister = 32,
  HSA_API_ID_hsa_memory_allocate = 33,
  HSA_API_ID_hsa_memory_free = 34,
  HSA_API_ID_hsa_memory_copy = 35,
  HSA_API_ID_hsa_memory_assign_agent = 36,
  HSA_API_ID_hsa_signal_create = 37,
  HSA_API_ID_hsa_signal_destroy = 38,
  HSA_API_ID_hsa_signal_load_relaxed = 39,
  HSA_API_ID_hsa_signal_load_scacquire = 40,
  HSA_API_ID_hsa_signal_store_relaxed = 41,
  HSA_API_ID_hsa_signal_store_screlease = 42,
  HSA_API_ID_hsa_signal_wait_relaxed = 43,
  HSA_API_ID_hsa_signal_wait_scacquire = 44,
  HSA_API_ID_hsa_signal_and_relaxed = 45,
  HSA_API_ID_hsa_signal_and_scacquire = 46,
  HSA_API_ID_hsa_signal_and_screlease = 47,
  HSA_API_ID_hsa_signal_and_scacq_screl = 48,
  HSA_API_ID_hsa_signal_or_relaxed = 49,
  HSA_API_ID_hsa_signal_or_scacquire = 50,
  HSA_API_ID_hsa_signal_or_screlease = 51,
  HSA_API_ID_hsa_signal_or_scacq_screl = 52,
  HSA_API_ID_hsa_signal_xor_relaxed = 53,
  HSA_API_ID_hsa_signal_xor_scacquire = 54,
  HSA_API_ID_hsa_signal_xor_screlease = 55,
  HSA_API_ID_hsa_signal_xor_scacq_screl = 56,
  HSA_API_ID_hsa_signal_exchange_relaxed = 57,
  HSA_API_ID_hsa_signal_exchange_scacquire = 58,
  HSA_API_ID_hsa_signal_exchange_screlease = 59,
  HSA_API_ID_hsa_signal_exchange_scacq_screl = 60,
  HSA_API_ID_hsa_signal_add_relaxed = 61,
  HSA_API_ID_hsa_signal_add_scacquire = 62,
  HSA_API_ID_hsa_signal_add_screlease = 63,
  HSA_API_ID_hsa_signal_add_scacq_screl = 64,
  HSA_API_ID_hsa_signal_subtract_relaxed = 65,
  HSA_API_ID_hsa_signal_subtract_scacquire = 66,
  HSA_API_ID_hsa_signal_subtract_screlease = 67,
  HSA_API_ID_hsa_signal_subtract_scacq_screl = 68,
  HSA_API_ID_hsa_signal_cas_relaxed = 69,
  HSA_API_ID_hsa_signal_cas_scacquire = 70,
  HSA_API_ID_hsa_signal_cas_screlease = 71,
  HSA_API_ID_hsa_signal_cas_scacq_screl = 72,
  HSA_API_ID_hsa_isa_from_name = 73,
  HSA_API_ID_hsa_isa_get_info = 74,
  HSA_API_ID_hsa_isa_compatible = 75,
  HSA_API_ID_hsa_code_object_serialize = 76,
  HSA_API_ID_hsa_code_object_deserialize = 77,
  HSA_API_ID_hsa_code_object_destroy = 78,
  HSA_API_ID_hsa_code_object_get_info = 79,
  HSA_API_ID_hsa_code_object_get_symbol = 80,
  HSA_API_ID_hsa_code_symbol_get_info = 81,
  HSA_API_ID_hsa_code_object_iterate_symbols = 82,
  HSA_API_ID_hsa_executable_create = 83,
  HSA_API_ID_hsa_executable_destroy = 84,
  HSA_API_ID_hsa_executable_load_code_object = 85,
  HSA_API_ID_hsa_executable_freeze = 86,
  HSA_API_ID_hsa_executable_get_info = 87,
  HSA_API_ID_hsa_executable_global_variable_define = 88,
  HSA_API_ID_hsa_executable_agent_global_variable_define = 89,
  HSA_API_ID_hsa_executable_readonly_variable_define = 90,
  HSA_API_ID_hsa_executable_validate = 91,
  HSA_API_ID_hsa_executable_get_symbol = 92,
  HSA_API_ID_hsa_executable_symbol_get_info = 93,
  HSA_API_ID_hsa_executable_iterate_symbols = 94,
  HSA_API_ID_hsa_status_string = 95,
  HSA_API_ID_hsa_extension_get_name = 96,
  HSA_API_ID_hsa_system_major_extension_supported = 97,
  HSA_API_ID_hsa_system_get_major_extension_table = 98,
  HSA_API_ID_hsa_agent_major_extension_supported = 99,
  HSA_API_ID_hsa_cache_get_info = 100,
  HSA_API_ID_hsa_agent_iterate_caches = 101,
  HSA_API_ID_hsa_signal_silent_store_relaxed = 102,
  HSA_API_ID_hsa_signal_silent_store_screlease = 103,
  HSA_API_ID_hsa_signal_group_create = 104,
  HSA_API_ID_hsa_signal_group_destroy = 105,
  HSA_API_ID_hsa_signal_group_wait_any_scacquire = 106,
  HSA_API_ID_hsa_signal_group_wait_any_relaxed = 107,
  HSA_API_ID_hsa_agent_iterate_isas = 108,
  HSA_API_ID_hsa_isa_get_info_alt = 109,
  HSA_API_ID_hsa_isa_get_exception_policies = 110,
  HSA_API_ID_hsa_isa_get_round_method = 111,
  HSA_API_ID_hsa_wavefront_get_info = 112,
  HSA_API_ID_hsa_isa_iterate_wavefronts = 113,
  HSA_API_ID_hsa_code_object_get_symbol_from_name = 114,
  HSA_API_ID_hsa_code_object_reader_create_from_file = 115,
  HSA_API_ID_hsa_code_object_reader_create_from_memory = 116,
  HSA_API_ID_hsa_code_object_reader_destroy = 117,
  HSA_API_ID_hsa_executable_create_alt = 118,
  HSA_API_ID_hsa_executable_load_program_code_object = 119,
  HSA_API_ID_hsa_executable_load_agent_code_object = 120,
  HSA_API_ID_hsa_executable_validate_alt = 121,
  HSA_API_ID_hsa_executable_get_symbol_by_name = 122,
  HSA_API_ID_hsa_executable_iterate_agent_symbols = 123,
  HSA_API_ID_hsa_executable_iterate_program_symbols = 124,

  /* block: AmdExt API */
  HSA_API_ID_hsa_amd_coherency_get_type = 125,
  HSA_API_ID_hsa_amd_coherency_set_type = 126,
  HSA_API_ID_hsa_amd_profiling_set_profiler_enabled = 127,
  HSA_API_ID_hsa_amd_profiling_async_copy_enable = 128,
  HSA_API_ID_hsa_amd_profiling_get_dispatch_time = 129,
  HSA_API_ID_hsa_amd_profiling_get_async_copy_time = 130,
  HSA_API_ID_hsa_amd_profiling_convert_tick_to_system_domain = 131,
  HSA_API_ID_hsa_amd_signal_async_handler = 132,
  HSA_API_ID_hsa_amd_async_function = 133,
  HSA_API_ID_hsa_amd_signal_wait_any = 134,
  HSA_API_ID_hsa_amd_queue_cu_set_mask = 135,
  HSA_API_ID_hsa_amd_memory_pool_get_info = 136,
  HSA_API_ID_hsa_amd_agent_iterate_memory_pools = 137,
  HSA_API_ID_hsa_amd_memory_pool_allocate = 138,
  HSA_API_ID_hsa_amd_memory_pool_free = 139,
  HSA_API_ID_hsa_amd_memory_async_copy = 140,
  HSA_API_ID_hsa_amd_memory_async_copy_on_engine = 141,
  HSA_API_ID_hsa_amd_memory_copy_engine_status = 142,
  HSA_API_ID_hsa_amd_agent_memory_pool_get_info = 143,
  HSA_API_ID_hsa_amd_agents_allow_access = 144,
  HSA_API_ID_hsa_amd_memory_pool_can_migrate = 145,
  HSA_API_ID_hsa_amd_memory_migrate = 146,
  HSA_API_ID_hsa_amd_memory_lock = 147,
  HSA_API_ID_hsa_amd_memory_unlock = 148,
  HSA_API_ID_hsa_amd_memory_fill = 149,
  HSA_API_ID_hsa_amd_interop_map_buffer = 150,
  HSA_API_ID_hsa_amd_interop_unmap_buffer = 151,
  HSA_API_ID_hsa_amd_image_create = 152,
  HSA_API_ID_hsa_amd_pointer_info = 153,
  HSA_API_ID_hsa_amd_pointer_info_set_userdata = 154,
  HSA_API_ID_hsa_amd_ipc_memory_create = 155,
  HSA_API_ID_hsa_amd_ipc_memory_attach = 156,
  HSA_API_ID_hsa_amd_ipc_memory_detach = 157,
  HSA_API_ID_hsa_amd_signal_create = 158,
  HSA_API_ID_hsa_amd_ipc_signal_create = 159,
  HSA_API_ID_hsa_amd_ipc_signal_attach = 160,
  HSA_API_ID_hsa_amd_register_system_event_handler = 161,
  HSA_API_ID_hsa_amd_queue_intercept_create = 162,
  HSA_API_ID_hsa_amd_queue_intercept_register = 163,
  HSA_API_ID_hsa_amd_queue_set_priority = 164,
  HSA_API_ID_hsa_amd_memory_async_copy_rect = 165,
  HSA_API_ID_hsa_amd_runtime_queue_create_register = 166,
  HSA_API_ID_hsa_amd_memory_lock_to_pool = 167,
  HSA_API_ID_hsa_amd_register_deallocation_callback = 168,
  HSA_API_ID_hsa_amd_deregister_deallocation_callback = 169,
  HSA_API_ID_hsa_amd_signal_value_pointer = 170,
  HSA_API_ID_hsa_amd_svm_attributes_set = 171,
  HSA_API_ID_hsa_amd_svm_attributes_get = 172,
  HSA_API_ID_hsa_amd_svm_prefetch_async = 173,
  HSA_API_ID_hsa_amd_spm_acquire = 174,
  HSA_API_ID_hsa_amd_spm_release = 175,
  HSA_API_ID_hsa_amd_spm_set_dest_buffer = 176,
  HSA_API_ID_hsa_amd_queue_cu_get_mask = 177,
  HSA_API_ID_hsa_amd_portable_export_dmabuf = 178,
  HSA_API_ID_hsa_amd_portable_close_dmabuf = 179,
  HSA_API_ID_hsa_amd_vmem_address_reserve = 180,
  HSA_API_ID_hsa_amd_vmem_address_free = 181,
  HSA_API_ID_hsa_amd_vmem_handle_create = 182,
  HSA_API_ID_hsa_amd_vmem_handle_release = 183,
  HSA_API_ID_hsa_amd_vmem_map = 184,
  HSA_API_ID_hsa_amd_vmem_unmap = 185,
  HSA_API_ID_hsa_amd_vmem_set_access = 186,
  HSA_API_ID_hsa_amd_vmem_get_access = 187,
  HSA_API_ID_hsa_amd_vmem_export_shareable_handle = 188,
  HSA_API_ID_hsa_amd_vmem_import_shareable_handle = 189,
  HSA_API_ID_hsa_amd_vmem_retain_alloc_handle = 190,
  HSA_API_ID_hsa_amd_vmem_get_alloc_properties_from_handle = 191,
  HSA_API_ID_hsa_amd_agent_set_async_scratch_limit = 192,
  HSA_API_ID_hsa_amd_queue_get_info = 193,
  HSA_API_ID_hsa_amd_vmem_address_reserve_align = 194,

  /* block: ImageExt API */
  HSA_API_ID_hsa_ext_image_get_capability = 195,
  HSA_API_ID_hsa_ext_image_data_get_info = 196,
  HSA_API_ID_hsa_ext_image_create = 197,
  HSA_API_ID_hsa_ext_image_import = 198,
  HSA_API_ID_hsa_ext_image_export = 199,
  HSA_API_ID_hsa_ext_image_copy = 200,
  HSA_API_ID_hsa_ext_image_clear = 201,
  HSA_API_ID_hsa_ext_image_destroy = 202,
  HSA_API_ID_hsa_ext_sampler_create = 203,
  HSA_API_ID_hsa_ext_sampler_destroy = 204,
  HSA_API_ID_hsa_ext_image_get_capability_with_layout = 205,
  HSA_API_ID_hsa_ext_image_data_get_info_with_layout = 206,
  HSA_API_ID_hsa_ext_image_create_with_layout = 207,

  HSA_API_ID_DISPATCH = 208,
  HSA_API_ID_NUMBER = 209,
};
/* Declarations of APIs intended for use only by tools. */
typedef void (*hsa_amd_queue_intercept_packet_writer)(const void*, uint64_t);
typedef void (*hsa_amd_queue_intercept_handler)(const void*, uint64_t, uint64_t, void*,
                                                hsa_amd_queue_intercept_packet_writer);
typedef void (*hsa_amd_runtime_queue_notifier)(const hsa_queue_t*, hsa_agent_t, void*);

/* section: API arg structure */

struct hsa_api_data_t {
  uint64_t correlation_id;
  uint32_t phase;
  union {
    uint64_t uint64_t_retval;
    hsa_status_t hsa_status_t_retval;
    hsa_signal_value_t hsa_signal_value_t_retval;
    uint32_t uint32_t_retval;
  };
  union {
    /* block: CoreApi API */
    struct {
    } hsa_init;
    struct {
    } hsa_shut_down;
    struct {
      hsa_system_info_t attribute;
      void* value;
    } hsa_system_get_info;
    struct {
      uint16_t extension;
      uint16_t version_major;
      uint16_t version_minor;
      bool* result;
    } hsa_system_extension_supported;
    struct {
      uint16_t extension;
      uint16_t version_major;
      uint16_t version_minor;
      void* table;
    } hsa_system_get_extension_table;
    struct {
      hsa_status_t (* callback)(hsa_agent_t agent,void* data);
      void* data;
    } hsa_iterate_agents;
    struct {
      hsa_agent_t agent;
      hsa_agent_info_t attribute;
      void* value;
    } hsa_agent_get_info;
    struct {
      hsa_agent_t agent;
      uint32_t size;
      hsa_queue_type32_t type;
      void (* callback)(hsa_status_t status,hsa_queue_t* source,void* data);
      void* data;
      uint32_t private_segment_size;
      uint32_t group_segment_size;
      hsa_queue_t** queue;
    } hsa_queue_create;
    struct {
      hsa_region_t region;
      uint32_t size;
      hsa_queue_type32_t type;
      uint32_t features;
      hsa_signal_t doorbell_signal;
      hsa_queue_t** queue;
    } hsa_soft_queue_create;
    struct {
      hsa_queue_t* queue;
    } hsa_queue_destroy;
    struct {
      hsa_queue_t* queue;
    } hsa_queue_inactivate;
    struct {
      const hsa_queue_t* queue;
    } hsa_queue_load_read_index_scacquire;
    struct {
      const hsa_queue_t* queue;
    } hsa_queue_load_read_index_relaxed;
    struct {
      const hsa_queue_t* queue;
    } hsa_queue_load_write_index_scacquire;
    struct {
      const hsa_queue_t* queue;
    } hsa_queue_load_write_index_relaxed;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_store_write_index_relaxed;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_store_write_index_screlease;
    struct {
      const hsa_queue_t* queue;
      uint64_t expected;
      uint64_t value;
    } hsa_queue_cas_write_index_scacq_screl;
    struct {
      const hsa_queue_t* queue;
      uint64_t expected;
      uint64_t value;
    } hsa_queue_cas_write_index_scacquire;
    struct {
      const hsa_queue_t* queue;
      uint64_t expected;
      uint64_t value;
    } hsa_queue_cas_write_index_relaxed;
    struct {
      const hsa_queue_t* queue;
      uint64_t expected;
      uint64_t value;
    } hsa_queue_cas_write_index_screlease;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_add_write_index_scacq_screl;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_add_write_index_scacquire;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_add_write_index_relaxed;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_add_write_index_screlease;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_store_read_index_relaxed;
    struct {
      const hsa_queue_t* queue;
      uint64_t value;
    } hsa_queue_store_read_index_screlease;
    struct {
      hsa_agent_t agent;
      hsa_status_t (* callback)(hsa_region_t region,void* data);
      void* data;
    } hsa_agent_iterate_regions;
    struct {
      hsa_region_t region;
      hsa_region_info_t attribute;
      void* value;
    } hsa_region_get_info;
    struct {
      hsa_agent_t agent;
      hsa_profile_t profile;
      uint16_t* mask;
    } hsa_agent_get_exception_policies;
    struct {
      uint16_t extension;
      hsa_agent_t agent;
      uint16_t version_major;
      uint16_t version_minor;
      bool* result;
    } hsa_agent_extension_supported;
    struct {
      void* ptr;
      size_t size;
    } hsa_memory_register;
    struct {
      void* ptr;
      size_t size;
    } hsa_memory_deregister;
    struct {
      hsa_region_t region;
      size_t size;
      void** ptr;
    } hsa_memory_allocate;
    struct {
      void* ptr;
    } hsa_memory_free;
    struct {
      void* dst;
      const void* src;
      size_t size;
    } hsa_memory_copy;
    struct {
      void* ptr;
      hsa_agent_t agent;
      hsa_access_permission_t access;
    } hsa_memory_assign_agent;
    struct {
      hsa_signal_value_t initial_value;
      uint32_t num_consumers;
      const hsa_agent_t* consumers;
      hsa_signal_t* signal;
    } hsa_signal_create;
    struct {
      hsa_signal_t signal;
    } hsa_signal_destroy;
    struct {
      hsa_signal_t signal;
    } hsa_signal_load_relaxed;
    struct {
      hsa_signal_t signal;
    } hsa_signal_load_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_store_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_store_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_condition_t condition;
      hsa_signal_value_t compare_value;
      uint64_t timeout_hint;
      hsa_wait_state_t wait_state_hint;
    } hsa_signal_wait_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_condition_t condition;
      hsa_signal_value_t compare_value;
      uint64_t timeout_hint;
      hsa_wait_state_t wait_state_hint;
    } hsa_signal_wait_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_and_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_and_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_and_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_and_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_or_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_or_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_or_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_or_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_xor_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_xor_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_xor_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_xor_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_exchange_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_exchange_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_exchange_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_exchange_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_add_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_add_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_add_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_add_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_subtract_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_subtract_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_subtract_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_subtract_scacq_screl;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t expected;
      hsa_signal_value_t value;
    } hsa_signal_cas_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t expected;
      hsa_signal_value_t value;
    } hsa_signal_cas_scacquire;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t expected;
      hsa_signal_value_t value;
    } hsa_signal_cas_screlease;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t expected;
      hsa_signal_value_t value;
    } hsa_signal_cas_scacq_screl;
    struct {
      const char* name;
      hsa_isa_t* isa;
    } hsa_isa_from_name;
    struct {
      hsa_isa_t isa;
      hsa_isa_info_t attribute;
      uint32_t index;
      void* value;
    } hsa_isa_get_info;
    struct {
      hsa_isa_t code_object_isa;
      hsa_isa_t agent_isa;
      bool* result;
    } hsa_isa_compatible;
    struct {
      hsa_code_object_t code_object;
      hsa_status_t (* alloc_callback)(size_t size,hsa_callback_data_t data,void** address);
      hsa_callback_data_t callback_data;
      const char* options;
      void** serialized_code_object;
      size_t* serialized_code_object_size;
    } hsa_code_object_serialize;
    struct {
      void* serialized_code_object;
      size_t serialized_code_object_size;
      const char* options;
      hsa_code_object_t* code_object;
    } hsa_code_object_deserialize;
    struct {
      hsa_code_object_t code_object;
    } hsa_code_object_destroy;
    struct {
      hsa_code_object_t code_object;
      hsa_code_object_info_t attribute;
      void* value;
    } hsa_code_object_get_info;
    struct {
      hsa_code_object_t code_object;
      const char* symbol_name;
      hsa_code_symbol_t* symbol;
    } hsa_code_object_get_symbol;
    struct {
      hsa_code_symbol_t code_symbol;
      hsa_code_symbol_info_t attribute;
      void* value;
    } hsa_code_symbol_get_info;
    struct {
      hsa_code_object_t code_object;
      hsa_status_t (* callback)(hsa_code_object_t code_object,hsa_code_symbol_t symbol,void* data);
      void* data;
    } hsa_code_object_iterate_symbols;
    struct {
      hsa_profile_t profile;
      hsa_executable_state_t executable_state;
      const char* options;
      hsa_executable_t* executable;
    } hsa_executable_create;
    struct {
      hsa_executable_t executable;
    } hsa_executable_destroy;
    struct {
      hsa_executable_t executable;
      hsa_agent_t agent;
      hsa_code_object_t code_object;
      const char* options;
    } hsa_executable_load_code_object;
    struct {
      hsa_executable_t executable;
      const char* options;
    } hsa_executable_freeze;
    struct {
      hsa_executable_t executable;
      hsa_executable_info_t attribute;
      void* value;
    } hsa_executable_get_info;
    struct {
      hsa_executable_t executable;
      const char* variable_name;
      void* address;
    } hsa_executable_global_variable_define;
    struct {
      hsa_executable_t executable;
      hsa_agent_t agent;
      const char* variable_name;
      void* address;
    } hsa_executable_agent_global_variable_define;
    struct {
      hsa_executable_t executable;
      hsa_agent_t agent;
      const char* variable_name;
      void* address;
    } hsa_executable_readonly_variable_define;
    struct {
      hsa_executable_t executable;
      uint32_t* result;
    } hsa_executable_validate;
    struct {
      hsa_executable_t executable;
      const char* module_name;
      const char* symbol_name;
      hsa_agent_t agent;
      int32_t call_convention;
      hsa_executable_symbol_t* symbol;
    } hsa_executable_get_symbol;
    struct {
      hsa_executable_symbol_t executable_symbol;
      hsa_executable_symbol_info_t attribute;
      void* value;
    } hsa_executable_symbol_get_info;
    struct {
      hsa_executable_t executable;
      hsa_status_t (* callback)(hsa_executable_t exec,hsa_executable_symbol_t symbol,void* data);
      void* data;
    } hsa_executable_iterate_symbols;
    struct {
      hsa_status_t status;
      const char** status_string;
    } hsa_status_string;
    struct {
      uint16_t extension;
      const char** name;
    } hsa_extension_get_name;
    struct {
      uint16_t extension;
      uint16_t version_major;
      uint16_t* version_minor;
      bool* result;
    } hsa_system_major_extension_supported;
    struct {
      uint16_t extension;
      uint16_t version_major;
      size_t table_length;
      void* table;
    } hsa_system_get_major_extension_table;
    struct {
      uint16_t extension;
      hsa_agent_t agent;
      uint16_t version_major;
      uint16_t* version_minor;
      bool* result;
    } hsa_agent_major_extension_supported;
    struct {
      hsa_cache_t cache;
      hsa_cache_info_t attribute;
      void* value;
    } hsa_cache_get_info;
    struct {
      hsa_agent_t agent;
      hsa_status_t (* callback)(hsa_cache_t cache,void* data);
      void* data;
    } hsa_agent_iterate_caches;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_silent_store_relaxed;
    struct {
      hsa_signal_t signal;
      hsa_signal_value_t value;
    } hsa_signal_silent_store_screlease;
    struct {
      uint32_t num_signals;
      const hsa_signal_t* signals;
      uint32_t num_consumers;
      const hsa_agent_t* consumers;
      hsa_signal_group_t* signal_group;
    } hsa_signal_group_create;
    struct {
      hsa_signal_group_t signal_group;
    } hsa_signal_group_destroy;
    struct {
      hsa_signal_group_t signal_group;
      const hsa_signal_condition_t* conditions;
      const hsa_signal_value_t* compare_values;
      hsa_wait_state_t wait_state_hint;
      hsa_signal_t* signal;
      hsa_signal_value_t* value;
    } hsa_signal_group_wait_any_scacquire;
    struct {
      hsa_signal_group_t signal_group;
      const hsa_signal_condition_t* conditions;
      const hsa_signal_value_t* compare_values;
      hsa_wait_state_t wait_state_hint;
      hsa_signal_t* signal;
      hsa_signal_value_t* value;
    } hsa_signal_group_wait_any_relaxed;
    struct {
      hsa_agent_t agent;
      hsa_status_t (* callback)(hsa_isa_t isa,void* data);
      void* data;
    } hsa_agent_iterate_isas;
    struct {
      hsa_isa_t isa;
      hsa_isa_info_t attribute;
      void* value;
    } hsa_isa_get_info_alt;
    struct {
      hsa_isa_t isa;
      hsa_profile_t profile;
      uint16_t* mask;
    } hsa_isa_get_exception_policies;
    struct {
      hsa_isa_t isa;
      hsa_fp_type_t fp_type;
      hsa_flush_mode_t flush_mode;
      hsa_round_method_t* round_method;
    } hsa_isa_get_round_method;
    struct {
      hsa_wavefront_t wavefront;
      hsa_wavefront_info_t attribute;
      void* value;
    } hsa_wavefront_get_info;
    struct {
      hsa_isa_t isa;
      hsa_status_t (* callback)(hsa_wavefront_t wavefront,void* data);
      void* data;
    } hsa_isa_iterate_wavefronts;
    struct {
      hsa_code_object_t code_object;
      const char* module_name;
      const char* symbol_name;
      hsa_code_symbol_t* symbol;
    } hsa_code_object_get_symbol_from_name;
    struct {
      hsa_file_t file;
      hsa_code_object_reader_t* code_object_reader;
    } hsa_code_object_reader_create_from_file;
    struct {
      const void* code_object;
      size_t size;
      hsa_code_object_reader_t* code_object_reader;
    } hsa_code_object_reader_create_from_memory;
    struct {
      hsa_code_object_reader_t code_object_reader;
    } hsa_code_object_reader_destroy;
    struct {
      hsa_profile_t profile;
      hsa_default_float_rounding_mode_t default_float_rounding_mode;
      const char* options;
      hsa_executable_t* executable;
    } hsa_executable_create_alt;
    struct {
      hsa_executable_t executable;
      hsa_code_object_reader_t code_object_reader;
      const char* options;
      hsa_loaded_code_object_t* loaded_code_object;
    } hsa_executable_load_program_code_object;
    struct {
      hsa_executable_t executable;
      hsa_agent_t agent;
      hsa_code_object_reader_t code_object_reader;
      const char* options;
      hsa_loaded_code_object_t* loaded_code_object;
    } hsa_executable_load_agent_code_object;
    struct {
      hsa_executable_t executable;
      const char* options;
      uint32_t* result;
    } hsa_executable_validate_alt;
    struct {
      hsa_executable_t executable;
      const char* symbol_name;
      const hsa_agent_t* agent;
      hsa_executable_symbol_t* symbol;
    } hsa_executable_get_symbol_by_name;
    struct {
      hsa_executable_t executable;
      hsa_agent_t agent;
      hsa_status_t (* callback)(hsa_executable_t exec,hsa_agent_t agent,hsa_executable_symbol_t symbol,void* data);
      void* data;
    } hsa_executable_iterate_agent_symbols;
    struct {
      hsa_executable_t executable;
      hsa_status_t (* callback)(hsa_executable_t exec,hsa_executable_symbol_t symbol,void* data);
      void* data;
    } hsa_executable_iterate_program_symbols;

    /* block: AmdExt API */
    struct {
      hsa_agent_t agent;
      hsa_amd_coherency_type_t* type;
    } hsa_amd_coherency_get_type;
    struct {
      hsa_agent_t agent;
      hsa_amd_coherency_type_t type;
    } hsa_amd_coherency_set_type;
    struct {
      hsa_queue_t* queue;
      int enable;
    } hsa_amd_profiling_set_profiler_enabled;
    struct {
      bool enable;
    } hsa_amd_profiling_async_copy_enable;
    struct {
      hsa_agent_t agent;
      hsa_signal_t signal;
      hsa_amd_profiling_dispatch_time_t* time;
    } hsa_amd_profiling_get_dispatch_time;
    struct {
      hsa_signal_t signal;
      hsa_amd_profiling_async_copy_time_t* time;
    } hsa_amd_profiling_get_async_copy_time;
    struct {
      hsa_agent_t agent;
      uint64_t agent_tick;
      uint64_t* system_tick;
    } hsa_amd_profiling_convert_tick_to_system_domain;
    struct {
      hsa_signal_t signal;
      hsa_signal_condition_t cond;
      hsa_signal_value_t value;
      hsa_amd_signal_handler handler;
      void* arg;
    } hsa_amd_signal_async_handler;
    struct {
      void (* callback)(void* arg);
      void* arg;
    } hsa_amd_async_function;
    struct {
      uint32_t signal_count;
      hsa_signal_t* signals;
      hsa_signal_condition_t* conds;
      hsa_signal_value_t* values;
      uint64_t timeout_hint;
      hsa_wait_state_t wait_hint;
      hsa_signal_value_t* satisfying_value;
    } hsa_amd_signal_wait_any;
    struct {
      const hsa_queue_t* queue;
      uint32_t num_cu_mask_count;
      const uint32_t* cu_mask;
    } hsa_amd_queue_cu_set_mask;
    struct {
      hsa_amd_memory_pool_t memory_pool;
      hsa_amd_memory_pool_info_t attribute;
      void* value;
    } hsa_amd_memory_pool_get_info;
    struct {
      hsa_agent_t agent;
      hsa_status_t (* callback)(hsa_amd_memory_pool_t memory_pool,void* data);
      void* data;
    } hsa_amd_agent_iterate_memory_pools;
    struct {
      hsa_amd_memory_pool_t memory_pool;
      size_t size;
      uint32_t flags;
      void** ptr;
    } hsa_amd_memory_pool_allocate;
    struct {
      void* ptr;
    } hsa_amd_memory_pool_free;
    struct {
      void* dst;
      hsa_agent_t dst_agent;
      const void* src;
      hsa_agent_t src_agent;
      size_t size;
      uint32_t num_dep_signals;
      const hsa_signal_t* dep_signals;
      hsa_signal_t completion_signal;
    } hsa_amd_memory_async_copy;
    struct {
      void* dst;
      hsa_agent_t dst_agent;
      const void* src;
      hsa_agent_t src_agent;
      size_t size;
      uint32_t num_dep_signals;
      const hsa_signal_t* dep_signals;
      hsa_signal_t completion_signal;
      hsa_amd_sdma_engine_id_t engine_id;
      bool force_copy_on_sdma;
    } hsa_amd_memory_async_copy_on_engine;
    struct {
      hsa_agent_t dst_agent;
      hsa_agent_t src_agent;
      uint32_t* engine_ids_mask;
    } hsa_amd_memory_copy_engine_status;
    struct {
      hsa_agent_t agent;
      hsa_amd_memory_pool_t memory_pool;
      hsa_amd_agent_memory_pool_info_t attribute;
      void* value;
    } hsa_amd_agent_memory_pool_get_info;
    struct {
      uint32_t num_agents;
      const hsa_agent_t* agents;
      const uint32_t* flags;
      const void* ptr;
    } hsa_amd_agents_allow_access;
    struct {
      hsa_amd_memory_pool_t src_memory_pool;
      hsa_amd_memory_pool_t dst_memory_pool;
      bool* result;
    } hsa_amd_memory_pool_can_migrate;
    struct {
      const void* ptr;
      hsa_amd_memory_pool_t memory_pool;
      uint32_t flags;
    } hsa_amd_memory_migrate;
    struct {
      void* host_ptr;
      size_t size;
      hsa_agent_t* agents;
      int num_agent;
      void** agent_ptr;
    } hsa_amd_memory_lock;
    struct {
      void* host_ptr;
    } hsa_amd_memory_unlock;
    struct {
      void* ptr;
      uint32_t value;
      size_t count;
    } hsa_amd_memory_fill;
    struct {
      uint32_t num_agents;
      hsa_agent_t* agents;
      int interop_handle;
      uint32_t flags;
      size_t* size;
      void** ptr;
      size_t* metadata_size;
      const void** metadata;
    } hsa_amd_interop_map_buffer;
    struct {
      void* ptr;
    } hsa_amd_interop_unmap_buffer;
    struct {
      hsa_agent_t agent;
      const hsa_ext_image_descriptor_t* image_descriptor;
      const hsa_amd_image_descriptor_t* image_layout;
      const void* image_data;
      hsa_access_permission_t access_permission;
      hsa_ext_image_t* image;
    } hsa_amd_image_create;
    struct {
      const void* ptr;
      hsa_amd_pointer_info_t* info;
      void* (* alloc)(size_t);
      uint32_t* num_agents_accessible;
      hsa_agent_t** accessible;
    } hsa_amd_pointer_info;
    struct {
      const void* ptr;
      void* userdata;
    } hsa_amd_pointer_info_set_userdata;
    struct {
      void* ptr;
      size_t len;
      hsa_amd_ipc_memory_t* handle;
    } hsa_amd_ipc_memory_create;
    struct {
      const hsa_amd_ipc_memory_t* handle;
      size_t len;
      uint32_t num_agents;
      const hsa_agent_t* mapping_agents;
      void** mapped_ptr;
    } hsa_amd_ipc_memory_attach;
    struct {
      void* mapped_ptr;
    } hsa_amd_ipc_memory_detach;
    struct {
      hsa_signal_value_t initial_value;
      uint32_t num_consumers;
      const hsa_agent_t* consumers;
      uint64_t attributes;
      hsa_signal_t* signal;
    } hsa_amd_signal_create;
    struct {
      hsa_signal_t signal;
      hsa_amd_ipc_signal_t* handle;
    } hsa_amd_ipc_signal_create;
    struct {
      const hsa_amd_ipc_signal_t* handle;
      hsa_signal_t* signal;
    } hsa_amd_ipc_signal_attach;
    struct {
      hsa_amd_system_event_callback_t callback;
      void* data;
    } hsa_amd_register_system_event_handler;
    struct {
      hsa_agent_t agent_handle;
      uint32_t size;
      hsa_queue_type32_t type;
      void (* callback)(hsa_status_t status,hsa_queue_t* source,void* data);
      void* data;
      uint32_t private_segment_size;
      uint32_t group_segment_size;
      hsa_queue_t** queue;
    } hsa_amd_queue_intercept_create;
    struct {
      hsa_queue_t* queue;
      hsa_amd_queue_intercept_handler callback;
      void* user_data;
    } hsa_amd_queue_intercept_register;
    struct {
      hsa_queue_t* queue;
      hsa_amd_queue_priority_t priority;
    } hsa_amd_queue_set_priority;
    struct {
      const hsa_pitched_ptr_t* dst;
      const hsa_dim3_t* dst_offset;
      const hsa_pitched_ptr_t* src;
      const hsa_dim3_t* src_offset;
      const hsa_dim3_t* range;
      hsa_dim3_t range__val;
      hsa_agent_t copy_agent;
      hsa_amd_copy_direction_t dir;
      uint32_t num_dep_signals;
      const hsa_signal_t* dep_signals;
      hsa_signal_t completion_signal;
    } hsa_amd_memory_async_copy_rect;
    struct {
      hsa_amd_runtime_queue_notifier callback;
      void* user_data;
    } hsa_amd_runtime_queue_create_register;
    struct {
      void* host_ptr;
      size_t size;
      hsa_agent_t* agents;
      int num_agent;
      hsa_amd_memory_pool_t pool;
      uint32_t flags;
      void** agent_ptr;
    } hsa_amd_memory_lock_to_pool;
    struct {
      void* ptr;
      hsa_amd_deallocation_callback_t callback;
      void* user_data;
    } hsa_amd_register_deallocation_callback;
    struct {
      void* ptr;
      hsa_amd_deallocation_callback_t callback;
    } hsa_amd_deregister_deallocation_callback;
    struct {
      hsa_signal_t signal;
      volatile hsa_signal_value_t** value_ptr;
    } hsa_amd_signal_value_pointer;
    struct {
      void* ptr;
      size_t size;
      hsa_amd_svm_attribute_pair_t* attribute_list;
      size_t attribute_count;
    } hsa_amd_svm_attributes_set;
    struct {
      void* ptr;
      size_t size;
      hsa_amd_svm_attribute_pair_t* attribute_list;
      size_t attribute_count;
    } hsa_amd_svm_attributes_get;
    struct {
      void* ptr;
      size_t size;
      hsa_agent_t agent;
      uint32_t num_dep_signals;
      const hsa_signal_t* dep_signals;
      hsa_signal_t completion_signal;
    } hsa_amd_svm_prefetch_async;
    struct {
      hsa_agent_t preferred_agent;
    } hsa_amd_spm_acquire;
    struct {
      hsa_agent_t preferred_agent;
    } hsa_amd_spm_release;
    struct {
      hsa_agent_t preferred_agent;
      size_t size_in_bytes;
      uint32_t* timeout;
      uint32_t* size_copied;
      void* dest;
      bool* is_data_loss;
    } hsa_amd_spm_set_dest_buffer;
    struct {
      const hsa_queue_t* queue;
      uint32_t num_cu_mask_count;
      uint32_t* cu_mask;
    } hsa_amd_queue_cu_get_mask;
    struct {
      const void* ptr;
      size_t size;
      int* dmabuf;
      uint64_t* offset;
    } hsa_amd_portable_export_dmabuf;
    struct {
      int dmabuf;
    } hsa_amd_portable_close_dmabuf;
    struct {
      void** va;
      size_t size;
      uint64_t address;
      uint64_t flags;
    } hsa_amd_vmem_address_reserve;
    struct {
      void* va;
      size_t size;
    } hsa_amd_vmem_address_free;
    struct {
      hsa_amd_memory_pool_t pool;
      size_t size;
      hsa_amd_memory_type_t type;
      uint64_t flags;
      hsa_amd_vmem_alloc_handle_t* memory_handle;
    } hsa_amd_vmem_handle_create;
    struct {
      hsa_amd_vmem_alloc_handle_t memory_handle;
    } hsa_amd_vmem_handle_release;
    struct {
      void* va;
      size_t size;
      size_t in_offset;
      hsa_amd_vmem_alloc_handle_t memory_handle;
      uint64_t flags;
    } hsa_amd_vmem_map;
    struct {
      void* va;
      size_t size;
    } hsa_amd_vmem_unmap;
    struct {
      void* va;
      size_t size;
      const hsa_amd_memory_access_desc_t* desc;
      size_t desc_cnt;
    } hsa_amd_vmem_set_access;
    struct {
      void* va;
      hsa_access_permission_t* perms;
      hsa_agent_t agent_handle;
    } hsa_amd_vmem_get_access;
    struct {
      int* dmabuf_fd;
      hsa_amd_vmem_alloc_handle_t handle;
      uint64_t flags;
    } hsa_amd_vmem_export_shareable_handle;
    struct {
      int dmabuf_fd;
      hsa_amd_vmem_alloc_handle_t* handle;
    } hsa_amd_vmem_import_shareable_handle;
    struct {
      hsa_amd_vmem_alloc_handle_t* memory_handle;
      void* addr;
    } hsa_amd_vmem_retain_alloc_handle;
    struct {
      hsa_amd_vmem_alloc_handle_t memory_handle;
      hsa_amd_memory_pool_t* pool;
      hsa_amd_memory_type_t* type;
    } hsa_amd_vmem_get_alloc_properties_from_handle;
    struct {
      hsa_agent_t agent;
      size_t threshold;
    } hsa_amd_agent_set_async_scratch_limit;
    struct {
      hsa_queue_t* queue;
      hsa_queue_info_attribute_t attribute;
      void* value;
    } hsa_amd_queue_get_info;
    struct {
      void** va;
      size_t size;
      uint64_t address;
      uint64_t alignment;
      uint64_t flags;
    } hsa_amd_vmem_address_reserve_align;

    /* block: ImageExt API */
    struct {
      hsa_agent_t agent;
      hsa_ext_image_geometry_t geometry;
      const hsa_ext_image_format_t* image_format;
      uint32_t* capability_mask;
    } hsa_ext_image_get_capability;
    struct {
      hsa_agent_t agent;
      const hsa_ext_image_descriptor_t* image_descriptor;
      hsa_access_permission_t access_permission;
      hsa_ext_image_data_info_t* image_data_info;
    } hsa_ext_image_data_get_info;
    struct {
      hsa_agent_t agent;
      const hsa_ext_image_descriptor_t* image_descriptor;
      const void* image_data;
      hsa_access_permission_t access_permission;
      hsa_ext_image_t* image;
    } hsa_ext_image_create;
    struct {
      hsa_agent_t agent;
      const void* src_memory;
      size_t src_row_pitch;
      size_t src_slice_pitch;
      hsa_ext_image_t dst_image;
      const hsa_ext_image_region_t* image_region;
    } hsa_ext_image_import;
    struct {
      hsa_agent_t agent;
      hsa_ext_image_t src_image;
      void* dst_memory;
      size_t dst_row_pitch;
      size_t dst_slice_pitch;
      const hsa_ext_image_region_t* image_region;
    } hsa_ext_image_export;
    struct {
      hsa_agent_t agent;
      hsa_ext_image_t src_image;
      const hsa_dim3_t* src_offset;
      hsa_ext_image_t dst_image;
      const hsa_dim3_t* dst_offset;
      const hsa_dim3_t* range;
    } hsa_ext_image_copy;
    struct {
      hsa_agent_t agent;
      hsa_ext_image_t image;
      const void* data;
      const hsa_ext_image_region_t* image_region;
    } hsa_ext_image_clear;
    struct {
      hsa_agent_t agent;
      hsa_ext_image_t image;
    } hsa_ext_image_destroy;
    struct {
      hsa_agent_t agent;
      const hsa_ext_sampler_descriptor_t* sampler_descriptor;
      hsa_ext_sampler_t* sampler;
    } hsa_ext_sampler_create;
    struct {
      hsa_agent_t agent;
      hsa_ext_sampler_t sampler;
    } hsa_ext_sampler_destroy;
    struct {
      hsa_agent_t agent;
      hsa_ext_image_geometry_t geometry;
      const hsa_ext_image_format_t* image_format;
      hsa_ext_image_data_layout_t image_data_layout;
      uint32_t* capability_mask;
    } hsa_ext_image_get_capability_with_layout;
    struct {
      hsa_agent_t agent;
      const hsa_ext_image_descriptor_t* image_descriptor;
      hsa_access_permission_t access_permission;
      hsa_ext_image_data_layout_t image_data_layout;
      size_t image_data_row_pitch;
      size_t image_data_slice_pitch;
      hsa_ext_image_data_info_t* image_data_info;
    } hsa_ext_image_data_get_info_with_layout;
    struct {
      hsa_agent_t agent;
      const hsa_ext_image_descriptor_t* image_descriptor;
      const void* image_data;
      hsa_access_permission_t access_permission;
      hsa_ext_image_data_layout_t image_data_layout;
      size_t image_data_row_pitch;
      size_t image_data_slice_pitch;
      hsa_ext_image_t* image;
    } hsa_ext_image_create_with_layout;
  } args;
  uint64_t *phase_data;
};

/* section: API output stream */

#ifdef __cplusplus
#include "hsa_ostream_ops.h"
typedef std::pair<uint32_t, hsa_api_data_t> hsa_api_data_pair_t;
inline std::ostream& operator<< (std::ostream& out, const hsa_api_data_pair_t& data_pair) {
  const uint32_t cid = data_pair.first;
  const hsa_api_data_t& api_data = data_pair.second;
  switch(cid) {
    /* block: CoreApi API */
    case HSA_API_ID_hsa_init: {
      out << "hsa_init(";
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_shut_down: {
      out << "hsa_shut_down(";
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_system_get_info: {
      out << "hsa_system_get_info(";
      out << api_data.args.hsa_system_get_info.attribute << ", ";
      out << api_data.args.hsa_system_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_system_extension_supported: {
      out << "hsa_system_extension_supported(";
      out << api_data.args.hsa_system_extension_supported.extension << ", ";
      out << api_data.args.hsa_system_extension_supported.version_major << ", ";
      out << api_data.args.hsa_system_extension_supported.version_minor << ", ";
      out << api_data.args.hsa_system_extension_supported.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_system_get_extension_table: {
      out << "hsa_system_get_extension_table(";
      out << api_data.args.hsa_system_get_extension_table.extension << ", ";
      out << api_data.args.hsa_system_get_extension_table.version_major << ", ";
      out << api_data.args.hsa_system_get_extension_table.version_minor << ", ";
      out << api_data.args.hsa_system_get_extension_table.table;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_iterate_agents: {
      out << "hsa_iterate_agents(";
      out << api_data.args.hsa_iterate_agents.callback << ", ";
      out << api_data.args.hsa_iterate_agents.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_get_info: {
      out << "hsa_agent_get_info(";
      out << api_data.args.hsa_agent_get_info.agent << ", ";
      out << api_data.args.hsa_agent_get_info.attribute << ", ";
      out << api_data.args.hsa_agent_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_create: {
      out << "hsa_queue_create(";
      out << api_data.args.hsa_queue_create.agent << ", ";
      out << api_data.args.hsa_queue_create.size << ", ";
      out << api_data.args.hsa_queue_create.type << ", ";
      out << api_data.args.hsa_queue_create.callback << ", ";
      out << api_data.args.hsa_queue_create.data << ", ";
      out << api_data.args.hsa_queue_create.private_segment_size << ", ";
      out << api_data.args.hsa_queue_create.group_segment_size << ", ";
      out << api_data.args.hsa_queue_create.queue;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_soft_queue_create: {
      out << "hsa_soft_queue_create(";
      out << api_data.args.hsa_soft_queue_create.region << ", ";
      out << api_data.args.hsa_soft_queue_create.size << ", ";
      out << api_data.args.hsa_soft_queue_create.type << ", ";
      out << api_data.args.hsa_soft_queue_create.features << ", ";
      out << api_data.args.hsa_soft_queue_create.doorbell_signal << ", ";
      out << api_data.args.hsa_soft_queue_create.queue;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_destroy: {
      out << "hsa_queue_destroy(";
      out << api_data.args.hsa_queue_destroy.queue;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_inactivate: {
      out << "hsa_queue_inactivate(";
      out << api_data.args.hsa_queue_inactivate.queue;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_load_read_index_scacquire: {
      out << "hsa_queue_load_read_index_scacquire(";
      out << api_data.args.hsa_queue_load_read_index_scacquire.queue;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_load_read_index_relaxed: {
      out << "hsa_queue_load_read_index_relaxed(";
      out << api_data.args.hsa_queue_load_read_index_relaxed.queue;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_load_write_index_scacquire: {
      out << "hsa_queue_load_write_index_scacquire(";
      out << api_data.args.hsa_queue_load_write_index_scacquire.queue;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_load_write_index_relaxed: {
      out << "hsa_queue_load_write_index_relaxed(";
      out << api_data.args.hsa_queue_load_write_index_relaxed.queue;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_store_write_index_relaxed: {
      out << "hsa_queue_store_write_index_relaxed(";
      out << api_data.args.hsa_queue_store_write_index_relaxed.queue << ", ";
      out << api_data.args.hsa_queue_store_write_index_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_queue_store_write_index_screlease: {
      out << "hsa_queue_store_write_index_screlease(";
      out << api_data.args.hsa_queue_store_write_index_screlease.queue << ", ";
      out << api_data.args.hsa_queue_store_write_index_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_queue_cas_write_index_scacq_screl: {
      out << "hsa_queue_cas_write_index_scacq_screl(";
      out << api_data.args.hsa_queue_cas_write_index_scacq_screl.queue << ", ";
      out << api_data.args.hsa_queue_cas_write_index_scacq_screl.expected << ", ";
      out << api_data.args.hsa_queue_cas_write_index_scacq_screl.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_cas_write_index_scacquire: {
      out << "hsa_queue_cas_write_index_scacquire(";
      out << api_data.args.hsa_queue_cas_write_index_scacquire.queue << ", ";
      out << api_data.args.hsa_queue_cas_write_index_scacquire.expected << ", ";
      out << api_data.args.hsa_queue_cas_write_index_scacquire.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_cas_write_index_relaxed: {
      out << "hsa_queue_cas_write_index_relaxed(";
      out << api_data.args.hsa_queue_cas_write_index_relaxed.queue << ", ";
      out << api_data.args.hsa_queue_cas_write_index_relaxed.expected << ", ";
      out << api_data.args.hsa_queue_cas_write_index_relaxed.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_cas_write_index_screlease: {
      out << "hsa_queue_cas_write_index_screlease(";
      out << api_data.args.hsa_queue_cas_write_index_screlease.queue << ", ";
      out << api_data.args.hsa_queue_cas_write_index_screlease.expected << ", ";
      out << api_data.args.hsa_queue_cas_write_index_screlease.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_add_write_index_scacq_screl: {
      out << "hsa_queue_add_write_index_scacq_screl(";
      out << api_data.args.hsa_queue_add_write_index_scacq_screl.queue << ", ";
      out << api_data.args.hsa_queue_add_write_index_scacq_screl.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_add_write_index_scacquire: {
      out << "hsa_queue_add_write_index_scacquire(";
      out << api_data.args.hsa_queue_add_write_index_scacquire.queue << ", ";
      out << api_data.args.hsa_queue_add_write_index_scacquire.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_add_write_index_relaxed: {
      out << "hsa_queue_add_write_index_relaxed(";
      out << api_data.args.hsa_queue_add_write_index_relaxed.queue << ", ";
      out << api_data.args.hsa_queue_add_write_index_relaxed.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_add_write_index_screlease: {
      out << "hsa_queue_add_write_index_screlease(";
      out << api_data.args.hsa_queue_add_write_index_screlease.queue << ", ";
      out << api_data.args.hsa_queue_add_write_index_screlease.value;
      out << ") = " << api_data.uint64_t_retval;
      break;
    }
    case HSA_API_ID_hsa_queue_store_read_index_relaxed: {
      out << "hsa_queue_store_read_index_relaxed(";
      out << api_data.args.hsa_queue_store_read_index_relaxed.queue << ", ";
      out << api_data.args.hsa_queue_store_read_index_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_queue_store_read_index_screlease: {
      out << "hsa_queue_store_read_index_screlease(";
      out << api_data.args.hsa_queue_store_read_index_screlease.queue << ", ";
      out << api_data.args.hsa_queue_store_read_index_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_agent_iterate_regions: {
      out << "hsa_agent_iterate_regions(";
      out << api_data.args.hsa_agent_iterate_regions.agent << ", ";
      out << api_data.args.hsa_agent_iterate_regions.callback << ", ";
      out << api_data.args.hsa_agent_iterate_regions.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_region_get_info: {
      out << "hsa_region_get_info(";
      out << api_data.args.hsa_region_get_info.region << ", ";
      out << api_data.args.hsa_region_get_info.attribute << ", ";
      out << api_data.args.hsa_region_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_get_exception_policies: {
      out << "hsa_agent_get_exception_policies(";
      out << api_data.args.hsa_agent_get_exception_policies.agent << ", ";
      out << api_data.args.hsa_agent_get_exception_policies.profile << ", ";
      out << api_data.args.hsa_agent_get_exception_policies.mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_extension_supported: {
      out << "hsa_agent_extension_supported(";
      out << api_data.args.hsa_agent_extension_supported.extension << ", ";
      out << api_data.args.hsa_agent_extension_supported.agent << ", ";
      out << api_data.args.hsa_agent_extension_supported.version_major << ", ";
      out << api_data.args.hsa_agent_extension_supported.version_minor << ", ";
      out << api_data.args.hsa_agent_extension_supported.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_register: {
      out << "hsa_memory_register(";
      out << api_data.args.hsa_memory_register.ptr << ", ";
      out << api_data.args.hsa_memory_register.size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_deregister: {
      out << "hsa_memory_deregister(";
      out << api_data.args.hsa_memory_deregister.ptr << ", ";
      out << api_data.args.hsa_memory_deregister.size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_allocate: {
      out << "hsa_memory_allocate(";
      out << api_data.args.hsa_memory_allocate.region << ", ";
      out << api_data.args.hsa_memory_allocate.size << ", ";
      out << api_data.args.hsa_memory_allocate.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_free: {
      out << "hsa_memory_free(";
      out << api_data.args.hsa_memory_free.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_copy: {
      out << "hsa_memory_copy(";
      out << api_data.args.hsa_memory_copy.dst << ", ";
      out << api_data.args.hsa_memory_copy.src << ", ";
      out << api_data.args.hsa_memory_copy.size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_memory_assign_agent: {
      out << "hsa_memory_assign_agent(";
      out << api_data.args.hsa_memory_assign_agent.ptr << ", ";
      out << api_data.args.hsa_memory_assign_agent.agent << ", ";
      out << api_data.args.hsa_memory_assign_agent.access;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_create: {
      out << "hsa_signal_create(";
      out << api_data.args.hsa_signal_create.initial_value << ", ";
      out << api_data.args.hsa_signal_create.num_consumers << ", ";
      out << api_data.args.hsa_signal_create.consumers << ", ";
      out << api_data.args.hsa_signal_create.signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_destroy: {
      out << "hsa_signal_destroy(";
      out << api_data.args.hsa_signal_destroy.signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_load_relaxed: {
      out << "hsa_signal_load_relaxed(";
      out << api_data.args.hsa_signal_load_relaxed.signal;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_load_scacquire: {
      out << "hsa_signal_load_scacquire(";
      out << api_data.args.hsa_signal_load_scacquire.signal;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_store_relaxed: {
      out << "hsa_signal_store_relaxed(";
      out << api_data.args.hsa_signal_store_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_store_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_store_screlease: {
      out << "hsa_signal_store_screlease(";
      out << api_data.args.hsa_signal_store_screlease.signal << ", ";
      out << api_data.args.hsa_signal_store_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_wait_relaxed: {
      out << "hsa_signal_wait_relaxed(";
      out << api_data.args.hsa_signal_wait_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_wait_relaxed.condition << ", ";
      out << api_data.args.hsa_signal_wait_relaxed.compare_value << ", ";
      out << api_data.args.hsa_signal_wait_relaxed.timeout_hint << ", ";
      out << api_data.args.hsa_signal_wait_relaxed.wait_state_hint;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_wait_scacquire: {
      out << "hsa_signal_wait_scacquire(";
      out << api_data.args.hsa_signal_wait_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_wait_scacquire.condition << ", ";
      out << api_data.args.hsa_signal_wait_scacquire.compare_value << ", ";
      out << api_data.args.hsa_signal_wait_scacquire.timeout_hint << ", ";
      out << api_data.args.hsa_signal_wait_scacquire.wait_state_hint;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_and_relaxed: {
      out << "hsa_signal_and_relaxed(";
      out << api_data.args.hsa_signal_and_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_and_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_and_scacquire: {
      out << "hsa_signal_and_scacquire(";
      out << api_data.args.hsa_signal_and_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_and_scacquire.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_and_screlease: {
      out << "hsa_signal_and_screlease(";
      out << api_data.args.hsa_signal_and_screlease.signal << ", ";
      out << api_data.args.hsa_signal_and_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_and_scacq_screl: {
      out << "hsa_signal_and_scacq_screl(";
      out << api_data.args.hsa_signal_and_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_and_scacq_screl.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_or_relaxed: {
      out << "hsa_signal_or_relaxed(";
      out << api_data.args.hsa_signal_or_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_or_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_or_scacquire: {
      out << "hsa_signal_or_scacquire(";
      out << api_data.args.hsa_signal_or_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_or_scacquire.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_or_screlease: {
      out << "hsa_signal_or_screlease(";
      out << api_data.args.hsa_signal_or_screlease.signal << ", ";
      out << api_data.args.hsa_signal_or_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_or_scacq_screl: {
      out << "hsa_signal_or_scacq_screl(";
      out << api_data.args.hsa_signal_or_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_or_scacq_screl.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_xor_relaxed: {
      out << "hsa_signal_xor_relaxed(";
      out << api_data.args.hsa_signal_xor_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_xor_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_xor_scacquire: {
      out << "hsa_signal_xor_scacquire(";
      out << api_data.args.hsa_signal_xor_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_xor_scacquire.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_xor_screlease: {
      out << "hsa_signal_xor_screlease(";
      out << api_data.args.hsa_signal_xor_screlease.signal << ", ";
      out << api_data.args.hsa_signal_xor_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_xor_scacq_screl: {
      out << "hsa_signal_xor_scacq_screl(";
      out << api_data.args.hsa_signal_xor_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_xor_scacq_screl.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_exchange_relaxed: {
      out << "hsa_signal_exchange_relaxed(";
      out << api_data.args.hsa_signal_exchange_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_exchange_relaxed.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_exchange_scacquire: {
      out << "hsa_signal_exchange_scacquire(";
      out << api_data.args.hsa_signal_exchange_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_exchange_scacquire.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_exchange_screlease: {
      out << "hsa_signal_exchange_screlease(";
      out << api_data.args.hsa_signal_exchange_screlease.signal << ", ";
      out << api_data.args.hsa_signal_exchange_screlease.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_exchange_scacq_screl: {
      out << "hsa_signal_exchange_scacq_screl(";
      out << api_data.args.hsa_signal_exchange_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_exchange_scacq_screl.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_add_relaxed: {
      out << "hsa_signal_add_relaxed(";
      out << api_data.args.hsa_signal_add_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_add_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_add_scacquire: {
      out << "hsa_signal_add_scacquire(";
      out << api_data.args.hsa_signal_add_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_add_scacquire.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_add_screlease: {
      out << "hsa_signal_add_screlease(";
      out << api_data.args.hsa_signal_add_screlease.signal << ", ";
      out << api_data.args.hsa_signal_add_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_add_scacq_screl: {
      out << "hsa_signal_add_scacq_screl(";
      out << api_data.args.hsa_signal_add_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_add_scacq_screl.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_subtract_relaxed: {
      out << "hsa_signal_subtract_relaxed(";
      out << api_data.args.hsa_signal_subtract_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_subtract_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_subtract_scacquire: {
      out << "hsa_signal_subtract_scacquire(";
      out << api_data.args.hsa_signal_subtract_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_subtract_scacquire.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_subtract_screlease: {
      out << "hsa_signal_subtract_screlease(";
      out << api_data.args.hsa_signal_subtract_screlease.signal << ", ";
      out << api_data.args.hsa_signal_subtract_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_subtract_scacq_screl: {
      out << "hsa_signal_subtract_scacq_screl(";
      out << api_data.args.hsa_signal_subtract_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_subtract_scacq_screl.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_cas_relaxed: {
      out << "hsa_signal_cas_relaxed(";
      out << api_data.args.hsa_signal_cas_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_cas_relaxed.expected << ", ";
      out << api_data.args.hsa_signal_cas_relaxed.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_cas_scacquire: {
      out << "hsa_signal_cas_scacquire(";
      out << api_data.args.hsa_signal_cas_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_cas_scacquire.expected << ", ";
      out << api_data.args.hsa_signal_cas_scacquire.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_cas_screlease: {
      out << "hsa_signal_cas_screlease(";
      out << api_data.args.hsa_signal_cas_screlease.signal << ", ";
      out << api_data.args.hsa_signal_cas_screlease.expected << ", ";
      out << api_data.args.hsa_signal_cas_screlease.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_cas_scacq_screl: {
      out << "hsa_signal_cas_scacq_screl(";
      out << api_data.args.hsa_signal_cas_scacq_screl.signal << ", ";
      out << api_data.args.hsa_signal_cas_scacq_screl.expected << ", ";
      out << api_data.args.hsa_signal_cas_scacq_screl.value;
      out << ") = " << api_data.hsa_signal_value_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_from_name: {
      out << "hsa_isa_from_name(";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_isa_from_name.name << ", ";
      out << api_data.args.hsa_isa_from_name.isa;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_get_info: {
      out << "hsa_isa_get_info(";
      out << api_data.args.hsa_isa_get_info.isa << ", ";
      out << api_data.args.hsa_isa_get_info.attribute << ", ";
      out << api_data.args.hsa_isa_get_info.index << ", ";
      out << api_data.args.hsa_isa_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_compatible: {
      out << "hsa_isa_compatible(";
      out << api_data.args.hsa_isa_compatible.code_object_isa << ", ";
      out << api_data.args.hsa_isa_compatible.agent_isa << ", ";
      out << api_data.args.hsa_isa_compatible.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_serialize: {
      out << "hsa_code_object_serialize(";
      out << api_data.args.hsa_code_object_serialize.code_object << ", ";
      out << api_data.args.hsa_code_object_serialize.alloc_callback << ", ";
      out << api_data.args.hsa_code_object_serialize.callback_data << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_code_object_serialize.options << ", ";
      out << api_data.args.hsa_code_object_serialize.serialized_code_object << ", ";
      out << api_data.args.hsa_code_object_serialize.serialized_code_object_size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_deserialize: {
      out << "hsa_code_object_deserialize(";
      out << api_data.args.hsa_code_object_deserialize.serialized_code_object << ", ";
      out << api_data.args.hsa_code_object_deserialize.serialized_code_object_size << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_code_object_deserialize.options << ", ";
      out << api_data.args.hsa_code_object_deserialize.code_object;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_destroy: {
      out << "hsa_code_object_destroy(";
      out << api_data.args.hsa_code_object_destroy.code_object;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_get_info: {
      out << "hsa_code_object_get_info(";
      out << api_data.args.hsa_code_object_get_info.code_object << ", ";
      out << api_data.args.hsa_code_object_get_info.attribute << ", ";
      out << api_data.args.hsa_code_object_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_get_symbol: {
      out << "hsa_code_object_get_symbol(";
      out << api_data.args.hsa_code_object_get_symbol.code_object << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_code_object_get_symbol.symbol_name << ", ";
      out << api_data.args.hsa_code_object_get_symbol.symbol;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_symbol_get_info: {
      out << "hsa_code_symbol_get_info(";
      out << api_data.args.hsa_code_symbol_get_info.code_symbol << ", ";
      out << api_data.args.hsa_code_symbol_get_info.attribute << ", ";
      out << api_data.args.hsa_code_symbol_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_iterate_symbols: {
      out << "hsa_code_object_iterate_symbols(";
      out << api_data.args.hsa_code_object_iterate_symbols.code_object << ", ";
      out << api_data.args.hsa_code_object_iterate_symbols.callback << ", ";
      out << api_data.args.hsa_code_object_iterate_symbols.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_create: {
      out << "hsa_executable_create(";
      out << api_data.args.hsa_executable_create.profile << ", ";
      out << api_data.args.hsa_executable_create.executable_state << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_create.options << ", ";
      out << api_data.args.hsa_executable_create.executable;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_destroy: {
      out << "hsa_executable_destroy(";
      out << api_data.args.hsa_executable_destroy.executable;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_load_code_object: {
      out << "hsa_executable_load_code_object(";
      out << api_data.args.hsa_executable_load_code_object.executable << ", ";
      out << api_data.args.hsa_executable_load_code_object.agent << ", ";
      out << api_data.args.hsa_executable_load_code_object.code_object << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_load_code_object.options;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_freeze: {
      out << "hsa_executable_freeze(";
      out << api_data.args.hsa_executable_freeze.executable << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_freeze.options;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_get_info: {
      out << "hsa_executable_get_info(";
      out << api_data.args.hsa_executable_get_info.executable << ", ";
      out << api_data.args.hsa_executable_get_info.attribute << ", ";
      out << api_data.args.hsa_executable_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_global_variable_define: {
      out << "hsa_executable_global_variable_define(";
      out << api_data.args.hsa_executable_global_variable_define.executable << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_global_variable_define.variable_name << ", ";
      out << api_data.args.hsa_executable_global_variable_define.address;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_agent_global_variable_define: {
      out << "hsa_executable_agent_global_variable_define(";
      out << api_data.args.hsa_executable_agent_global_variable_define.executable << ", ";
      out << api_data.args.hsa_executable_agent_global_variable_define.agent << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_agent_global_variable_define.variable_name << ", ";
      out << api_data.args.hsa_executable_agent_global_variable_define.address;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_readonly_variable_define: {
      out << "hsa_executable_readonly_variable_define(";
      out << api_data.args.hsa_executable_readonly_variable_define.executable << ", ";
      out << api_data.args.hsa_executable_readonly_variable_define.agent << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_readonly_variable_define.variable_name << ", ";
      out << api_data.args.hsa_executable_readonly_variable_define.address;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_validate: {
      out << "hsa_executable_validate(";
      out << api_data.args.hsa_executable_validate.executable << ", ";
      out << api_data.args.hsa_executable_validate.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_get_symbol: {
      out << "hsa_executable_get_symbol(";
      out << api_data.args.hsa_executable_get_symbol.executable << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_get_symbol.module_name << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_get_symbol.symbol_name << ", ";
      out << api_data.args.hsa_executable_get_symbol.agent << ", ";
      out << api_data.args.hsa_executable_get_symbol.call_convention << ", ";
      out << api_data.args.hsa_executable_get_symbol.symbol;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_symbol_get_info: {
      out << "hsa_executable_symbol_get_info(";
      out << api_data.args.hsa_executable_symbol_get_info.executable_symbol << ", ";
      out << api_data.args.hsa_executable_symbol_get_info.attribute << ", ";
      out << api_data.args.hsa_executable_symbol_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_iterate_symbols: {
      out << "hsa_executable_iterate_symbols(";
      out << api_data.args.hsa_executable_iterate_symbols.executable << ", ";
      out << api_data.args.hsa_executable_iterate_symbols.callback << ", ";
      out << api_data.args.hsa_executable_iterate_symbols.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_status_string: {
      out << "hsa_status_string(";
      out << api_data.args.hsa_status_string.status << ", ";
      out << api_data.args.hsa_status_string.status_string;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_extension_get_name: {
      out << "hsa_extension_get_name(";
      out << api_data.args.hsa_extension_get_name.extension << ", ";
      out << api_data.args.hsa_extension_get_name.name;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_system_major_extension_supported: {
      out << "hsa_system_major_extension_supported(";
      out << api_data.args.hsa_system_major_extension_supported.extension << ", ";
      out << api_data.args.hsa_system_major_extension_supported.version_major << ", ";
      out << api_data.args.hsa_system_major_extension_supported.version_minor << ", ";
      out << api_data.args.hsa_system_major_extension_supported.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_system_get_major_extension_table: {
      out << "hsa_system_get_major_extension_table(";
      out << api_data.args.hsa_system_get_major_extension_table.extension << ", ";
      out << api_data.args.hsa_system_get_major_extension_table.version_major << ", ";
      out << api_data.args.hsa_system_get_major_extension_table.table_length << ", ";
      out << api_data.args.hsa_system_get_major_extension_table.table;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_major_extension_supported: {
      out << "hsa_agent_major_extension_supported(";
      out << api_data.args.hsa_agent_major_extension_supported.extension << ", ";
      out << api_data.args.hsa_agent_major_extension_supported.agent << ", ";
      out << api_data.args.hsa_agent_major_extension_supported.version_major << ", ";
      out << api_data.args.hsa_agent_major_extension_supported.version_minor << ", ";
      out << api_data.args.hsa_agent_major_extension_supported.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_cache_get_info: {
      out << "hsa_cache_get_info(";
      out << api_data.args.hsa_cache_get_info.cache << ", ";
      out << api_data.args.hsa_cache_get_info.attribute << ", ";
      out << api_data.args.hsa_cache_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_iterate_caches: {
      out << "hsa_agent_iterate_caches(";
      out << api_data.args.hsa_agent_iterate_caches.agent << ", ";
      out << api_data.args.hsa_agent_iterate_caches.callback << ", ";
      out << api_data.args.hsa_agent_iterate_caches.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_silent_store_relaxed: {
      out << "hsa_signal_silent_store_relaxed(";
      out << api_data.args.hsa_signal_silent_store_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_silent_store_relaxed.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_silent_store_screlease: {
      out << "hsa_signal_silent_store_screlease(";
      out << api_data.args.hsa_signal_silent_store_screlease.signal << ", ";
      out << api_data.args.hsa_signal_silent_store_screlease.value;
      out << ") = void";
      break;
    }
    case HSA_API_ID_hsa_signal_group_create: {
      out << "hsa_signal_group_create(";
      out << api_data.args.hsa_signal_group_create.num_signals << ", ";
      out << api_data.args.hsa_signal_group_create.signals << ", ";
      out << api_data.args.hsa_signal_group_create.num_consumers << ", ";
      out << api_data.args.hsa_signal_group_create.consumers << ", ";
      out << api_data.args.hsa_signal_group_create.signal_group;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_group_destroy: {
      out << "hsa_signal_group_destroy(";
      out << api_data.args.hsa_signal_group_destroy.signal_group;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_group_wait_any_scacquire: {
      out << "hsa_signal_group_wait_any_scacquire(";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.signal_group << ", ";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.conditions << ", ";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.compare_values << ", ";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.wait_state_hint << ", ";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.signal << ", ";
      out << api_data.args.hsa_signal_group_wait_any_scacquire.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_signal_group_wait_any_relaxed: {
      out << "hsa_signal_group_wait_any_relaxed(";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.signal_group << ", ";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.conditions << ", ";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.compare_values << ", ";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.wait_state_hint << ", ";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.signal << ", ";
      out << api_data.args.hsa_signal_group_wait_any_relaxed.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_agent_iterate_isas: {
      out << "hsa_agent_iterate_isas(";
      out << api_data.args.hsa_agent_iterate_isas.agent << ", ";
      out << api_data.args.hsa_agent_iterate_isas.callback << ", ";
      out << api_data.args.hsa_agent_iterate_isas.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_get_info_alt: {
      out << "hsa_isa_get_info_alt(";
      out << api_data.args.hsa_isa_get_info_alt.isa << ", ";
      out << api_data.args.hsa_isa_get_info_alt.attribute << ", ";
      out << api_data.args.hsa_isa_get_info_alt.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_get_exception_policies: {
      out << "hsa_isa_get_exception_policies(";
      out << api_data.args.hsa_isa_get_exception_policies.isa << ", ";
      out << api_data.args.hsa_isa_get_exception_policies.profile << ", ";
      out << api_data.args.hsa_isa_get_exception_policies.mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_get_round_method: {
      out << "hsa_isa_get_round_method(";
      out << api_data.args.hsa_isa_get_round_method.isa << ", ";
      out << api_data.args.hsa_isa_get_round_method.fp_type << ", ";
      out << api_data.args.hsa_isa_get_round_method.flush_mode << ", ";
      out << api_data.args.hsa_isa_get_round_method.round_method;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_wavefront_get_info: {
      out << "hsa_wavefront_get_info(";
      out << api_data.args.hsa_wavefront_get_info.wavefront << ", ";
      out << api_data.args.hsa_wavefront_get_info.attribute << ", ";
      out << api_data.args.hsa_wavefront_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_isa_iterate_wavefronts: {
      out << "hsa_isa_iterate_wavefronts(";
      out << api_data.args.hsa_isa_iterate_wavefronts.isa << ", ";
      out << api_data.args.hsa_isa_iterate_wavefronts.callback << ", ";
      out << api_data.args.hsa_isa_iterate_wavefronts.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_get_symbol_from_name: {
      out << "hsa_code_object_get_symbol_from_name(";
      out << api_data.args.hsa_code_object_get_symbol_from_name.code_object << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_code_object_get_symbol_from_name.module_name << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_code_object_get_symbol_from_name.symbol_name << ", ";
      out << api_data.args.hsa_code_object_get_symbol_from_name.symbol;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_reader_create_from_file: {
      out << "hsa_code_object_reader_create_from_file(";
      out << api_data.args.hsa_code_object_reader_create_from_file.file << ", ";
      out << api_data.args.hsa_code_object_reader_create_from_file.code_object_reader;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_reader_create_from_memory: {
      out << "hsa_code_object_reader_create_from_memory(";
      out << api_data.args.hsa_code_object_reader_create_from_memory.code_object << ", ";
      out << api_data.args.hsa_code_object_reader_create_from_memory.size << ", ";
      out << api_data.args.hsa_code_object_reader_create_from_memory.code_object_reader;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_code_object_reader_destroy: {
      out << "hsa_code_object_reader_destroy(";
      out << api_data.args.hsa_code_object_reader_destroy.code_object_reader;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_create_alt: {
      out << "hsa_executable_create_alt(";
      out << api_data.args.hsa_executable_create_alt.profile << ", ";
      out << api_data.args.hsa_executable_create_alt.default_float_rounding_mode << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_create_alt.options << ", ";
      out << api_data.args.hsa_executable_create_alt.executable;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_load_program_code_object: {
      out << "hsa_executable_load_program_code_object(";
      out << api_data.args.hsa_executable_load_program_code_object.executable << ", ";
      out << api_data.args.hsa_executable_load_program_code_object.code_object_reader << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_load_program_code_object.options << ", ";
      out << api_data.args.hsa_executable_load_program_code_object.loaded_code_object;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_load_agent_code_object: {
      out << "hsa_executable_load_agent_code_object(";
      out << api_data.args.hsa_executable_load_agent_code_object.executable << ", ";
      out << api_data.args.hsa_executable_load_agent_code_object.agent << ", ";
      out << api_data.args.hsa_executable_load_agent_code_object.code_object_reader << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_load_agent_code_object.options << ", ";
      out << api_data.args.hsa_executable_load_agent_code_object.loaded_code_object;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_validate_alt: {
      out << "hsa_executable_validate_alt(";
      out << api_data.args.hsa_executable_validate_alt.executable << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_validate_alt.options << ", ";
      out << api_data.args.hsa_executable_validate_alt.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_get_symbol_by_name: {
      out << "hsa_executable_get_symbol_by_name(";
      out << api_data.args.hsa_executable_get_symbol_by_name.executable << ", ";
      out << "0x" << std::hex << (uint64_t)api_data.args.hsa_executable_get_symbol_by_name.symbol_name << ", ";
      out << api_data.args.hsa_executable_get_symbol_by_name.agent << ", ";
      out << api_data.args.hsa_executable_get_symbol_by_name.symbol;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_iterate_agent_symbols: {
      out << "hsa_executable_iterate_agent_symbols(";
      out << api_data.args.hsa_executable_iterate_agent_symbols.executable << ", ";
      out << api_data.args.hsa_executable_iterate_agent_symbols.agent << ", ";
      out << api_data.args.hsa_executable_iterate_agent_symbols.callback << ", ";
      out << api_data.args.hsa_executable_iterate_agent_symbols.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_executable_iterate_program_symbols: {
      out << "hsa_executable_iterate_program_symbols(";
      out << api_data.args.hsa_executable_iterate_program_symbols.executable << ", ";
      out << api_data.args.hsa_executable_iterate_program_symbols.callback << ", ";
      out << api_data.args.hsa_executable_iterate_program_symbols.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }

    /* block: AmdExt API */
    case HSA_API_ID_hsa_amd_coherency_get_type: {
      out << "hsa_amd_coherency_get_type(";
      out << api_data.args.hsa_amd_coherency_get_type.agent << ", ";
      out << api_data.args.hsa_amd_coherency_get_type.type;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_coherency_set_type: {
      out << "hsa_amd_coherency_set_type(";
      out << api_data.args.hsa_amd_coherency_set_type.agent << ", ";
      out << api_data.args.hsa_amd_coherency_set_type.type;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_profiling_set_profiler_enabled: {
      out << "hsa_amd_profiling_set_profiler_enabled(";
      out << api_data.args.hsa_amd_profiling_set_profiler_enabled.queue << ", ";
      out << api_data.args.hsa_amd_profiling_set_profiler_enabled.enable;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_profiling_async_copy_enable: {
      out << "hsa_amd_profiling_async_copy_enable(";
      out << api_data.args.hsa_amd_profiling_async_copy_enable.enable;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_profiling_get_dispatch_time: {
      out << "hsa_amd_profiling_get_dispatch_time(";
      out << api_data.args.hsa_amd_profiling_get_dispatch_time.agent << ", ";
      out << api_data.args.hsa_amd_profiling_get_dispatch_time.signal << ", ";
      out << api_data.args.hsa_amd_profiling_get_dispatch_time.time;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_profiling_get_async_copy_time: {
      out << "hsa_amd_profiling_get_async_copy_time(";
      out << api_data.args.hsa_amd_profiling_get_async_copy_time.signal << ", ";
      out << api_data.args.hsa_amd_profiling_get_async_copy_time.time;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_profiling_convert_tick_to_system_domain: {
      out << "hsa_amd_profiling_convert_tick_to_system_domain(";
      out << api_data.args.hsa_amd_profiling_convert_tick_to_system_domain.agent << ", ";
      out << api_data.args.hsa_amd_profiling_convert_tick_to_system_domain.agent_tick << ", ";
      out << api_data.args.hsa_amd_profiling_convert_tick_to_system_domain.system_tick;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_signal_async_handler: {
      out << "hsa_amd_signal_async_handler(";
      out << api_data.args.hsa_amd_signal_async_handler.signal << ", ";
      out << api_data.args.hsa_amd_signal_async_handler.cond << ", ";
      out << api_data.args.hsa_amd_signal_async_handler.value << ", ";
      out << api_data.args.hsa_amd_signal_async_handler.handler << ", ";
      out << api_data.args.hsa_amd_signal_async_handler.arg;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_async_function: {
      out << "hsa_amd_async_function(";
      out << api_data.args.hsa_amd_async_function.callback << ", ";
      out << api_data.args.hsa_amd_async_function.arg;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_signal_wait_any: {
      out << "hsa_amd_signal_wait_any(";
      out << api_data.args.hsa_amd_signal_wait_any.signal_count << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.signals << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.conds << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.values << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.timeout_hint << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.wait_hint << ", ";
      out << api_data.args.hsa_amd_signal_wait_any.satisfying_value;
      out << ") = " << api_data.uint32_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_cu_set_mask: {
      out << "hsa_amd_queue_cu_set_mask(";
      out << api_data.args.hsa_amd_queue_cu_set_mask.queue << ", ";
      out << api_data.args.hsa_amd_queue_cu_set_mask.num_cu_mask_count << ", ";
      out << api_data.args.hsa_amd_queue_cu_set_mask.cu_mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_pool_get_info: {
      out << "hsa_amd_memory_pool_get_info(";
      out << api_data.args.hsa_amd_memory_pool_get_info.memory_pool << ", ";
      out << api_data.args.hsa_amd_memory_pool_get_info.attribute << ", ";
      out << api_data.args.hsa_amd_memory_pool_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_agent_iterate_memory_pools: {
      out << "hsa_amd_agent_iterate_memory_pools(";
      out << api_data.args.hsa_amd_agent_iterate_memory_pools.agent << ", ";
      out << api_data.args.hsa_amd_agent_iterate_memory_pools.callback << ", ";
      out << api_data.args.hsa_amd_agent_iterate_memory_pools.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_pool_allocate: {
      out << "hsa_amd_memory_pool_allocate(";
      out << api_data.args.hsa_amd_memory_pool_allocate.memory_pool << ", ";
      out << api_data.args.hsa_amd_memory_pool_allocate.size << ", ";
      out << api_data.args.hsa_amd_memory_pool_allocate.flags << ", ";
      out << api_data.args.hsa_amd_memory_pool_allocate.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_pool_free: {
      out << "hsa_amd_memory_pool_free(";
      out << api_data.args.hsa_amd_memory_pool_free.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_async_copy: {
      out << "hsa_amd_memory_async_copy(";
      out << api_data.args.hsa_amd_memory_async_copy.dst << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.dst_agent << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.src << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.src_agent << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.size << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.num_dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy.completion_signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_async_copy_on_engine: {
      out << "hsa_amd_memory_async_copy_on_engine(";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.dst << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.dst_agent << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.src << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.src_agent << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.size << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.num_dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.completion_signal << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.engine_id << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_on_engine.force_copy_on_sdma;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_copy_engine_status: {
      out << "hsa_amd_memory_copy_engine_status(";
      out << api_data.args.hsa_amd_memory_copy_engine_status.dst_agent << ", ";
      out << api_data.args.hsa_amd_memory_copy_engine_status.src_agent << ", ";
      out << api_data.args.hsa_amd_memory_copy_engine_status.engine_ids_mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_agent_memory_pool_get_info: {
      out << "hsa_amd_agent_memory_pool_get_info(";
      out << api_data.args.hsa_amd_agent_memory_pool_get_info.agent << ", ";
      out << api_data.args.hsa_amd_agent_memory_pool_get_info.memory_pool << ", ";
      out << api_data.args.hsa_amd_agent_memory_pool_get_info.attribute << ", ";
      out << api_data.args.hsa_amd_agent_memory_pool_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_agents_allow_access: {
      out << "hsa_amd_agents_allow_access(";
      out << api_data.args.hsa_amd_agents_allow_access.num_agents << ", ";
      out << api_data.args.hsa_amd_agents_allow_access.agents << ", ";
      out << api_data.args.hsa_amd_agents_allow_access.flags << ", ";
      out << api_data.args.hsa_amd_agents_allow_access.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_pool_can_migrate: {
      out << "hsa_amd_memory_pool_can_migrate(";
      out << api_data.args.hsa_amd_memory_pool_can_migrate.src_memory_pool << ", ";
      out << api_data.args.hsa_amd_memory_pool_can_migrate.dst_memory_pool << ", ";
      out << api_data.args.hsa_amd_memory_pool_can_migrate.result;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_migrate: {
      out << "hsa_amd_memory_migrate(";
      out << api_data.args.hsa_amd_memory_migrate.ptr << ", ";
      out << api_data.args.hsa_amd_memory_migrate.memory_pool << ", ";
      out << api_data.args.hsa_amd_memory_migrate.flags;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_lock: {
      out << "hsa_amd_memory_lock(";
      out << api_data.args.hsa_amd_memory_lock.host_ptr << ", ";
      out << api_data.args.hsa_amd_memory_lock.size << ", ";
      out << api_data.args.hsa_amd_memory_lock.agents << ", ";
      out << api_data.args.hsa_amd_memory_lock.num_agent << ", ";
      out << api_data.args.hsa_amd_memory_lock.agent_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_unlock: {
      out << "hsa_amd_memory_unlock(";
      out << api_data.args.hsa_amd_memory_unlock.host_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_fill: {
      out << "hsa_amd_memory_fill(";
      out << api_data.args.hsa_amd_memory_fill.ptr << ", ";
      out << api_data.args.hsa_amd_memory_fill.value << ", ";
      out << api_data.args.hsa_amd_memory_fill.count;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_interop_map_buffer: {
      out << "hsa_amd_interop_map_buffer(";
      out << api_data.args.hsa_amd_interop_map_buffer.num_agents << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.agents << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.interop_handle << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.flags << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.size << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.ptr << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.metadata_size << ", ";
      out << api_data.args.hsa_amd_interop_map_buffer.metadata;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_interop_unmap_buffer: {
      out << "hsa_amd_interop_unmap_buffer(";
      out << api_data.args.hsa_amd_interop_unmap_buffer.ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_image_create: {
      out << "hsa_amd_image_create(";
      out << api_data.args.hsa_amd_image_create.agent << ", ";
      out << api_data.args.hsa_amd_image_create.image_descriptor << ", ";
      out << api_data.args.hsa_amd_image_create.image_layout << ", ";
      out << api_data.args.hsa_amd_image_create.image_data << ", ";
      out << api_data.args.hsa_amd_image_create.access_permission << ", ";
      out << api_data.args.hsa_amd_image_create.image;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_pointer_info: {
      out << "hsa_amd_pointer_info(";
      out << api_data.args.hsa_amd_pointer_info.ptr << ", ";
      out << api_data.args.hsa_amd_pointer_info.info << ", ";
      out << api_data.args.hsa_amd_pointer_info.alloc << ", ";
      out << api_data.args.hsa_amd_pointer_info.num_agents_accessible << ", ";
      out << api_data.args.hsa_amd_pointer_info.accessible;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_pointer_info_set_userdata: {
      out << "hsa_amd_pointer_info_set_userdata(";
      out << api_data.args.hsa_amd_pointer_info_set_userdata.ptr << ", ";
      out << api_data.args.hsa_amd_pointer_info_set_userdata.userdata;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_ipc_memory_create: {
      out << "hsa_amd_ipc_memory_create(";
      out << api_data.args.hsa_amd_ipc_memory_create.ptr << ", ";
      out << api_data.args.hsa_amd_ipc_memory_create.len << ", ";
      out << api_data.args.hsa_amd_ipc_memory_create.handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_ipc_memory_attach: {
      out << "hsa_amd_ipc_memory_attach(";
      out << api_data.args.hsa_amd_ipc_memory_attach.handle << ", ";
      out << api_data.args.hsa_amd_ipc_memory_attach.len << ", ";
      out << api_data.args.hsa_amd_ipc_memory_attach.num_agents << ", ";
      out << api_data.args.hsa_amd_ipc_memory_attach.mapping_agents << ", ";
      out << api_data.args.hsa_amd_ipc_memory_attach.mapped_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_ipc_memory_detach: {
      out << "hsa_amd_ipc_memory_detach(";
      out << api_data.args.hsa_amd_ipc_memory_detach.mapped_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_signal_create: {
      out << "hsa_amd_signal_create(";
      out << api_data.args.hsa_amd_signal_create.initial_value << ", ";
      out << api_data.args.hsa_amd_signal_create.num_consumers << ", ";
      out << api_data.args.hsa_amd_signal_create.consumers << ", ";
      out << api_data.args.hsa_amd_signal_create.attributes << ", ";
      out << api_data.args.hsa_amd_signal_create.signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_ipc_signal_create: {
      out << "hsa_amd_ipc_signal_create(";
      out << api_data.args.hsa_amd_ipc_signal_create.signal << ", ";
      out << api_data.args.hsa_amd_ipc_signal_create.handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_ipc_signal_attach: {
      out << "hsa_amd_ipc_signal_attach(";
      out << api_data.args.hsa_amd_ipc_signal_attach.handle << ", ";
      out << api_data.args.hsa_amd_ipc_signal_attach.signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_register_system_event_handler: {
      out << "hsa_amd_register_system_event_handler(";
      out << api_data.args.hsa_amd_register_system_event_handler.callback << ", ";
      out << api_data.args.hsa_amd_register_system_event_handler.data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_intercept_create: {
      out << "hsa_amd_queue_intercept_create(";
      out << api_data.args.hsa_amd_queue_intercept_create.agent_handle << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.size << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.type << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.callback << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.data << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.private_segment_size << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.group_segment_size << ", ";
      out << api_data.args.hsa_amd_queue_intercept_create.queue;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_intercept_register: {
      out << "hsa_amd_queue_intercept_register(";
      out << api_data.args.hsa_amd_queue_intercept_register.queue << ", ";
      out << api_data.args.hsa_amd_queue_intercept_register.callback << ", ";
      out << api_data.args.hsa_amd_queue_intercept_register.user_data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_set_priority: {
      out << "hsa_amd_queue_set_priority(";
      out << api_data.args.hsa_amd_queue_set_priority.queue << ", ";
      out << api_data.args.hsa_amd_queue_set_priority.priority;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_async_copy_rect: {
      out << "hsa_amd_memory_async_copy_rect(";
      out << api_data.args.hsa_amd_memory_async_copy_rect.dst << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.dst_offset << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.src << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.src_offset << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.range << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.range__val << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.copy_agent << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.dir << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.num_dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.dep_signals << ", ";
      out << api_data.args.hsa_amd_memory_async_copy_rect.completion_signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_runtime_queue_create_register: {
      out << "hsa_amd_runtime_queue_create_register(";
      out << api_data.args.hsa_amd_runtime_queue_create_register.callback << ", ";
      out << api_data.args.hsa_amd_runtime_queue_create_register.user_data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_memory_lock_to_pool: {
      out << "hsa_amd_memory_lock_to_pool(";
      out << api_data.args.hsa_amd_memory_lock_to_pool.host_ptr << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.size << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.agents << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.num_agent << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.pool << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.flags << ", ";
      out << api_data.args.hsa_amd_memory_lock_to_pool.agent_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_register_deallocation_callback: {
      out << "hsa_amd_register_deallocation_callback(";
      out << api_data.args.hsa_amd_register_deallocation_callback.ptr << ", ";
      out << api_data.args.hsa_amd_register_deallocation_callback.callback << ", ";
      out << api_data.args.hsa_amd_register_deallocation_callback.user_data;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_deregister_deallocation_callback: {
      out << "hsa_amd_deregister_deallocation_callback(";
      out << api_data.args.hsa_amd_deregister_deallocation_callback.ptr << ", ";
      out << api_data.args.hsa_amd_deregister_deallocation_callback.callback;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_signal_value_pointer: {
      out << "hsa_amd_signal_value_pointer(";
      out << api_data.args.hsa_amd_signal_value_pointer.signal << ", ";
      out << api_data.args.hsa_amd_signal_value_pointer.value_ptr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_svm_attributes_set: {
      out << "hsa_amd_svm_attributes_set(";
      out << api_data.args.hsa_amd_svm_attributes_set.ptr << ", ";
      out << api_data.args.hsa_amd_svm_attributes_set.size << ", ";
      out << api_data.args.hsa_amd_svm_attributes_set.attribute_list << ", ";
      out << api_data.args.hsa_amd_svm_attributes_set.attribute_count;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_svm_attributes_get: {
      out << "hsa_amd_svm_attributes_get(";
      out << api_data.args.hsa_amd_svm_attributes_get.ptr << ", ";
      out << api_data.args.hsa_amd_svm_attributes_get.size << ", ";
      out << api_data.args.hsa_amd_svm_attributes_get.attribute_list << ", ";
      out << api_data.args.hsa_amd_svm_attributes_get.attribute_count;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_svm_prefetch_async: {
      out << "hsa_amd_svm_prefetch_async(";
      out << api_data.args.hsa_amd_svm_prefetch_async.ptr << ", ";
      out << api_data.args.hsa_amd_svm_prefetch_async.size << ", ";
      out << api_data.args.hsa_amd_svm_prefetch_async.agent << ", ";
      out << api_data.args.hsa_amd_svm_prefetch_async.num_dep_signals << ", ";
      out << api_data.args.hsa_amd_svm_prefetch_async.dep_signals << ", ";
      out << api_data.args.hsa_amd_svm_prefetch_async.completion_signal;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_spm_acquire: {
      out << "hsa_amd_spm_acquire(";
      out << api_data.args.hsa_amd_spm_acquire.preferred_agent;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_spm_release: {
      out << "hsa_amd_spm_release(";
      out << api_data.args.hsa_amd_spm_release.preferred_agent;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_spm_set_dest_buffer: {
      out << "hsa_amd_spm_set_dest_buffer(";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.preferred_agent << ", ";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.size_in_bytes << ", ";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.timeout << ", ";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.size_copied << ", ";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.dest << ", ";
      out << api_data.args.hsa_amd_spm_set_dest_buffer.is_data_loss;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_cu_get_mask: {
      out << "hsa_amd_queue_cu_get_mask(";
      out << api_data.args.hsa_amd_queue_cu_get_mask.queue << ", ";
      out << api_data.args.hsa_amd_queue_cu_get_mask.num_cu_mask_count << ", ";
      out << api_data.args.hsa_amd_queue_cu_get_mask.cu_mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_portable_export_dmabuf: {
      out << "hsa_amd_portable_export_dmabuf(";
      out << api_data.args.hsa_amd_portable_export_dmabuf.ptr << ", ";
      out << api_data.args.hsa_amd_portable_export_dmabuf.size << ", ";
      out << api_data.args.hsa_amd_portable_export_dmabuf.dmabuf << ", ";
      out << api_data.args.hsa_amd_portable_export_dmabuf.offset;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_portable_close_dmabuf: {
      out << "hsa_amd_portable_close_dmabuf(";
      out << api_data.args.hsa_amd_portable_close_dmabuf.dmabuf;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_address_reserve: {
      out << "hsa_amd_vmem_address_reserve(";
      out << api_data.args.hsa_amd_vmem_address_reserve.va << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve.size << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve.address << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve.flags;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_address_free: {
      out << "hsa_amd_vmem_address_free(";
      out << api_data.args.hsa_amd_vmem_address_free.va << ", ";
      out << api_data.args.hsa_amd_vmem_address_free.size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_handle_create: {
      out << "hsa_amd_vmem_handle_create(";
      out << api_data.args.hsa_amd_vmem_handle_create.pool << ", ";
      out << api_data.args.hsa_amd_vmem_handle_create.size << ", ";
      out << api_data.args.hsa_amd_vmem_handle_create.type << ", ";
      out << api_data.args.hsa_amd_vmem_handle_create.flags << ", ";
      out << api_data.args.hsa_amd_vmem_handle_create.memory_handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_handle_release: {
      out << "hsa_amd_vmem_handle_release(";
      out << api_data.args.hsa_amd_vmem_handle_release.memory_handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_map: {
      out << "hsa_amd_vmem_map(";
      out << api_data.args.hsa_amd_vmem_map.va << ", ";
      out << api_data.args.hsa_amd_vmem_map.size << ", ";
      out << api_data.args.hsa_amd_vmem_map.in_offset << ", ";
      out << api_data.args.hsa_amd_vmem_map.memory_handle << ", ";
      out << api_data.args.hsa_amd_vmem_map.flags;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_unmap: {
      out << "hsa_amd_vmem_unmap(";
      out << api_data.args.hsa_amd_vmem_unmap.va << ", ";
      out << api_data.args.hsa_amd_vmem_unmap.size;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_set_access: {
      out << "hsa_amd_vmem_set_access(";
      out << api_data.args.hsa_amd_vmem_set_access.va << ", ";
      out << api_data.args.hsa_amd_vmem_set_access.size << ", ";
      out << api_data.args.hsa_amd_vmem_set_access.desc << ", ";
      out << api_data.args.hsa_amd_vmem_set_access.desc_cnt;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_get_access: {
      out << "hsa_amd_vmem_get_access(";
      out << api_data.args.hsa_amd_vmem_get_access.va << ", ";
      out << api_data.args.hsa_amd_vmem_get_access.perms << ", ";
      out << api_data.args.hsa_amd_vmem_get_access.agent_handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_export_shareable_handle: {
      out << "hsa_amd_vmem_export_shareable_handle(";
      out << api_data.args.hsa_amd_vmem_export_shareable_handle.dmabuf_fd << ", ";
      out << api_data.args.hsa_amd_vmem_export_shareable_handle.handle << ", ";
      out << api_data.args.hsa_amd_vmem_export_shareable_handle.flags;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_import_shareable_handle: {
      out << "hsa_amd_vmem_import_shareable_handle(";
      out << api_data.args.hsa_amd_vmem_import_shareable_handle.dmabuf_fd << ", ";
      out << api_data.args.hsa_amd_vmem_import_shareable_handle.handle;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_retain_alloc_handle: {
      out << "hsa_amd_vmem_retain_alloc_handle(";
      out << api_data.args.hsa_amd_vmem_retain_alloc_handle.memory_handle << ", ";
      out << api_data.args.hsa_amd_vmem_retain_alloc_handle.addr;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_get_alloc_properties_from_handle: {
      out << "hsa_amd_vmem_get_alloc_properties_from_handle(";
      out << api_data.args.hsa_amd_vmem_get_alloc_properties_from_handle.memory_handle << ", ";
      out << api_data.args.hsa_amd_vmem_get_alloc_properties_from_handle.pool << ", ";
      out << api_data.args.hsa_amd_vmem_get_alloc_properties_from_handle.type;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_agent_set_async_scratch_limit: {
      out << "hsa_amd_agent_set_async_scratch_limit(";
      out << api_data.args.hsa_amd_agent_set_async_scratch_limit.agent << ", ";
      out << api_data.args.hsa_amd_agent_set_async_scratch_limit.threshold;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_queue_get_info: {
      out << "hsa_amd_queue_get_info(";
      out << api_data.args.hsa_amd_queue_get_info.queue << ", ";
      out << api_data.args.hsa_amd_queue_get_info.attribute << ", ";
      out << api_data.args.hsa_amd_queue_get_info.value;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_amd_vmem_address_reserve_align: {
      out << "hsa_amd_vmem_address_reserve_align(";
      out << api_data.args.hsa_amd_vmem_address_reserve_align.va << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve_align.size << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve_align.address << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve_align.alignment << ", ";
      out << api_data.args.hsa_amd_vmem_address_reserve_align.flags;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }

    /* block: ImageExt API */
    case HSA_API_ID_hsa_ext_image_get_capability: {
      out << "hsa_ext_image_get_capability(";
      out << api_data.args.hsa_ext_image_get_capability.agent << ", ";
      out << api_data.args.hsa_ext_image_get_capability.geometry << ", ";
      out << api_data.args.hsa_ext_image_get_capability.image_format << ", ";
      out << api_data.args.hsa_ext_image_get_capability.capability_mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_data_get_info: {
      out << "hsa_ext_image_data_get_info(";
      out << api_data.args.hsa_ext_image_data_get_info.agent << ", ";
      out << api_data.args.hsa_ext_image_data_get_info.image_descriptor << ", ";
      out << api_data.args.hsa_ext_image_data_get_info.access_permission << ", ";
      out << api_data.args.hsa_ext_image_data_get_info.image_data_info;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_create: {
      out << "hsa_ext_image_create(";
      out << api_data.args.hsa_ext_image_create.agent << ", ";
      out << api_data.args.hsa_ext_image_create.image_descriptor << ", ";
      out << api_data.args.hsa_ext_image_create.image_data << ", ";
      out << api_data.args.hsa_ext_image_create.access_permission << ", ";
      out << api_data.args.hsa_ext_image_create.image;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_import: {
      out << "hsa_ext_image_import(";
      out << api_data.args.hsa_ext_image_import.agent << ", ";
      out << api_data.args.hsa_ext_image_import.src_memory << ", ";
      out << api_data.args.hsa_ext_image_import.src_row_pitch << ", ";
      out << api_data.args.hsa_ext_image_import.src_slice_pitch << ", ";
      out << api_data.args.hsa_ext_image_import.dst_image << ", ";
      out << api_data.args.hsa_ext_image_import.image_region;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_export: {
      out << "hsa_ext_image_export(";
      out << api_data.args.hsa_ext_image_export.agent << ", ";
      out << api_data.args.hsa_ext_image_export.src_image << ", ";
      out << api_data.args.hsa_ext_image_export.dst_memory << ", ";
      out << api_data.args.hsa_ext_image_export.dst_row_pitch << ", ";
      out << api_data.args.hsa_ext_image_export.dst_slice_pitch << ", ";
      out << api_data.args.hsa_ext_image_export.image_region;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_copy: {
      out << "hsa_ext_image_copy(";
      out << api_data.args.hsa_ext_image_copy.agent << ", ";
      out << api_data.args.hsa_ext_image_copy.src_image << ", ";
      out << api_data.args.hsa_ext_image_copy.src_offset << ", ";
      out << api_data.args.hsa_ext_image_copy.dst_image << ", ";
      out << api_data.args.hsa_ext_image_copy.dst_offset << ", ";
      out << api_data.args.hsa_ext_image_copy.range;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_clear: {
      out << "hsa_ext_image_clear(";
      out << api_data.args.hsa_ext_image_clear.agent << ", ";
      out << api_data.args.hsa_ext_image_clear.image << ", ";
      out << api_data.args.hsa_ext_image_clear.data << ", ";
      out << api_data.args.hsa_ext_image_clear.image_region;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_destroy: {
      out << "hsa_ext_image_destroy(";
      out << api_data.args.hsa_ext_image_destroy.agent << ", ";
      out << api_data.args.hsa_ext_image_destroy.image;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_sampler_create: {
      out << "hsa_ext_sampler_create(";
      out << api_data.args.hsa_ext_sampler_create.agent << ", ";
      out << api_data.args.hsa_ext_sampler_create.sampler_descriptor << ", ";
      out << api_data.args.hsa_ext_sampler_create.sampler;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_sampler_destroy: {
      out << "hsa_ext_sampler_destroy(";
      out << api_data.args.hsa_ext_sampler_destroy.agent << ", ";
      out << api_data.args.hsa_ext_sampler_destroy.sampler;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_get_capability_with_layout: {
      out << "hsa_ext_image_get_capability_with_layout(";
      out << api_data.args.hsa_ext_image_get_capability_with_layout.agent << ", ";
      out << api_data.args.hsa_ext_image_get_capability_with_layout.geometry << ", ";
      out << api_data.args.hsa_ext_image_get_capability_with_layout.image_format << ", ";
      out << api_data.args.hsa_ext_image_get_capability_with_layout.image_data_layout << ", ";
      out << api_data.args.hsa_ext_image_get_capability_with_layout.capability_mask;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_data_get_info_with_layout: {
      out << "hsa_ext_image_data_get_info_with_layout(";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.agent << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.image_descriptor << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.access_permission << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.image_data_layout << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.image_data_row_pitch << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.image_data_slice_pitch << ", ";
      out << api_data.args.hsa_ext_image_data_get_info_with_layout.image_data_info;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    case HSA_API_ID_hsa_ext_image_create_with_layout: {
      out << "hsa_ext_image_create_with_layout(";
      out << api_data.args.hsa_ext_image_create_with_layout.agent << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image_descriptor << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image_data << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.access_permission << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image_data_layout << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image_data_row_pitch << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image_data_slice_pitch << ", ";
      out << api_data.args.hsa_ext_image_create_with_layout.image;
      out << ") = " << api_data.hsa_status_t_retval;
      break;
    }
    default:
      out << "ERROR: unknown API";
      abort();
  }
  return out;
}
#endif
#endif /* HSA_PROF_STR_H_ */