/*
 * Copyright 2016-2023 NVIDIA Corporation.  All rights reserved.
 *
 * NOTICE TO LICENSEE:
 *
 * This source code and/or documentation ("Licensed Deliverables") are
 * subject to NVIDIA intellectual property rights under U.S. and
 * international Copyright laws.
 *
 * These Licensed Deliverables contained herein is PROPRIETARY and
 * CONFIDENTIAL to NVIDIA and is being provided under the terms and
 * conditions of a form of NVIDIA software license agreement by and
 * between NVIDIA and Licensee ("License Agreement") or electronically
 * accepted by Licensee.  Notwithstanding any terms or conditions to
 * the contrary in the License Agreement, reproduction or disclosure
 * of the Licensed Deliverables to any third party without the express
 * written consent of NVIDIA is prohibited.
 *
 * NOTWITHSTANDING ANY TERMS OR CONDITIONS TO THE CONTRARY IN THE
 * LICENSE AGREEMENT, <PERSON><PERSON>DIA MAKES NO REPRESENTATION ABOUT THE
 * SUITABILITY OF THESE LICENSED DELIVERABLES FOR ANY PURPOSE.  IT IS
 * PROVIDED "AS IS" WITHOUT EXPRESS OR IMPLIED WARRANTY OF ANY KIND.
 * NVIDIA DISCLAIMS ALL WARRANTIES WITH REGARD TO THESE LICENSED
 * DELIVERABLES, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY,
 * NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE.
 * NOTWITHSTANDING ANY TERMS OR CONDITIONS TO THE CONTRARY IN THE
 * LICENSE AGREEMENT, IN NO EVENT SHALL NVIDIA BE LIABLE FOR ANY
 * SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, OR ANY
 * DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
 * WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
 * ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
 * OF THESE LICENSED DELIVERABLES.
 *
 * U.S. Government End Users.  These Licensed Deliverables are a
 * "commercial item" as that term is defined at 48 C.F.R. 2.101 (OCT
 * 1995), consisting of "commercial computer software" and "commercial
 * computer software documentation" as such terms are used in 48
 * C.F.R. 12.212 (SEPT 1995) and is provided to the U.S. Government
 * only as a commercial end item.  Consistent with 48 C.F.R.12.212 and
 * 48 C.F.R. 227.7202-1 through 227.7202-4 (JUNE 1995), all
 * U.S. Government End Users acquire the Licensed Deliverables with
 * only those rights set forth herein.
 *
 * Any use of the Licensed Deliverables in individual and commercial
 * software must include, in the user documentation and internal
 * comments to the code, the above Disclaimer and U.S. Government End
 * Users Notice.
 */

//NOTE: For NVRTC, these declarations have been moved into the compiler (to reduce compile time)
#define EXCLUDE_FROM_RTC

#if !defined(__SM_61_INTRINSICS_H__)
#define __SM_61_INTRINSICS_H__

#if defined(__CUDACC_RTC__)
#define __SM_61_INTRINSICS_DECL__ __device__
#else /* !__CUDACC_RTC__ */
#define __SM_61_INTRINSICS_DECL__ static __device__ __inline__
#endif /* __CUDACC_RTC__ */

#if defined(__cplusplus) && defined(__CUDACC__)

#if defined(_NVHPC_CUDA) || !defined(__CUDA_ARCH__) || __CUDA_ARCH__ >= 610

/*******************************************************************************
*                                                                              *
*                                                                              *
*                                                                              *
*******************************************************************************/

#include "cuda_runtime_api.h"

#if !defined(__CUDA_ARCH__) && !defined(_NVHPC_CUDA)
#define __DEF_IF_HOST { }
#else  /* !__CUDA_ARCH__ && !_NVHPC_CUDA */
#define __DEF_IF_HOST ;
#endif /* __CUDA_ARCH__ || _NVHPC_CUDA */

/*******************************************************************************
*                                                                              *
*  Below are declarations of SM-6.1 intrinsics which are included as           *
*  source (instead of being built in to the compiler)                          *
*                                                                              *
*******************************************************************************/


/******************************************************************************
 *                                   __dp2a                                   *
 ******************************************************************************/
// Generic [_lo]
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p signed \p int16 by \p int8 dot product with \p int32 accumulate,
 * taking the lower half of the second input.
 *
 * \details Extracts two packed 16-bit integers from \p scrA
 * and two packed 8-bit integers from the lower 16 bits of \p srcB,
 * then creates two pairwise 8x16 products and adds them together
 * to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp2a_lo(int srcA, int srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p unsigned \p int16 by \p int8 dot product with
 * \p unsigned \p int32 accumulate, taking the lower half of the second input.
 *
 * \details Extracts two packed 16-bit integers from \p scrA
 * and two packed 8-bit integers from the lower 16 bits of \p srcB,
 * then creates two pairwise 8x16 products and adds them together
 * to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp2a_lo(unsigned int srcA, unsigned int srcB, unsigned int c) __DEF_IF_HOST
// Vector-style [_lo]
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p signed \p int16 by \p int8 dot product with \p int32 accumulate,
 * taking the lower half of the second input.
 *
 * \details Takes two packed 16-bit integers from \p scrA vector
 * and two packed 8-bit integers from the lower 16 bits of \p srcB vector,
 * then creates two pairwise 8x16 products and adds them together
 * to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp2a_lo(short2 srcA, char4 srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p unsigned \p int16 by \p int8 dot product with
 * \p unsigned \p int32 accumulate, taking the lower half of the second input.
 *
 * \details Takes two packed 16-bit integers from \p scrA vector
 * and two packed 8-bit integers from the lower 16 bits of \p srcB vector,
 * then creates two pairwise 8x16 products and adds them together
 * to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp2a_lo(ushort2 srcA, uchar4 srcB, unsigned int c) __DEF_IF_HOST
// Generic [_hi]
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p signed \p int16 by \p int8 dot product with \p int32 accumulate,
 * taking the upper half of the second input.
 *
 * \details Extracts two packed 16-bit integers from \p scrA
 * and two packed 8-bit integers from the upper 16 bits of \p srcB,
 * then creates two pairwise 8x16 products and adds them together
 * to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp2a_hi(int srcA, int srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p unsigned \p int16 by \p int8 dot product with
 * \p unsigned \p int32 accumulate, taking the upper half of the second input.
 *
 * \details Extracts two packed 16-bit integers from \p scrA
 * and two packed 8-bit integers from the upper 16 bits of \p srcB,
 * then creates two pairwise 8x16 products and adds them together
 * to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp2a_hi(unsigned int srcA, unsigned int srcB, unsigned int c) __DEF_IF_HOST
// Vector-style [_hi]
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p signed \p int16 by \p int8 dot product with \p int32 accumulate,
 * taking the upper half of the second input.
 *
 * \details Takes two packed 16-bit integers from \p scrA vector
 * and two packed 8-bit integers from the upper 16 bits of \p srcB vector,
 * then creates two pairwise 8x16 products and adds them together
 * to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp2a_hi(short2 srcA, char4 srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Two-way \p unsigned \p int16 by \p int8 dot product with
 * \p unsigned \p int32 accumulate, taking the upper half of the second input.
 *
 * \details Takes two packed 16-bit integers from \p scrA vector
 * and two packed 8-bit integers from the upper 16 bits of \p srcB vector,
 * then creates two pairwise 8x16 products and adds them together
 * to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp2a_hi(ushort2 srcA, uchar4 srcB, unsigned int c) __DEF_IF_HOST


/******************************************************************************
 *                                   __dp4a                                   *
 ******************************************************************************/
// Generic
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Four-way \p signed \p int8 dot product with \p int32 accumulate.
 *
 * \details Extracts four pairs of packed byte-sized integers from \p scrA
 * and \p srcB, then creates four pairwise products and adds them together
 * to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp4a(int srcA, int srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Four-way \p unsigned \p int8 dot product with \p unsigned \p int32 accumulate.
 *
 * \details Extracts four pairs of packed byte-sized integers from \p scrA
 * and \p srcB, then creates four pairwise products and adds them together
 * to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp4a(unsigned int srcA, unsigned int srcB, unsigned int c) __DEF_IF_HOST
// Vector-style
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Four-way \p signed \p int8 dot product with \p int32 accumulate.
 *
 * \details Takes four pairs of packed byte-sized integers from \p scrA
 * and \p srcB vectors, then creates four pairwise products and adds them
 * together to a signed 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ int __dp4a(char4 srcA, char4 srcB, int c) __DEF_IF_HOST
/**
 * \ingroup CUDA_MATH_INTRINSIC_INT
 * \brief Four-way \p unsigned \p int8 dot product with \p unsigned \p int32 accumulate.
 *
 * \details Takes four pairs of packed byte-sized integers from \p scrA
 * and \p srcB vectors, then creates four pairwise products and adds them
 * together to an unsigned 32-bit integer \p c.
 */
__SM_61_INTRINSICS_DECL__ unsigned int __dp4a(uchar4 srcA, uchar4 srcB, unsigned int c) __DEF_IF_HOST

#endif /* _NVHPC_CUDA || !__CUDA_ARCH__ || __CUDA_ARCH__ >= 610 */

#endif /* __cplusplus && __CUDACC__ */

#undef __DEF_IF_HOST
#undef __SM_61_INTRINSICS_DECL__

#if !defined(__CUDACC_RTC__) && defined(__CUDA_ARCH__)
#include "sm_61_intrinsics.hpp"
#endif /* !__CUDACC_RTC__ && __CUDA_ARCH__ */

#endif /* !__SM_61_INTRINSICS_H__ */
#undef EXCLUDE_FROM_RTC