from typing import Any

import numpy as np

__all__ = [
    'geterr',
    'seterr',
    'errstate',
    'agm',
    'airy',
    'airye',
    'bdtr',
    'bdtrc',
    'bdtri',
    'bdtrik',
    'bdtrin',
    'bei',
    'beip',
    'ber',
    'berp',
    'besselpoly',
    'beta',
    'betainc',
    'betaincc',
    'betainccinv',
    'betaincinv',
    'betaln',
    'binom',
    'boxcox',
    'boxcox1p',
    'btdtria',
    'btdtrib',
    'cbrt',
    'chdtr',
    'chdtrc',
    'chdtri',
    'chdtriv',
    'chndtr',
    'chndtridf',
    'chndtrinc',
    'chndtrix',
    'cosdg',
    'cosm1',
    'cotdg',
    'dawsn',
    'ellipe',
    'ellipeinc',
    'ellipj',
    'ellipk',
    'ellipkinc',
    'ellipkm1',
    'elliprc',
    'elliprd',
    'elliprf',
    'elliprg',
    'elliprj',
    'entr',
    'erf',
    'erfc',
    'erfcinv',
    'erfcx',
    'erfi',
    'erfinv',
    'eval_chebyc',
    'eval_chebys',
    'eval_chebyt',
    'eval_chebyu',
    'eval_gegenbauer',
    'eval_genlaguerre',
    'eval_hermite',
    'eval_hermitenorm',
    'eval_jacobi',
    'eval_laguerre',
    'eval_legendre',
    'eval_sh_chebyt',
    'eval_sh_chebyu',
    'eval_sh_jacobi',
    'eval_sh_legendre',
    'exp1',
    'exp10',
    'exp2',
    'expi',
    'expit',
    'expm1',
    'expn',
    'exprel',
    'fdtr',
    'fdtrc',
    'fdtri',
    'fdtridfd',
    'fresnel',
    'gamma',
    'gammainc',
    'gammaincc',
    'gammainccinv',
    'gammaincinv',
    'gammaln',
    'gammasgn',
    'gdtr',
    'gdtrc',
    'gdtria',
    'gdtrib',
    'gdtrix',
    'hankel1',
    'hankel1e',
    'hankel2',
    'hankel2e',
    'huber',
    'hyp0f1',
    'hyp1f1',
    'hyp2f1',
    'hyperu',
    'i0',
    'i0e',
    'i1',
    'i1e',
    'inv_boxcox',
    'inv_boxcox1p',
    'it2i0k0',
    'it2j0y0',
    'it2struve0',
    'itairy',
    'iti0k0',
    'itj0y0',
    'itmodstruve0',
    'itstruve0',
    'iv',
    'ive',
    'j0',
    'j1',
    'jn',
    'jv',
    'jve',
    'k0',
    'k0e',
    'k1',
    'k1e',
    'kei',
    'keip',
    'kelvin',
    'ker',
    'kerp',
    'kl_div',
    'kn',
    'kolmogi',
    'kolmogorov',
    'kv',
    'kve',
    'log1p',
    'log_expit',
    'log_ndtr',
    'log_wright_bessel',
    'loggamma',
    'logit',
    'lpmv',
    'mathieu_a',
    'mathieu_b',
    'mathieu_cem',
    'mathieu_modcem1',
    'mathieu_modcem2',
    'mathieu_modsem1',
    'mathieu_modsem2',
    'mathieu_sem',
    'modfresnelm',
    'modfresnelp',
    'modstruve',
    'nbdtr',
    'nbdtrc',
    'nbdtri',
    'nbdtrik',
    'nbdtrin',
    'ncfdtr',
    'ncfdtri',
    'ncfdtridfd',
    'ncfdtridfn',
    'ncfdtrinc',
    'nctdtr',
    'nctdtridf',
    'nctdtrinc',
    'nctdtrit',
    'ndtr',
    'ndtri',
    'ndtri_exp',
    'nrdtrimn',
    'nrdtrisd',
    'obl_ang1',
    'obl_ang1_cv',
    'obl_cv',
    'obl_rad1',
    'obl_rad1_cv',
    'obl_rad2',
    'obl_rad2_cv',
    'owens_t',
    'pbdv',
    'pbvv',
    'pbwa',
    'pdtr',
    'pdtrc',
    'pdtri',
    'pdtrik',
    'poch',
    'powm1',
    'pro_ang1',
    'pro_ang1_cv',
    'pro_cv',
    'pro_rad1',
    'pro_rad1_cv',
    'pro_rad2',
    'pro_rad2_cv',
    'pseudo_huber',
    'psi',
    'radian',
    'rel_entr',
    'rgamma',
    'round',
    'shichi',
    'sici',
    'sindg',
    'smirnov',
    'smirnovi',
    'spence',
    'sph_harm',
    'stdtr',
    'stdtridf',
    'stdtrit',
    'struve',
    'tandg',
    'tklmbda',
    'voigt_profile',
    'wofz',
    'wright_bessel',
    'wrightomega',
    'xlog1py',
    'xlogy',
    'y0',
    'y1',
    'yn',
    'yv',
    'yve',
    'zetac'
]

def geterr() -> dict[str, str]: ...
def seterr(**kwargs: str) -> dict[str, str]: ...

class errstate:
    def __init__(self, **kargs: str) -> None: ...
    def __enter__(self) -> None: ...
    def __exit__(
        self,
        exc_type: Any,  # Unused
        exc_value: Any,  # Unused
        traceback: Any,  # Unused
    ) -> None: ...

_cosine_cdf: np.ufunc
_cosine_invcdf: np.ufunc
_cospi: np.ufunc
_ellip_harm: np.ufunc
_factorial: np.ufunc
_igam_fac: np.ufunc
_kolmogc: np.ufunc
_kolmogci: np.ufunc
_kolmogp: np.ufunc
_lambertw: np.ufunc
_lanczos_sum_expg_scaled: np.ufunc
_lgam1p: np.ufunc
_log1pmx: np.ufunc
_riemann_zeta: np.ufunc
_scaled_exp1: np.ufunc
_sf_error_test_function: np.ufunc
_sinpi: np.ufunc
_smirnovc: np.ufunc
_smirnovci: np.ufunc
_smirnovp: np.ufunc
_spherical_in: np.ufunc
_spherical_in_d: np.ufunc
_spherical_jn: np.ufunc
_spherical_jn_d: np.ufunc
_spherical_kn: np.ufunc
_spherical_kn_d: np.ufunc
_spherical_yn: np.ufunc
_spherical_yn_d: np.ufunc
_stirling2_inexact: np.ufunc
_struve_asymp_large_z: np.ufunc
_struve_bessel_series: np.ufunc
_struve_power_series: np.ufunc
_zeta: np.ufunc
agm: np.ufunc
airy: np.ufunc
airye: np.ufunc
bdtr: np.ufunc
bdtrc: np.ufunc
bdtri: np.ufunc
bdtrik: np.ufunc
bdtrin: np.ufunc
bei: np.ufunc
beip: np.ufunc
ber: np.ufunc
berp: np.ufunc
besselpoly: np.ufunc
beta: np.ufunc
betainc: np.ufunc
betaincc: np.ufunc
betainccinv: np.ufunc
betaincinv: np.ufunc
betaln: np.ufunc
binom: np.ufunc
boxcox1p: np.ufunc
boxcox: np.ufunc
btdtria: np.ufunc
btdtrib: np.ufunc
cbrt: np.ufunc
chdtr: np.ufunc
chdtrc: np.ufunc
chdtri: np.ufunc
chdtriv: np.ufunc
chndtr: np.ufunc
chndtridf: np.ufunc
chndtrinc: np.ufunc
chndtrix: np.ufunc
cosdg: np.ufunc
cosm1: np.ufunc
cotdg: np.ufunc
dawsn: np.ufunc
ellipe: np.ufunc
ellipeinc: np.ufunc
ellipj: np.ufunc
ellipk: np.ufunc
ellipkinc: np.ufunc
ellipkm1: np.ufunc
elliprc: np.ufunc
elliprd: np.ufunc
elliprf: np.ufunc
elliprg: np.ufunc
elliprj: np.ufunc
entr: np.ufunc
erf: np.ufunc
erfc: np.ufunc
erfcinv: np.ufunc
erfcx: np.ufunc
erfi: np.ufunc
erfinv: np.ufunc
eval_chebyc: np.ufunc
eval_chebys: np.ufunc
eval_chebyt: np.ufunc
eval_chebyu: np.ufunc
eval_gegenbauer: np.ufunc
eval_genlaguerre: np.ufunc
eval_hermite: np.ufunc
eval_hermitenorm: np.ufunc
eval_jacobi: np.ufunc
eval_laguerre: np.ufunc
eval_legendre: np.ufunc
eval_sh_chebyt: np.ufunc
eval_sh_chebyu: np.ufunc
eval_sh_jacobi: np.ufunc
eval_sh_legendre: np.ufunc
exp10: np.ufunc
exp1: np.ufunc
exp2: np.ufunc
expi: np.ufunc
expit: np.ufunc
expm1: np.ufunc
expn: np.ufunc
exprel: np.ufunc
fdtr: np.ufunc
fdtrc: np.ufunc
fdtri: np.ufunc
fdtridfd: np.ufunc
fresnel: np.ufunc
gamma: np.ufunc
gammainc: np.ufunc
gammaincc: np.ufunc
gammainccinv: np.ufunc
gammaincinv: np.ufunc
gammaln: np.ufunc
gammasgn: np.ufunc
gdtr: np.ufunc
gdtrc: np.ufunc
gdtria: np.ufunc
gdtrib: np.ufunc
gdtrix: np.ufunc
hankel1: np.ufunc
hankel1e: np.ufunc
hankel2: np.ufunc
hankel2e: np.ufunc
huber: np.ufunc
hyp0f1: np.ufunc
hyp1f1: np.ufunc
hyp2f1: np.ufunc
hyperu: np.ufunc
i0: np.ufunc
i0e: np.ufunc
i1: np.ufunc
i1e: np.ufunc
inv_boxcox1p: np.ufunc
inv_boxcox: np.ufunc
it2i0k0: np.ufunc
it2j0y0: np.ufunc
it2struve0: np.ufunc
itairy: np.ufunc
iti0k0: np.ufunc
itj0y0: np.ufunc
itmodstruve0: np.ufunc
itstruve0: np.ufunc
iv: np.ufunc
ive: np.ufunc
j0: np.ufunc
j1: np.ufunc
jn: np.ufunc
jv: np.ufunc
jve: np.ufunc
k0: np.ufunc
k0e: np.ufunc
k1: np.ufunc
k1e: np.ufunc
kei: np.ufunc
keip: np.ufunc
kelvin: np.ufunc
ker: np.ufunc
kerp: np.ufunc
kl_div: np.ufunc
kn: np.ufunc
kolmogi: np.ufunc
kolmogorov: np.ufunc
kv: np.ufunc
kve: np.ufunc
log1p: np.ufunc
log_expit: np.ufunc
log_ndtr: np.ufunc
log_wright_bessel: np.ufunc
loggamma: np.ufunc
logit: np.ufunc
lpmv: np.ufunc
mathieu_a: np.ufunc
mathieu_b: np.ufunc
mathieu_cem: np.ufunc
mathieu_modcem1: np.ufunc
mathieu_modcem2: np.ufunc
mathieu_modsem1: np.ufunc
mathieu_modsem2: np.ufunc
mathieu_sem: np.ufunc
modfresnelm: np.ufunc
modfresnelp: np.ufunc
modstruve: np.ufunc
nbdtr: np.ufunc
nbdtrc: np.ufunc
nbdtri: np.ufunc
nbdtrik: np.ufunc
nbdtrin: np.ufunc
ncfdtr: np.ufunc
ncfdtri: np.ufunc
ncfdtridfd: np.ufunc
ncfdtridfn: np.ufunc
ncfdtrinc: np.ufunc
nctdtr: np.ufunc
nctdtridf: np.ufunc
nctdtrinc: np.ufunc
nctdtrit: np.ufunc
ndtr: np.ufunc
ndtri: np.ufunc
ndtri_exp: np.ufunc
nrdtrimn: np.ufunc
nrdtrisd: np.ufunc
obl_ang1: np.ufunc
obl_ang1_cv: np.ufunc
obl_cv: np.ufunc
obl_rad1: np.ufunc
obl_rad1_cv: np.ufunc
obl_rad2: np.ufunc
obl_rad2_cv: np.ufunc
owens_t: np.ufunc
pbdv: np.ufunc
pbvv: np.ufunc
pbwa: np.ufunc
pdtr: np.ufunc
pdtrc: np.ufunc
pdtri: np.ufunc
pdtrik: np.ufunc
poch: np.ufunc
powm1: np.ufunc
pro_ang1: np.ufunc
pro_ang1_cv: np.ufunc
pro_cv: np.ufunc
pro_rad1: np.ufunc
pro_rad1_cv: np.ufunc
pro_rad2: np.ufunc
pro_rad2_cv: np.ufunc
pseudo_huber: np.ufunc
psi: np.ufunc
radian: np.ufunc
rel_entr: np.ufunc
rgamma: np.ufunc
round: np.ufunc
shichi: np.ufunc
sici: np.ufunc
sindg: np.ufunc
smirnov: np.ufunc
smirnovi: np.ufunc
spence: np.ufunc
sph_harm: np.ufunc
stdtr: np.ufunc
stdtridf: np.ufunc
stdtrit: np.ufunc
struve: np.ufunc
tandg: np.ufunc
tklmbda: np.ufunc
voigt_profile: np.ufunc
wofz: np.ufunc
wright_bessel: np.ufunc
wrightomega: np.ufunc
xlog1py: np.ufunc
xlogy: np.ufunc
y0: np.ufunc
y1: np.ufunc
yn: np.ufunc
yv: np.ufunc
yve: np.ufunc
zetac: np.ufunc

