scikit_image-0.25.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scikit_image-0.25.2.dist-info/LICENSE.txt,sha256=YR0yB1BNy0qAjfIJ0ufJtPLon-aQ-Ec1wNVIFglmtSc,6435
scikit_image-0.25.2.dist-info/METADATA,sha256=78C5N2fDpfI5xcj4NdqaE63c-rlTOmlxFzo20Gh9uTQ,14355
scikit_image-0.25.2.dist-info/RECORD,,
scikit_image-0.25.2.dist-info/WHEEL,sha256=6uXuBuTHKYVHX38njLnDjCYRk1Z5gwaXJtzFqt6LRKw,137
skimage/__init__.py,sha256=0xoAHLxU20QDimJE--uurfxEDzWk1ySZ11yV9x0ODd4,4107
skimage/__init__.pyi,sha256=-q2FUJzv9v_H7mN2vSJuwDSZedVPCpVDUQQb4-sz5i0,848
skimage/__pycache__/__init__.cpython-311.pyc,,
skimage/__pycache__/conftest.cpython-311.pyc,,
skimage/_shared/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/__pycache__/__init__.cpython-311.pyc,,
skimage/_shared/__pycache__/_dependency_checks.cpython-311.pyc,,
skimage/_shared/__pycache__/_geometry.cpython-311.pyc,,
skimage/_shared/__pycache__/_tempfile.cpython-311.pyc,,
skimage/_shared/__pycache__/_warnings.cpython-311.pyc,,
skimage/_shared/__pycache__/compat.cpython-311.pyc,,
skimage/_shared/__pycache__/coord.cpython-311.pyc,,
skimage/_shared/__pycache__/dtype.cpython-311.pyc,,
skimage/_shared/__pycache__/filters.cpython-311.pyc,,
skimage/_shared/__pycache__/tester.cpython-311.pyc,,
skimage/_shared/__pycache__/testing.cpython-311.pyc,,
skimage/_shared/__pycache__/utils.cpython-311.pyc,,
skimage/_shared/__pycache__/version_requirements.cpython-311.pyc,,
skimage/_shared/_dependency_checks.py,sha256=6ZaHZf0d65U5cGMtSEegDUYQ_gJL1wSkC-w9zZkcA18,211
skimage/_shared/_geometry.py,sha256=vagNwhLEcKvM0k19agDXvhnZDjwjTL9au5O-CSuDCQY,1343
skimage/_shared/_tempfile.py,sha256=lWfbjc6DHgndl9D8L4Gflq6fPVfVp-GSOwLgc9mdmpo,763
skimage/_shared/_warnings.py,sha256=daVYJAaNlKvzrYNfoG6uRkz7IQos6zTkqZjnVflsfFE,5225
skimage/_shared/compat.py,sha256=GwI-eClJ6GpQgURalFbNfkH4P0az0441TRRwwPUxqus,976
skimage/_shared/coord.py,sha256=YgUPW4TYe1j3cYTGnlGYqnbRAtroPWzsyten1A3-gIw,4329
skimage/_shared/dtype.py,sha256=-6l3DaRdHqFRB9EnvMU5xqHxwN7c519Svv47qy1R-Sk,2397
skimage/_shared/fast_exp.cpython-311-x86_64-linux-gnu.so,sha256=0Bc0fHaDZCYUkjxY5bg-or02tg_DGCy3mD-40F9fHcQ,83336
skimage/_shared/fast_exp.h,sha256=HtIZ7X68IvcNN47SM5E1SxBgvVYziJKvzvawzO94vi8,1246
skimage/_shared/filters.py,sha256=yQZFHt97P8XyQuZ5qEkVxBT1KRSXoWBbBQf2NimST0I,4877
skimage/_shared/geometry.cpython-311-x86_64-linux-gnu.so,sha256=4UPHhTYlKxtR6MERJpu-RoNULP2IHmG6BKq64u_SOAQ,198336
skimage/_shared/interpolation.cpython-311-x86_64-linux-gnu.so,sha256=qjW7NJ_dgZwW2hcl_Y3g-OOtn30zQs19Q3YP2I3Zp24,59168
skimage/_shared/tester.py,sha256=bA-Foz30kjF7LLpnUsOcllI5HS38f7e7hogP0uxG0vo,3589
skimage/_shared/testing.py,sha256=EUPc8YyzJJdOuw4HQWt7cKT_CVK9FFvffXEyC9xyFhs,9443
skimage/_shared/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_shared/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_coord.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_dtype.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_fast_exp.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_geometry.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_interpolation.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_safe_as_int.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_testing.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_utils.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_version_requirements.cpython-311.pyc,,
skimage/_shared/tests/__pycache__/test_warnings.cpython-311.pyc,,
skimage/_shared/tests/test_coord.py,sha256=XjSxfX5yHcaLc9YwTj994kkPX823piDUC-z5LyCw9nc,3056
skimage/_shared/tests/test_dtype.py,sha256=wwqc5o2vwjTsxkjUrmYDrN-8nhM-WscaQaQe6K0aWB8,369
skimage/_shared/tests/test_fast_exp.py,sha256=w0Vmeds1ufT_bh8Y8jewLzB1X4luoOD82c_H2k3GMuk,490
skimage/_shared/tests/test_geometry.py,sha256=3CXvdgORLkvXZZm8xSNr9PLqG-prRADMWJTPWxRXDFs,2120
skimage/_shared/tests/test_interpolation.py,sha256=OxsQEISFgYSb_neW9hCMvT2R30fYIFAqK7CnLTY3WSI,1137
skimage/_shared/tests/test_safe_as_int.py,sha256=uNtuC58dXNXzcbErawF7ModKJZiVcaK8vUnrv4ZFkSs,1424
skimage/_shared/tests/test_testing.py,sha256=hmbDxV-qArgB4vfO0Os_E9Weh3OPzNrhTOEYAn7iOS8,4204
skimage/_shared/tests/test_utils.py,sha256=zIfCaeZTBJmm-RdwRZM6aqmmBwgC5Oy6LbLejhc801I,15616
skimage/_shared/tests/test_version_requirements.py,sha256=o2My9JA0fbIik6bXv-hWevyuQrWRWdbFWCr23ee99tE,1075
skimage/_shared/tests/test_warnings.py,sha256=O-IVek3zadm58cuq_P-F59DX_6ETBHjiASAZ3sEB_8o,1250
skimage/_shared/transform.cpython-311-x86_64-linux-gnu.so,sha256=ALibunnwyCTzwWLJNpuilXeZGPh0o_7UMy_3BqRAmAo,198536
skimage/_shared/utils.py,sha256=aAWaxbetX2TeV-vVAH9sSv0q-aWMBF-2hrBYXzFCwvU,29655
skimage/_shared/version_requirements.py,sha256=ogsM5Qn6Zb6AAU_f6M4N7r3Vo2-NLUxT1RG0VSqZK2w,4347
skimage/_vendored/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/_vendored/__pycache__/__init__.cpython-311.pyc,,
skimage/_vendored/__pycache__/numpy_lookfor.cpython-311.pyc,,
skimage/_vendored/numpy_lookfor.py,sha256=jX7qOi9QMR3oPRFiMTA15X6hHNyjvHNqHbw9w-s2H0k,9669
skimage/color/__init__.py,sha256=QDtz_aFnz27WW9iaRg8rXNS9_8EQpNKFLB8Zzi7m-qw,130
skimage/color/__init__.pyi,sha256=03z6CYNjJon-6DHGJF1xHPRr0ZhCcXOzkn1owqORxXY,2382
skimage/color/__pycache__/__init__.cpython-311.pyc,,
skimage/color/__pycache__/adapt_rgb.cpython-311.pyc,,
skimage/color/__pycache__/colorconv.cpython-311.pyc,,
skimage/color/__pycache__/colorlabel.cpython-311.pyc,,
skimage/color/__pycache__/delta_e.cpython-311.pyc,,
skimage/color/__pycache__/rgb_colors.cpython-311.pyc,,
skimage/color/adapt_rgb.py,sha256=eWZhGvWQ3nucRZkrDP6sgnF6Ka0rL6BB7jxeXL4pcp8,2489
skimage/color/colorconv.py,sha256=BJJb9MJP36qa3YZinXS6VfHH9Xj8RFUqa4sBrpHjcqY,67505
skimage/color/colorlabel.py,sha256=v0VVZjMBf5_Dzq38w9txqJSzwjAQfA84ZL1_fnVE8JA,10506
skimage/color/delta_e.py,sha256=YwGF-yexqlZs6es1bFyuHEKa9wKuBeye74uLe6IF1JA,12706
skimage/color/rgb_colors.py,sha256=Nj-fsrXpuCtLFIjKMgzpvb6HkrpZiuvgDc5imTMGs9w,4493
skimage/color/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/color/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/color/tests/__pycache__/test_adapt_rgb.cpython-311.pyc,,
skimage/color/tests/__pycache__/test_colorconv.cpython-311.pyc,,
skimage/color/tests/__pycache__/test_colorlabel.cpython-311.pyc,,
skimage/color/tests/__pycache__/test_delta_e.cpython-311.pyc,,
skimage/color/tests/test_adapt_rgb.py,sha256=iXgHO2eYi3DTtou4Ddg1tquK2vVL-5DcWlJets14fyA,2761
skimage/color/tests/test_colorconv.py,sha256=ATNAUWNyTpzm_31HpXZeZwTEwTTYte2XsfZXh2oafxg,37267
skimage/color/tests/test_colorlabel.py,sha256=l1CUo4d0GNLXr-fcv4sedV9BokdNJKLwsmhYtf_JmDY,10906
skimage/color/tests/test_delta_e.py,sha256=-ggGG1FtoN1ShcWsLIaAEOTZ2eCwnOm5NS4OpA9QN0o,8087
skimage/conftest.py,sha256=KG_ignoqlB5vlbHIdtsVdobltaJRE83LhcG-9EjCKUI,310
skimage/data/README.txt,sha256=Wra5WN0KyiF-Pp3FNWCMJDKkdIKRCdEGTwPSSUYACyc,280
skimage/data/__init__.py,sha256=8Y4bk3tEAfBWnB-xKf7pXopkkP-AczTaXFxAQewZxS0,388
skimage/data/__init__.pyi,sha256=varbukgcATJEhBB6ICnSPDI7SgNyIu1d8WQYZgXHJIE,1411
skimage/data/__pycache__/__init__.cpython-311.pyc,,
skimage/data/__pycache__/_binary_blobs.cpython-311.pyc,,
skimage/data/__pycache__/_fetchers.cpython-311.pyc,,
skimage/data/__pycache__/_registry.cpython-311.pyc,,
skimage/data/_binary_blobs.py,sha256=hkOjiAH4pbAqLUEeY3_d8b1mz8paqSKKncBIMHVaRz8,2193
skimage/data/_fetchers.py,sha256=f0KeqiKE4Y33sXCxn2dUiDR_-Q3-4usQH7ztLAcvS94,38807
skimage/data/_registry.py,sha256=EClzXun1o6hXIZyLb4DRgdmw10RhDyJOAo-rr_2L9po,15860
skimage/data/astronaut.png,sha256=iEMc2WU8zVOXQbVV-wpGthVYswHUEQQStbwotePqbLU,791555
skimage/data/brick.png,sha256=eWbK8yT2uoQxGNmPegd0bSL2o0NDCt0CM-yl9uqqj88,106634
skimage/data/camera.png,sha256=sHk9Kt2g-mromcA5iUgr_5pC09VpD8fjZI8nldcwwjo,139512
skimage/data/cell.png,sha256=jSOn-4H3zId80J8zA1f8f1lWUTBuhOFyUvbgobP2FRU,74183
skimage/data/chelsea.png,sha256=WWqh58uHXrefQ34xA4HSazOKgcLaI0OXBKc8RlHoxLs,240512
skimage/data/chessboard_GRAY.png,sha256=PlGHB3RRWvTQfYIL2IJzZMcIOb-bVzx0bkhQleiT35A,418
skimage/data/chessboard_RGB.png,sha256=GsAe_y1OUPTtpVot3s3CimV2YjpY16fvhFE8XMGaAzE,1127
skimage/data/clock_motion.png,sha256=8Ckiayi2QugBE9hmIumyFe4Geglm_q9eYGBKHgVzOVU,58784
skimage/data/coffee.png,sha256=zAL4yhiLFnx3WnEBtddn0ecXks92LDPW-hWkWZtajec,466706
skimage/data/coins.png,sha256=-Ndz_Jz6b02OWULcNNCgeI_K7SpP77vtCu9TmNfvTLo,75825
skimage/data/color.png,sha256=fS35k94rT6KnjgTl34BQ9JqcURqnXlmrO9VqycmK734,85584
skimage/data/grass.png,sha256=trYCJCaziTbEOkrAljXNeK8HTpD0L_qCJ6yLdFLTn4k,217893
skimage/data/gravel.png,sha256=xIYVtFG_HmBvvXLAqp-MwPBoq3ER732Tu5sPJYZEDBI,194247
skimage/data/horse.png,sha256=x_tgeJ_jlMSF-EIpHqOyHlDRQPOdbctfuZF8wXgiVFU,16633
skimage/data/hubble_deep_field.jpg,sha256=OhnF3YqSepM0uxIpptY3EbHAx2f7J-IobnyEo-LC9fQ,527940
skimage/data/ihc.png,sha256=-N0ao4fd0fSditE7UJIbI3346bJiYG0lh3Boew75PO8,477916
skimage/data/lbpcascade_frontalface_opencv.xml,sha256=Awl3iaPcuw5A0gue-CU328O2cLan8iaNc1Rw8i4AOpE,51858
skimage/data/lfw_subset.npy,sha256=lWDsL17frAGXP2OoqZ0ABT_s0R4hh34YA4--UA-Ohyw,1000080
skimage/data/logo.png,sha256=8sV_6K8Inwi1ulI9lVc8JuYpBKxZZ_TIhRsn0DNpAWg,179723
skimage/data/microaneurysms.png,sha256=oeG-WapEf4zggvf6gJmXqzaaKxN8tsQgKrxkfHzPZFY,4950
skimage/data/moon.png,sha256=eHOWGdEffrnBZbtdLv1Hcs7lV4EuyEdTLbsdku9x9Xc,50177
skimage/data/motorcycle_disp.npz,sha256=LknIzr_z-iA1mgzGiAyC4cA7uxBtqBoXchgoG8LxE9c,1146173
skimage/data/motorcycle_left.png,sha256=2xjpxBV2F0A8NTemujVd_q_pp-q7a5uUyzP2Ul3UkXk,644701
skimage/data/motorcycle_right.png,sha256=X8kTrocOQqS2YjFLyQTReGvK2OLwubZ9uloilAY1d5c,640373
skimage/data/multipage.tif,sha256=TaCtDT30gHqYRyR9G15WW1DUZIH2Q6-1w3wUgCx4Ew8,940
skimage/data/multipage_rgb.tif,sha256=HSO4RP043ODi0G8wQygXzbheUgcNj1RgorpYrr80oN4,5278
skimage/data/no_time_for_that_tiny.gif,sha256=IKvpS6nkXxjeQWxfvvjR9XpJlgC-QPmiAPriRgEO784,4438
skimage/data/page.png,sha256=NBpvCmFVdmKwJzSptuVuwzqRWyxBiGuXUJ3t8qQ7R6M,47679
skimage/data/phantom.png,sha256=VS_2mBZ6pALM6xeYETBgeiKKCgqnxRkpnqpNXzAbo2w,3386
skimage/data/retina.jpg,sha256=OKB_NvJ_CV6Biup7ltNCAsBRdtMCU8ZnM_LgA3np4OY,269564
skimage/data/rocket.jpg,sha256=wt0N58U4340RHkeWGbEpRk0CadCuX9GMqR0zp_3-qVw,112525
skimage/data/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/data/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/data/tests/__pycache__/test_data.cpython-311.pyc,,
skimage/data/tests/test_data.py,sha256=WiBVkVqcOG5Uw31jjxXYxxGpv5u9gv5SUy2SV-q4Hjc,5344
skimage/data/text.png,sha256=vYSqOm48mIeFDUXWBslrLllDP771AzhXC2PDGeZo5tE,42704
skimage/draw/__init__.py,sha256=TjMI6gosMA2CafWXFmuObiMxykg9yCGlUAkc_j-A0UA,161
skimage/draw/__init__.pyi,sha256=dzGdH8dVkLPbpDxkcA9v7wQdAEbxHyhq1MqnWPWYLcM,963
skimage/draw/__pycache__/__init__.cpython-311.pyc,,
skimage/draw/__pycache__/_polygon2mask.cpython-311.pyc,,
skimage/draw/__pycache__/_random_shapes.cpython-311.pyc,,
skimage/draw/__pycache__/draw.cpython-311.pyc,,
skimage/draw/__pycache__/draw3d.cpython-311.pyc,,
skimage/draw/__pycache__/draw_nd.cpython-311.pyc,,
skimage/draw/_draw.cpython-311-x86_64-linux-gnu.so,sha256=R_ODs408BKMtDfRnOIiSWWmRVDAuP3TSOvgKgjzcVOA,410232
skimage/draw/_polygon2mask.py,sha256=Y1dShd48WRbxpRqvPEvURPPxwFKaR0g-RY11_epKoyU,2472
skimage/draw/_random_shapes.py,sha256=tkB2pDoNYxoW_9VP2qUtnKeplNewLSpS6_x9J1kuBdI,16010
skimage/draw/draw.py,sha256=-Nyt6p05jtPhYqz5z3Awy0HPRytLsGqqMnvHax-svkk,33672
skimage/draw/draw3d.py,sha256=eMmpG3VNigylno7M11-pRY3BKlKSMPVChY1O5q5sV8o,3287
skimage/draw/draw_nd.py,sha256=RKG1GHjsTyXN8J2nWJGVDeoqG3srqPuqmTReZsJst_4,3692
skimage/draw/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/draw/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/draw/tests/__pycache__/test_draw.cpython-311.pyc,,
skimage/draw/tests/__pycache__/test_draw3d.cpython-311.pyc,,
skimage/draw/tests/__pycache__/test_draw_nd.cpython-311.pyc,,
skimage/draw/tests/__pycache__/test_polygon2mask.cpython-311.pyc,,
skimage/draw/tests/__pycache__/test_random_shapes.cpython-311.pyc,,
skimage/draw/tests/test_draw.py,sha256=An0-U8Pt-lN2QT-j99mQvJBZrOYsjLn_-gt07IcJscI,41136
skimage/draw/tests/test_draw3d.py,sha256=jKSpKmoYbkp9AYE40QWK8t_6sfmXraDooZBXe9NWtKM,6559
skimage/draw/tests/test_draw_nd.py,sha256=W9-C2gRov5aVmXhbKxdTep5X69bie0XpfuYu-u-KL44,485
skimage/draw/tests/test_polygon2mask.py,sha256=jvWREquUe7C1Ufjhc5yhe6qd4v15M1cLI0176lujjFs,329
skimage/draw/tests/test_random_shapes.py,sha256=PCgaS5KkUj1Gyf7JDYIiAjYjtLWNTLPyVHvbQSG3dWY,5481
skimage/exposure/__init__.py,sha256=8MMNgHdza2XatOj7Zf9h5xpl3O2gHeMn8N1gpIHKMJo,169
skimage/exposure/__init__.pyi,sha256=HKwoewNTdeXl1yHaCJqErXZbxT4tLpBtfjYz53PnPQk,682
skimage/exposure/__pycache__/__init__.cpython-311.pyc,,
skimage/exposure/__pycache__/_adapthist.cpython-311.pyc,,
skimage/exposure/__pycache__/exposure.cpython-311.pyc,,
skimage/exposure/__pycache__/histogram_matching.cpython-311.pyc,,
skimage/exposure/_adapthist.py,sha256=uxRzK1pMT0Gbp1dFS2W-22I8en155yqHDXwE8NodWsQ,10971
skimage/exposure/exposure.py,sha256=YJ23YDwc3b6Q5CfpMX6k3HduMI7wNIyZ-2IacrAQzgY,27785
skimage/exposure/histogram_matching.py,sha256=Q7_OwedrtvDcPO4KTRfqq-gxD6SQXqm2NfhPmUZhZA0,3194
skimage/exposure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/exposure/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/exposure/tests/__pycache__/test_exposure.cpython-311.pyc,,
skimage/exposure/tests/__pycache__/test_histogram_matching.cpython-311.pyc,,
skimage/exposure/tests/test_exposure.py,sha256=JZwxWAhem1fHzgQ6QubKSZ8MzIhKv6PPEEYdq_M3UzU,37090
skimage/exposure/tests/test_histogram_matching.py,sha256=f1BByEh05wfR3v2VrxMcrjdLkevnmNuhmouLJPKeLsM,5157
skimage/feature/__init__.py,sha256=paLASf66MmyJ8A1Ha8HE0WOn1GDnSioXhHIvA49gJ4g,178
skimage/feature/__init__.pyi,sha256=weDA8amsNEfeKiXUnHbRiuDCOKXt-saPei6aTkdhIw8,2148
skimage/feature/__pycache__/__init__.cpython-311.pyc,,
skimage/feature/__pycache__/_basic_features.cpython-311.pyc,,
skimage/feature/__pycache__/_canny.cpython-311.pyc,,
skimage/feature/__pycache__/_daisy.cpython-311.pyc,,
skimage/feature/__pycache__/_fisher_vector.cpython-311.pyc,,
skimage/feature/__pycache__/_hog.cpython-311.pyc,,
skimage/feature/__pycache__/_orb_descriptor_positions.cpython-311.pyc,,
skimage/feature/__pycache__/blob.cpython-311.pyc,,
skimage/feature/__pycache__/brief.cpython-311.pyc,,
skimage/feature/__pycache__/censure.cpython-311.pyc,,
skimage/feature/__pycache__/corner.cpython-311.pyc,,
skimage/feature/__pycache__/haar.cpython-311.pyc,,
skimage/feature/__pycache__/match.cpython-311.pyc,,
skimage/feature/__pycache__/orb.cpython-311.pyc,,
skimage/feature/__pycache__/peak.cpython-311.pyc,,
skimage/feature/__pycache__/sift.cpython-311.pyc,,
skimage/feature/__pycache__/template.cpython-311.pyc,,
skimage/feature/__pycache__/texture.cpython-311.pyc,,
skimage/feature/__pycache__/util.cpython-311.pyc,,
skimage/feature/_basic_features.py,sha256=3WBA3AMZks0u4MeVDV1Ry1Hms_NLweiqva6RzxIiHEY,6778
skimage/feature/_canny.py,sha256=g4aAemdO553jRF04M7oSXWQDvaE9b_29eewQWbIX9GM,9466
skimage/feature/_canny_cy.cpython-311-x86_64-linux-gnu.so,sha256=X5sA_hV_aSMz6zKK7zD6DUif4nLNKK6YayPzaIu6lfs,281664
skimage/feature/_cascade.cpython-311-x86_64-linux-gnu.so,sha256=7wqBa69m0zVEx7RBR0Vm070aIDFWlhGryTIMO_p4yh0,359336
skimage/feature/_daisy.py,sha256=BC42Hao8q2P5MHE0syRThFRfmBa5JeGNzDG1g3Ee9MA,10064
skimage/feature/_fisher_vector.py,sha256=QzY3m_kA6pJVowDSnNQzwnB1Bou0YTz8EIureYN6BL4,10511
skimage/feature/_haar.cpython-311-x86_64-linux-gnu.so,sha256=A4xckxU6moS05GiHYGJoiCENtNCEloclOiNnRNvPH8Y,570672
skimage/feature/_hessian_det_appx.cpython-311-x86_64-linux-gnu.so,sha256=QP6Yy9DmoljI4yaRUDis9EXoR0I5eexQZ_Wz9p7KK3o,85664
skimage/feature/_hog.py,sha256=APuWjYzXNJId9FKAz3PXpn-g4pJfw2KDOO-rp99fqBI,13208
skimage/feature/_hoghistogram.cpython-311-x86_64-linux-gnu.so,sha256=Xyrpp4SKioe9-ZRHTzYKYaZjYWDWsXNM_1cwoy7A_fk,294928
skimage/feature/_orb_descriptor_positions.py,sha256=zM1l5vjzmuH9QhABX2ZkgS19VoIb_7k08OzYL467anM,450
skimage/feature/_sift.cpython-311-x86_64-linux-gnu.so,sha256=EtDeBy1HXBGncPLH3TUvj1R3xsV8f5w8n7Swp7DkBso,358784
skimage/feature/_texture.cpython-311-x86_64-linux-gnu.so,sha256=tf3kLGvK5Cgpg_PioC5RLZLsmTJVnL2Pa-tYLpeWx4I,403232
skimage/feature/blob.py,sha256=z2Zc-fJBXzsb7r1TpgGU4y7wRA4f6sTbY3n4uaynDAU,27885
skimage/feature/brief.py,sha256=sOAu-qx44Np32s0lzzAzkIt6CRpFf8h4RKdvivLYG1Y,8115
skimage/feature/brief_cy.cpython-311-x86_64-linux-gnu.so,sha256=4pRVFeFsBebQqtM-b9EY9M1cnJBdn1rhXy6T-LfWe48,236344
skimage/feature/censure.py,sha256=TCW-RMHVpyFf51qBhrwMjRlQ5OfJIapHkRDgA5IPgzM,12003
skimage/feature/censure_cy.cpython-311-x86_64-linux-gnu.so,sha256=EuQ6_saykOnNEZ_SpTN3Ku7jpHWADKPZp1h18f2KwFE,231632
skimage/feature/corner.py,sha256=uKXtC13ILrZzIRBKPwh03sJ090ozqrKnMql1fXr5scE,45569
skimage/feature/corner_cy.cpython-311-x86_64-linux-gnu.so,sha256=mCJ2U1Lx3Gd1YP_lifjeL7yfT_0mmQdi9sdXNr6GDZo,381896
skimage/feature/haar.py,sha256=sGtUnmD8-KGGvsK-6risY-lTH-CYL4tBrbFTGFjhPRA,12921
skimage/feature/match.py,sha256=WayyFreFmSp02NEayvsjQt4HYwUoJPftHvTj9jVZBDs,4023
skimage/feature/orb.py,sha256=Bi41N8GYP6DtYlu5vjagmD0HTy_GqdEy5mdCwj2fCq8,13148
skimage/feature/orb_cy.cpython-311-x86_64-linux-gnu.so,sha256=RfVFa5Jaw4gbdP7dPggzs__aXAXBe3LdGXEPFGXLjTI,282856
skimage/feature/orb_descriptor_positions.txt,sha256=5lNSwCXegQMV0huO4FszyWGoPy1291cKt7Vpvddg8BE,2840
skimage/feature/peak.py,sha256=hb-0LUKtcVDz7saNV3je11VqNtOGz3lT4yaw0JdnzcM,14478
skimage/feature/sift.py,sha256=Rh7oRFE0XaA6w1emFjAxcal1Kd49AVkaXYvOi9Ktwkw,29407
skimage/feature/template.py,sha256=P8w0Wxx4voJQxMMbWGPIhled4NO9VSulT3Mf5qxEvfQ,6571
skimage/feature/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/feature/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_basic_features.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_blob.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_brief.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_canny.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_cascade.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_censure.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_corner.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_daisy.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_fisher_vector.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_haar.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_hog.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_match.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_orb.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_peak.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_sift.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_template.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_texture.cpython-311.pyc,,
skimage/feature/tests/__pycache__/test_util.cpython-311.pyc,,
skimage/feature/tests/test_basic_features.py,sha256=CpevV4VpfphjsQVIpfD8W6iPh56SejDhr-UpoDXTbO0,2092
skimage/feature/tests/test_blob.py,sha256=Eo-UNr1nJnPPZWjwzB6SSkHtAJ0MWamNTY-EeDegj2k,16199
skimage/feature/tests/test_brief.py,sha256=RBnlDa-J-XwaALha7ImGnIbVR_iraIyoG_JWeBWRqbw,3202
skimage/feature/tests/test_canny.py,sha256=oHWtVrwzEQadumXnBBdkS_zgvHDOgSOP7Q_u_zpUqvM,6388
skimage/feature/tests/test_cascade.py,sha256=uZnmMTtZUYeTRG-oJMLyKJDuS9o9ByGHmA0cPHpRdUs,514
skimage/feature/tests/test_censure.py,sha256=66Xit4dKcxnEmowgzy5ryc0Zdbii_fhUSISag_Ou5kE,3161
skimage/feature/tests/test_corner.py,sha256=8t1OjndiLrcMgYcFFK2a2ee5_yWWcUzeaS4gsNOKSTY,24187
skimage/feature/tests/test_daisy.py,sha256=XYgJOwW-6tLaEcHAhUwo_2QV_BbG2H1yNAH2U2Dusus,3393
skimage/feature/tests/test_fisher_vector.py,sha256=pmA0mq2Q-b8KltbfD5Fpqfpnp5Ks5n0VTLRC4wpTP58,5730
skimage/feature/tests/test_haar.py,sha256=tdewpi8wvEnEpkq8aKkFODuP9_g_Nk1qKXq7nkKsEnI,6659
skimage/feature/tests/test_hog.py,sha256=ch433R-JcroWz18LMi3BAJ8-b6HxZOoUoVBI4peCXu0,11720
skimage/feature/tests/test_match.py,sha256=0iVIAbz_JZhq8PVh787zJti_E5IeaGSZ33peA_Qljx4,8065
skimage/feature/tests/test_orb.py,sha256=0cO1P-ctZDBggWXpY0ZvPr9l3aERUs8h0g5f868fyCQ,6267
skimage/feature/tests/test_peak.py,sha256=BhSx6M4q8VNfh_RGFtjmg5R7AL_B8BQw8uxDy-rsICY,21988
skimage/feature/tests/test_sift.py,sha256=C1B2qOD3wrjlAuKoMnirXQhyPgCGpxCo3DDWwl6fQQo,5753
skimage/feature/tests/test_template.py,sha256=cSKzOc-o6X2ojyYqLe3I1LDSLcaa-EagVpfLt204VIY,6134
skimage/feature/tests/test_texture.py,sha256=s392qhy72ab_Z4rnP7GWZ8wGTOqK3ponL93ac-7LA3E,13596
skimage/feature/tests/test_util.py,sha256=fY1-kQb9g-HxCXNn4f14_q9_zumrWPDT1ZjTMz7e4JE,6164
skimage/feature/texture.py,sha256=pOt1rEzszGSlvXbA4L30NgEl7r-Paw86ZjTVV9aZmWo,20544
skimage/feature/util.py,sha256=v-9dcDXtQWLO6fuH_jhr-QBdIzCk7TJbBJUI2iAdYbA,7049
skimage/filters/__init__.py,sha256=1ehkljJM7R29gbYqXS75AZ6fNba7FMttFo67IFJSpD4,165
skimage/filters/__init__.pyi,sha256=KonQ6JGwzrRlNcoG5wOUmU7ujdNQkW2G5A7Ub52DGq8,2158
skimage/filters/__pycache__/__init__.cpython-311.pyc,,
skimage/filters/__pycache__/_fft_based.cpython-311.pyc,,
skimage/filters/__pycache__/_gabor.cpython-311.pyc,,
skimage/filters/__pycache__/_gaussian.cpython-311.pyc,,
skimage/filters/__pycache__/_median.cpython-311.pyc,,
skimage/filters/__pycache__/_rank_order.cpython-311.pyc,,
skimage/filters/__pycache__/_sparse.cpython-311.pyc,,
skimage/filters/__pycache__/_unsharp_mask.cpython-311.pyc,,
skimage/filters/__pycache__/_window.cpython-311.pyc,,
skimage/filters/__pycache__/edges.cpython-311.pyc,,
skimage/filters/__pycache__/lpi_filter.cpython-311.pyc,,
skimage/filters/__pycache__/ridges.cpython-311.pyc,,
skimage/filters/__pycache__/thresholding.cpython-311.pyc,,
skimage/filters/_fft_based.py,sha256=LUwb9JlvarVhLlSP5nPacsI6C5doVlJWufTh95QqOOg,6709
skimage/filters/_gabor.py,sha256=Ag--tND24VcErppMMsTMUsIRhyCxLDUv79lT8Jyg-As,7720
skimage/filters/_gaussian.py,sha256=fyHfkvJuEptkhm9USYrZMZ6dytWK39V2HqXYi7lxDvU,6037
skimage/filters/_median.py,sha256=4pp1HTIXijKWnt29j8rRTdqjXeRadYDPEGkOGQznjuQ,2964
skimage/filters/_multiotsu.cpython-311-x86_64-linux-gnu.so,sha256=NPQsmZf7OM9rPCXrjXu8YSd4UOKPg8Wxme88sMYIJnk,267696
skimage/filters/_rank_order.py,sha256=NUbi5sPtKTW-Xm6VZcwn0F5pV45yfaYDeHMEJtGy4nk,2057
skimage/filters/_sparse.py,sha256=jXiCqNqR5gUHFnhHoUVPsN16ufbU5CLEAQU_XexrI5g,4654
skimage/filters/_unsharp_mask.py,sha256=EoJ5JHrndcgrliDsef_WcQM6O3HGnswRUUW3tLBlUYs,5511
skimage/filters/_window.py,sha256=xO_yx7DSEXTBIWwPhXlsKNirmfH6Koc7k5Us_6A1wfY,4351
skimage/filters/edges.py,sha256=_z6jvZNt5_4DHc-tvhzgXv7ixQtNUvf0oZ5dmfzfV3M,25639
skimage/filters/lpi_filter.py,sha256=Z6SWW7EAM2Hj3sVOzQtUs9R_d7ZHwqIYAqNAdqpSOY8,7937
skimage/filters/rank/__init__.py,sha256=SFt54WGOb_evjktH87sKTcKk3i-4C2mqNN3c6nv46fw,1548
skimage/filters/rank/__pycache__/__init__.cpython-311.pyc,,
skimage/filters/rank/__pycache__/_percentile.cpython-311.pyc,,
skimage/filters/rank/__pycache__/bilateral.cpython-311.pyc,,
skimage/filters/rank/__pycache__/generic.cpython-311.pyc,,
skimage/filters/rank/_percentile.py,sha256=wmJYo8LeNrHgSYDMs-Lro8ErowHgGlbOjzxdcC8Dx-c,13895
skimage/filters/rank/bilateral.py,sha256=HpJnwjxRWsdpPRlZMBG0pZchLXrr0RJbVz-KFobsU0s,7822
skimage/filters/rank/bilateral_cy.cpython-311-x86_64-linux-gnu.so,sha256=nyfr6VDXjCiL-TFdQGryxc8J8J-6o-WrUAEf_5gyZzo,573520
skimage/filters/rank/core_cy.cpython-311-x86_64-linux-gnu.so,sha256=_8B43OXNBtHLAtOR90JHIZ-h5tgWcsQH_S3hbCn2GIQ,612672
skimage/filters/rank/core_cy_3d.cpython-311-x86_64-linux-gnu.so,sha256=lYllqNsBfGty9m65_sRkm8Dw-Ic63AQGWpmy953v1wc,363080
skimage/filters/rank/generic.py,sha256=6Sep8UCofRH_MiwHicSY42YX-qsoctYr4gf7sR9eae4,55984
skimage/filters/rank/generic_cy.cpython-311-x86_64-linux-gnu.so,sha256=tRYO9REhDxrkvO5NBESsnQMbn1kSe0VUhLP1IaJEjds,3972952
skimage/filters/rank/percentile_cy.cpython-311-x86_64-linux-gnu.so,sha256=_JwOZBqbbyUKUstQu8vR-TmKFkiY-RcmHyUNXoNIshg,1243400
skimage/filters/rank/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/rank/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/filters/rank/tests/__pycache__/test_rank.cpython-311.pyc,,
skimage/filters/rank/tests/test_rank.py,sha256=Juf040oJMF53n1ieo1i_-Dppvd-LV_mZkZvEd7KmdD0,38992
skimage/filters/ridges.py,sha256=vH-NFuH4M5VzbNUjW4fgs3-gOgaOyJeGhRi8lWXOC_M,14247
skimage/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/filters/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_correlate.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_edges.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_fft_based.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_gabor.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_gaussian.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_lpi_filter.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_median.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_ridges.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_thresholding.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_unsharp_mask.cpython-311.pyc,,
skimage/filters/tests/__pycache__/test_window.cpython-311.pyc,,
skimage/filters/tests/test_correlate.py,sha256=kd80vN3t94fOaZLvD9kl_iql3slFelLZ1A4wzzL7T_M,1995
skimage/filters/tests/test_edges.py,sha256=txSSnwoR2-XbesvX97oYTtp4vk6_SRLkJbbvv7XIqeg,21462
skimage/filters/tests/test_fft_based.py,sha256=OhqNpO1-1n5HotznDIoBMfc79e_8ua_RqBOuJrDTLh4,14556
skimage/filters/tests/test_gabor.py,sha256=BMfvYzHi1ZCmpXsVMcs12Rfawzz2pA_AG-YyuThkReU,3689
skimage/filters/tests/test_gaussian.py,sha256=82x-GcssO88CrGDjsiafSoDQOSFJ461JXquhqnmplk8,5454
skimage/filters/tests/test_lpi_filter.py,sha256=joUHee4ZFTvUuGs6SZKYw_WZVEAYCeMghSb7i27R7ts,3097
skimage/filters/tests/test_median.py,sha256=T9JoGWrDMAHwAZZ-_uFvonx9Fj4Q14aAjz4GTnHLjeQ,2113
skimage/filters/tests/test_ridges.py,sha256=T8QK0BJv8W282q0H-dA8LsYXtaRszFmYDpqYMDUZoe8,9538
skimage/filters/tests/test_thresholding.py,sha256=87P58TmJihPOdCgostVVWullDGBdHvawLmcez5rhhsI,26407
skimage/filters/tests/test_unsharp_mask.py,sha256=hv6ZVRGiH93Z5f1SxsBgNG83IRRtjCavuSnd2PIAq_o,5056
skimage/filters/tests/test_window.py,sha256=6_HHYrhGAIhh4QHE4XwSAMrido4gLiwIZRc_OfYKgEE,1624
skimage/filters/thresholding.py,sha256=0NuTX4VQbA2vRDN8VqAdIN4HlTkB-IthBZ1dsSoaZf0,47905
skimage/future/__init__.py,sha256=YX1ggMLn08L_Cy-UzcKlEJd1JbFjGAtWGGht705VXZE,506
skimage/future/__init__.pyi,sha256=q-RbSvWEFevrbvLiFJ44XiFL6jPKvSV-NqwVQ0FZMAk,493
skimage/future/__pycache__/__init__.cpython-311.pyc,,
skimage/future/__pycache__/manual_segmentation.cpython-311.pyc,,
skimage/future/__pycache__/trainable_segmentation.cpython-311.pyc,,
skimage/future/manual_segmentation.py,sha256=EQ-atSKTONFrnVjuGAia5XZdoKQ-Z_PtoHRGsDJY4r4,7610
skimage/future/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/future/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/future/tests/__pycache__/test_trainable_segmentation.cpython-311.pyc,,
skimage/future/tests/test_trainable_segmentation.py,sha256=Wr2uvOtG4LUf1mzrRtD5HphzSkSpTDJOZhIE_d9FTqY,4223
skimage/future/trainable_segmentation.py,sha256=ziB_HovaKyLENPGUaCPABXoIxBh7wkOW0894qTWWFIQ,5604
skimage/graph/__init__.py,sha256=fXO5xY04KgnleV0BeITsL522zKGo4LaC7Bi6V4rh-c4,338
skimage/graph/__init__.pyi,sha256=-fh5gug7Wi0FFVl0BDxTla37aXRDOsNfmHtHC_r7HJk,782
skimage/graph/__pycache__/__init__.cpython-311.pyc,,
skimage/graph/__pycache__/_graph.cpython-311.pyc,,
skimage/graph/__pycache__/_graph_cut.cpython-311.pyc,,
skimage/graph/__pycache__/_graph_merge.cpython-311.pyc,,
skimage/graph/__pycache__/_ncut.cpython-311.pyc,,
skimage/graph/__pycache__/_rag.cpython-311.pyc,,
skimage/graph/__pycache__/mcp.cpython-311.pyc,,
skimage/graph/__pycache__/spath.cpython-311.pyc,,
skimage/graph/_graph.py,sha256=cyfQ1zvBVb2_5zW_c00yiYY4rOEzkwFBVWjor0a0uCA,9223
skimage/graph/_graph_cut.py,sha256=WItPbPIQVpUkQLRszjtAXJ2ZAT-YwHAC9vLKO9hxmso,10148
skimage/graph/_graph_merge.py,sha256=lrgunWZGrplz42xIDIz3IJoR4AfhzNLc_MdD5O-3tkQ,4304
skimage/graph/_mcp.cpython-311-x86_64-linux-gnu.so,sha256=47MY8HMtms3grGoPc9-kQSwpZSJcP_loBfj_HG1i51w,537696
skimage/graph/_ncut.py,sha256=EmchgBgVFKaveAKMElDIcOGFOVGlrQWqb_bt-2lcy8M,1825
skimage/graph/_ncut_cy.cpython-311-x86_64-linux-gnu.so,sha256=k_h_I0TDjVmsqWOIw_04mgH-COS4fQx1uC8RAV9F2SE,295752
skimage/graph/_rag.py,sha256=0b0iGwAtnVwzmKX69x1JRxviXgtWDdn18UDkvUq_NdY,20626
skimage/graph/_spath.cpython-311-x86_64-linux-gnu.so,sha256=BKXfhIZrRpZekXfnrPKnxWZLua0Fs9zwk-RLb82pmRE,260464
skimage/graph/heap.cpython-311-x86_64-linux-gnu.so,sha256=i5cR5iIF52M_O35mqa3ZRHonZTT0cyA--R7Zdt1mbAY,138392
skimage/graph/mcp.py,sha256=1gRa1f2QhCiup4WFaaCr9YcnNHZVc8H1DoIA0oS-vVM,3182
skimage/graph/spath.py,sha256=uav6ZRcaw3O93NyLiq-EVQouZUtxK16VIYUYoWGuJNM,3399
skimage/graph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/graph/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_anisotropy.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_connect.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_flexible.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_heap.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_mcp.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_pixel_graph.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_rag.cpython-311.pyc,,
skimage/graph/tests/__pycache__/test_spath.cpython-311.pyc,,
skimage/graph/tests/test_anisotropy.py,sha256=_PGkxQt2is3f1bYmAzWPZJ-KKmAh-_9OYXkQ89-ceTY,3518
skimage/graph/tests/test_connect.py,sha256=XU3UMqDRacTy-dMulRNwEFBixyvYmxbK-RtfhFc8HzY,2367
skimage/graph/tests/test_flexible.py,sha256=TgkxK1QpeHAZu6BoULWoIY0ir83_vG9VZFplLjhYz5s,1560
skimage/graph/tests/test_heap.py,sha256=PHdBGa90S3k0VuEAZhUOi7-zSoDSeduQdtQUCaD8GIY,1105
skimage/graph/tests/test_mcp.py,sha256=hB5fPpB73Rj2c9YPtukyA0Hxeg424yuIOMkmVfeY3BM,5402
skimage/graph/tests/test_pixel_graph.py,sha256=Ein8JyF1gPYEY7P-u2mgQgOknKFsae5f-FTctg37kLw,3745
skimage/graph/tests/test_rag.py,sha256=VEdfa47xlwForkv0q1vfFiW-4nfGB9wjR46wVz9sSdc,7857
skimage/graph/tests/test_spath.py,sha256=keQxRt7EyZyEMO41d1UcAdT5ZVHvnp5HJ83l9RQYXVg,826
skimage/io/__init__.py,sha256=wHwMf7uBa0rcB4BcF76XIWhbCeLYW-CyCz6_4UnDUeE,1009
skimage/io/__pycache__/__init__.cpython-311.pyc,,
skimage/io/__pycache__/_image_stack.cpython-311.pyc,,
skimage/io/__pycache__/_io.cpython-311.pyc,,
skimage/io/__pycache__/collection.cpython-311.pyc,,
skimage/io/__pycache__/manage_plugins.cpython-311.pyc,,
skimage/io/__pycache__/sift.cpython-311.pyc,,
skimage/io/__pycache__/util.cpython-311.pyc,,
skimage/io/_image_stack.py,sha256=A75v07PK6yd-5Qi60JfoaKG2esjJI5bMtlou8dNjUSc,570
skimage/io/_io.py,sha256=j1PqWx7_rpaw-FQ_TGmtosGg9mdCuOKd-qD5VZtZqBA,8986
skimage/io/_plugins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/_plugins/__pycache__/__init__.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/fits_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/gdal_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/imageio_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/imread_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/matplotlib_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/pil_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/simpleitk_plugin.cpython-311.pyc,,
skimage/io/_plugins/__pycache__/tifffile_plugin.cpython-311.pyc,,
skimage/io/_plugins/fits_plugin.ini,sha256=60y6Ey9etFQTf3oRxr4hAgptiBxkmNG_PpJaP5LdhR8,88
skimage/io/_plugins/fits_plugin.py,sha256=hE0QRLBX6AcTtodB9VVopw5O7U62ektN251opWSu6MQ,4407
skimage/io/_plugins/gdal_plugin.ini,sha256=Yx6iK4NqTllzpc726aXZXOlktuhm8IR28i4hfB7Qkn8,89
skimage/io/_plugins/gdal_plugin.py,sha256=oNQjajA4GUxb3swAQMf19X_XJhyY3pRdaFde145K4J4,349
skimage/io/_plugins/imageio_plugin.ini,sha256=Gi4D65yDOFzg7gRCwXYGxlJoHsvlFSEwyt1wJkedqWc,88
skimage/io/_plugins/imageio_plugin.py,sha256=d9MQzuDOWadJlfDhNptaNQJEI8sPsXWgzmwJuDxldLk,330
skimage/io/_plugins/imread_plugin.ini,sha256=yNvmnjjif85MJpwIfSheQ0IMQ86rPZuuTDjuaZZH6Pk,86
skimage/io/_plugins/imread_plugin.py,sha256=dM_Z_GsqUIbZQatLUPKJEiOC6nYQP7k3nscDyTLjJvs,956
skimage/io/_plugins/matplotlib_plugin.ini,sha256=re_KwCXXwedUEqJ749d7q6YqPDde5_RxH72M7y3PYY0,123
skimage/io/_plugins/matplotlib_plugin.py,sha256=QOIyQmgZGQ7NBZf4mZ5SWHC8P86y64bmAROy_4DYhKk,6467
skimage/io/_plugins/pil_plugin.ini,sha256=ZSFIMpRtr2-tUPvK_cG0xLOrPSWgw1ObRWVe8TMIJF0,91
skimage/io/_plugins/pil_plugin.py,sha256=28qO6oO8ZUbac8wFnMw3E1Lv6KMUiNqf5lwsj9XgDU0,7843
skimage/io/_plugins/simpleitk_plugin.ini,sha256=Zu1mh1RaPIu6vw2cSPev_T2or0DTMYZeLxyc1u7Hcig,92
skimage/io/_plugins/simpleitk_plugin.py,sha256=N2Ja-_0TpfrixNl0iEWJydrIrHgOYlzuxHkF10KrYOE,531
skimage/io/_plugins/tifffile_plugin.ini,sha256=vQpbXXPSQN242crgASKbsxqn1aDUfHwftslt9vvuSkQ,110
skimage/io/_plugins/tifffile_plugin.py,sha256=mAT73YEKXXefD2DtMqZyUioqljO4FmKRi7D5b-gHeps,2071
skimage/io/collection.py,sha256=GoXoj5LNTEIiFVA44ZIVmOug4M2eS_p2yF3q_RfoDNg,15956
skimage/io/manage_plugins.py,sha256=F2oA6_YfA-ztjgKSi5e0t7PViLaZkNRLkUqwvRAcGv8,12309
skimage/io/sift.py,sha256=mfUV7SJhTqmxpwHXwyPMYhQT_1d6y5JloNfHgH40qnc,2561
skimage/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/io/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_collection.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_fits.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_imageio.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_imread.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_io.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_mpl_imshow.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_multi_image.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_pil.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_plugin.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_sift.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_simpleitk.cpython-311.pyc,,
skimage/io/tests/__pycache__/test_tifffile.cpython-311.pyc,,
skimage/io/tests/test_collection.py,sha256=oyDQBMq7uD4Dt6PYkr8q_GGskWh8I_vy3vRlorJrWSc,5357
skimage/io/tests/test_fits.py,sha256=sn5VaWkM1dr94F-WqbfIUooAW0wet7wCa43w5PZ4s1Q,884
skimage/io/tests/test_imageio.py,sha256=e2IgcZL3PWKZK3A-Guj849PjVUhC4sPz9V_hKDWD27Y,2724
skimage/io/tests/test_imread.py,sha256=id3_fw7zwgWODWpHvzrCoidMfzyTtPB_8JcTLbljUfk,1946
skimage/io/tests/test_io.py,sha256=rygpx2BDJ1SygelrrnEvv-fU0cFHcRLLQnlM22an134,5361
skimage/io/tests/test_mpl_imshow.py,sha256=Wb0QXVNaN21KF6vpp6vDVhWMY0EzBKz4sichtVMSRbY,3741
skimage/io/tests/test_multi_image.py,sha256=3jx3EAfU4_nSw4grgq-TCBeQ_CaGa74iQJHk6d3RytY,2527
skimage/io/tests/test_pil.py,sha256=XbW6HHktiKmAwDfs3Zw9E4XsMx0RcYwpctHM6Vy5o74,9217
skimage/io/tests/test_plugin.py,sha256=XEmvZxdIrlHALTcoOC8Uoj0srQwQuGK0mAWVCI4NNaA,3014
skimage/io/tests/test_sift.py,sha256=RE4W1QRzqBKaLSdhElhDO806EvcDcFwidR8RxYH2mic,3336
skimage/io/tests/test_simpleitk.py,sha256=THFUDW98M--DNcyMUenGobvw2IAxk4NikJa2Dbm_Iq4,2557
skimage/io/tests/test_tifffile.py,sha256=IF8wH16H9iqSFL55hforf9FNWtkbnqYGn72hWmqJbCY,2665
skimage/io/util.py,sha256=7gQdn6BD2HX-R5yAI6A8GCjG3HMWzUFjzjPJD1Qa3HE,1283
skimage/measure/__init__.py,sha256=QV04vcq3E_ez4cLSw_3xjbS1ptDAzmVTeJrZ3IpdA4I,174
skimage/measure/__init__.pyi,sha256=MFpgokeHZgVTQ2NRJ5JvIZLfFY6PoWt4zuWe4sO646M,1858
skimage/measure/__pycache__/__init__.cpython-311.pyc,,
skimage/measure/__pycache__/_blur_effect.cpython-311.pyc,,
skimage/measure/__pycache__/_colocalization.cpython-311.pyc,,
skimage/measure/__pycache__/_find_contours.cpython-311.pyc,,
skimage/measure/__pycache__/_label.cpython-311.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner.cpython-311.pyc,,
skimage/measure/__pycache__/_marching_cubes_lewiner_luts.cpython-311.pyc,,
skimage/measure/__pycache__/_moments.cpython-311.pyc,,
skimage/measure/__pycache__/_moments_analytical.cpython-311.pyc,,
skimage/measure/__pycache__/_polygon.cpython-311.pyc,,
skimage/measure/__pycache__/_regionprops.cpython-311.pyc,,
skimage/measure/__pycache__/_regionprops_utils.cpython-311.pyc,,
skimage/measure/__pycache__/block.cpython-311.pyc,,
skimage/measure/__pycache__/entropy.cpython-311.pyc,,
skimage/measure/__pycache__/fit.cpython-311.pyc,,
skimage/measure/__pycache__/pnpoly.cpython-311.pyc,,
skimage/measure/__pycache__/profile.cpython-311.pyc,,
skimage/measure/_blur_effect.py,sha256=mhgvLVugOfQo3iI_ZkU2_zcRVVmHEh28MxEhfVjc8oA,3324
skimage/measure/_ccomp.cpython-311-x86_64-linux-gnu.so,sha256=g4ETUvyFmwWPthK6f3xFqzITNWfishckIv4a7qCISN4,137264
skimage/measure/_colocalization.py,sha256=PUSk-oqQ6nVfXn0uK9feLfiBvdwEqufqQH0yohX0yCo,12233
skimage/measure/_find_contours.py,sha256=9XR2aSiN9vl0WpaA0s19HzP0cbiKxd1zvg7Qaht-I6M,9585
skimage/measure/_find_contours_cy.cpython-311-x86_64-linux-gnu.so,sha256=LWOoX4fQ3DLnw_RwcKE0bSop0ZLCuCErI-Z7c_69U4Q,244584
skimage/measure/_label.py,sha256=WoRzje_lZLblB0BSpGsb4rHifWg9ZO9u19Fudq_HmPU,3960
skimage/measure/_marching_cubes_lewiner.py,sha256=qX3b_vAdibqb34AkLqL1zK8sW9mwf5fJ3E3aRENNMMk,12856
skimage/measure/_marching_cubes_lewiner_cy.cpython-311-x86_64-linux-gnu.so,sha256=0pifS6b3KECUfyPzG0xuZnNqFdA1Jt1j8LEdIqDe8wM,381048
skimage/measure/_marching_cubes_lewiner_luts.py,sha256=puYUThcl9gATqqmerwTqAdpqooOeMoR0oV6J_gvP5wE,27802
skimage/measure/_moments.py,sha256=85KWsIKWuYDMifNeEADXsHvZt8khf13O2GpkbLwHcl8,17848
skimage/measure/_moments_analytical.py,sha256=1OzL8mhKfqxfKT6Q4t4WwpFyM_1cgKlquMM7jr5LJ-Q,6563
skimage/measure/_moments_cy.cpython-311-x86_64-linux-gnu.so,sha256=YffnXnxo7i-ZA7_cdToMv8xChUmv7hSFYiOL0P3zALk,277328
skimage/measure/_pnpoly.cpython-311-x86_64-linux-gnu.so,sha256=_A7Ts6g68pph9eJRifKaDqN0wR9EDFDWaLq_plfFhdg,259832
skimage/measure/_polygon.py,sha256=JqJ9iVRdFY9rVChXKXmVY5slHtOvBCjIyGpWyEkoqr4,5268
skimage/measure/_regionprops.py,sha256=F7peBsKw3pjyjvLuuUDKOfK08j2Ci-wI_k7agh39sxk,51730
skimage/measure/_regionprops_utils.py,sha256=CC4bxkhCTIZH7c7QBWLEu4HafFZBo_MsBn17L2eMOyQ,15989
skimage/measure/block.py,sha256=b4dhXeeaiBerPmFpNjtm52KvwRmIByPI1U-Y0S5gws4,3261
skimage/measure/entropy.py,sha256=aNypYauao_uvywjL1DewtbCDB01jrwX6BnUIAo_CbhM,1144
skimage/measure/fit.py,sha256=a5FqsqSuGNKPxdFTjgVyAhpIykjHMmXHT2dFIPMrSHk,32458
skimage/measure/pnpoly.py,sha256=DGgYjnvOANCvZOb_oOgzHTiDi62vWD03AOFQhuctJXo,2023
skimage/measure/profile.py,sha256=-r9sI9iSsu57zaFKtGc0rE5-x1W5ZgP_XleR_bAS0d0,6781
skimage/measure/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/measure/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_block.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_blur_effect.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_ccomp.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_colocalization.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_entropy.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_find_contours.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_fit.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_label.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_marching_cubes.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_moments.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_pnpoly.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_polygon.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_profile.cpython-311.pyc,,
skimage/measure/tests/__pycache__/test_regionprops.cpython-311.pyc,,
skimage/measure/tests/test_block.py,sha256=lOMhCz-a-zEPLJvK6fGiGVrBFGvFGKucb3fuf9W2yN0,4022
skimage/measure/tests/test_blur_effect.py,sha256=-nmf0zUEbtTeToaZaVwxggT4W8VEZTHoboQSkQ5j6fE,2502
skimage/measure/tests/test_ccomp.py,sha256=pHQ8jGMj7WlShxyaunDb3kwd2DopZxKq-MmL_2mmCoA,8033
skimage/measure/tests/test_colocalization.py,sha256=Hr1D1pu1jU7PuBb_Lp-Wwp37athNjIUHzycnPBJe74E,4672
skimage/measure/tests/test_entropy.py,sha256=mMUvbULp-qBSbEJ73k25xqG-D8dLymExO4I5WGrJsJI,400
skimage/measure/tests/test_find_contours.py,sha256=gexiCwV1eWsQy4boYOe3pUHGlJEbD0rRalHtrMBUZSc,5276
skimage/measure/tests/test_fit.py,sha256=WoySIrX_A6XU0opKgLe3d5W6mmsrQD8eNC9ww7yukEo,22093
skimage/measure/tests/test_label.py,sha256=6tTyVeNbgjhxxnwnyN0VhXKLuFTk21QWCvg1C-5Aa34,1783
skimage/measure/tests/test_marching_cubes.py,sha256=A4M4I8bnXm5bxVi6ChEvckje0AiN8m0CTWcGB15_fpg,7055
skimage/measure/tests/test_moments.py,sha256=kDxKemYhwnYajGEFa4EP2VALEOdUri9Z8lcvOR1RWQU,11768
skimage/measure/tests/test_pnpoly.py,sha256=fLKSNA2r7ABNqS6vpY_3D8AVJb0wAEnkWhAhLy_XMrI,1239
skimage/measure/tests/test_polygon.py,sha256=jor2cmpYG4XXfEu-jlURTmPUEho5YRF8i_NDJNid57s,2295
skimage/measure/tests/test_profile.py,sha256=OHBxbElqoVXz4HoeSrKXapDgLpoIoHriSt0KHVI-NIY,7822
skimage/measure/tests/test_regionprops.py,sha256=lC6_gd5iaWUiywXuAi4ZKa3SA5rtE7aUIXCKT5JFJqk,53066
skimage/metrics/__init__.py,sha256=TWewBmmkeJ32pddMk6TfIIGK8-kWALIor7GMHx5gMJg,180
skimage/metrics/__init__.pyi,sha256=1t3MLs46xdqirO2XUiVV__auPvFJY0rAM-S1PDVoHKQ,886
skimage/metrics/__pycache__/__init__.cpython-311.pyc,,
skimage/metrics/__pycache__/_adapted_rand_error.cpython-311.pyc,,
skimage/metrics/__pycache__/_contingency_table.cpython-311.pyc,,
skimage/metrics/__pycache__/_structural_similarity.cpython-311.pyc,,
skimage/metrics/__pycache__/_variation_of_information.cpython-311.pyc,,
skimage/metrics/__pycache__/set_metrics.cpython-311.pyc,,
skimage/metrics/__pycache__/simple_metrics.cpython-311.pyc,,
skimage/metrics/_adapted_rand_error.py,sha256=zbCyRSiOGz3QCzYVLdnCDpHtvh2ZoHyCEKKQe-uHJkA,3571
skimage/metrics/_contingency_table.py,sha256=4zT07BasPCvZ-wxTPoykVAKckUyGSdUyK28Mxa29v2I,1734
skimage/metrics/_structural_similarity.py,sha256=l4HTTumNP0vDABKt06R0Dq-7ZpUtupxqWz64e9C0sKA,10422
skimage/metrics/_variation_of_information.py,sha256=Kf02JWuAOTT6N5Y9vksiUHFSuSezrHv7KwWpc8ZVLag,4254
skimage/metrics/set_metrics.py,sha256=VNcOP2w9oNrrOS0qjPkVuI8IKTG1yYliMWGlzqbVtEk,4896
skimage/metrics/simple_metrics.py,sha256=RTpf2sSCOpLG6q7AtYMIdPHkYqchhqdEGXh4dB8Yt7Q,8247
skimage/metrics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/metrics/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/metrics/tests/__pycache__/test_segmentation_metrics.cpython-311.pyc,,
skimage/metrics/tests/__pycache__/test_set_metrics.cpython-311.pyc,,
skimage/metrics/tests/__pycache__/test_simple_metrics.cpython-311.pyc,,
skimage/metrics/tests/__pycache__/test_structural_similarity.cpython-311.pyc,,
skimage/metrics/tests/test_segmentation_metrics.py,sha256=mIjMWvOLIrJ38U7DFrEK2m0OAFF7uDZ5LWIKxvyZpUE,2593
skimage/metrics/tests/test_set_metrics.py,sha256=o42FKIdel_BcnmBUm1cWZ1nRj1u8Ik3EmJ1p8TEcg5g,6930
skimage/metrics/tests/test_simple_metrics.py,sha256=i3tXEQ4G9aa_V3quNvXWNKBzRauIgSiPVqpm6WW4RyY,4830
skimage/metrics/tests/test_structural_similarity.py,sha256=8dVJmhsqhKzc4TSy1hEOCj6Dz7ot8tqbkpS6BBigR8Q,9746
skimage/morphology/__init__.py,sha256=zJ3EJavfwOhIrQhF5DqXvMC8qM3JtpAU2baJneuc1vM,2117
skimage/morphology/__pycache__/__init__.cpython-311.pyc,,
skimage/morphology/__pycache__/_flood_fill.cpython-311.pyc,,
skimage/morphology/__pycache__/_skeletonize.cpython-311.pyc,,
skimage/morphology/__pycache__/_util.cpython-311.pyc,,
skimage/morphology/__pycache__/binary.cpython-311.pyc,,
skimage/morphology/__pycache__/convex_hull.cpython-311.pyc,,
skimage/morphology/__pycache__/extrema.cpython-311.pyc,,
skimage/morphology/__pycache__/footprints.cpython-311.pyc,,
skimage/morphology/__pycache__/gray.cpython-311.pyc,,
skimage/morphology/__pycache__/grayreconstruct.cpython-311.pyc,,
skimage/morphology/__pycache__/isotropic.cpython-311.pyc,,
skimage/morphology/__pycache__/max_tree.cpython-311.pyc,,
skimage/morphology/__pycache__/misc.cpython-311.pyc,,
skimage/morphology/_convex_hull.cpython-311-x86_64-linux-gnu.so,sha256=uEf0Utwrl-IBdysk-ZWUTdRyJ6UHaRst8UFwEvvymPM,236456
skimage/morphology/_extrema_cy.cpython-311-x86_64-linux-gnu.so,sha256=OUtv28ZO1LnJZGKUO4w8N0W76FLhH7fJZ8zFuYAlIjg,338824
skimage/morphology/_flood_fill.py,sha256=349jaVevFjmsQHM6ZRRR3Z-Oi0-gelWj6WP5MILXqwk,10733
skimage/morphology/_flood_fill_cy.cpython-311-x86_64-linux-gnu.so,sha256=bqgS5x6h2fFjm9UnIvfjC0G4pSolotnWUfZN1gc60No,414448
skimage/morphology/_grayreconstruct.cpython-311-x86_64-linux-gnu.so,sha256=Eu9bPZhfFd0fOqBi8Hq-fBz3bU6Hr4iifdUT5Y-B9Vw,290512
skimage/morphology/_max_tree.cpython-311-x86_64-linux-gnu.so,sha256=EStYYhhL-Dwr77CTMqgcesFyovtK4ytzU8mTiUThO7s,924296
skimage/morphology/_misc_cy.cpython-311-x86_64-linux-gnu.so,sha256=NzqBEoYb7SRoIUkSCi-xPshcW8jEcTnLn_bBKLMI1aw,337536
skimage/morphology/_skeletonize.py,sha256=nDm0COssRlhL8tlV3Z7mZWz1koRycB4-8G9kOT6fYqc,23432
skimage/morphology/_skeletonize_lee_cy.cpython-311-x86_64-linux-gnu.so,sha256=fv4d3D-ZPQINaadk008kUFHIrP1BIzW7UkRfiMuPvo8,254616
skimage/morphology/_skeletonize_various_cy.cpython-311-x86_64-linux-gnu.so,sha256=VgM3glxjwYs_Kk7lKFAtprjLfrg8VgEtqdcTyd4v9_I,259680
skimage/morphology/_util.py,sha256=TBba9j3tF3srL-q54cNYTRaYy3_8rPuD73v1r1i63vc,12019
skimage/morphology/ball_decompositions.npy,sha256=T561HzYf19fSLTQt7Hu5gXiiGqnJRFB_JYmLjKIT5U0,431
skimage/morphology/binary.py,sha256=4Py-51eyBa64w1c_WInfWRNyZiknHQKmtC-y--EAo8I,12112
skimage/morphology/convex_hull.py,sha256=5jLe9JbKXW9OBIuklxqYEm_ns-2usVuEx0wTZVmRnJA,8419
skimage/morphology/disk_decompositions.npy,sha256=dpc557PYxwYZk2gs30WgwEiUCVL7eOMbFy2DD3545-4,881
skimage/morphology/extrema.py,sha256=9LS6UvpGSmGW2gzxPpVtEU00beb_mh8C4Y1_ua47mlE,20816
skimage/morphology/footprints.py,sha256=Q_XBpszl9pJ2nKMTp0sMM7d3ogf4PUuKMDusMPXCrsE,42736
skimage/morphology/gray.py,sha256=aCBVlVr8mbFOhkUc2v05JadxY3UTFo65FLeQoKc8sgI,25928
skimage/morphology/grayreconstruct.py,sha256=-HwSfTlqe29Yp-IQmxhNQdfbJtgCHMoixj3fynFXEU0,9350
skimage/morphology/isotropic.py,sha256=XRVAX2ha5tw_XLQWd5HiQUGtcZ8MvChzaQPYDzXcinQ,7879
skimage/morphology/max_tree.py,sha256=7ZhC3CU-L4_GTq4yd_mwLPWC1xw7-uT_lGi5wS86hnQ,26983
skimage/morphology/misc.py,sha256=-iqoEXLgnhfGm0uXFCnT1B2zX28UtdBLtycbmUZ3sOE,16681
skimage/morphology/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/morphology/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_binary.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_convex_hull.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_extrema.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_flood_fill.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_footprints.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_gray.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_isotropic.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_max_tree.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_misc.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_reconstruction.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_skeletonize.cpython-311.pyc,,
skimage/morphology/tests/__pycache__/test_util.cpython-311.pyc,,
skimage/morphology/tests/test_binary.py,sha256=QqlWSIas2c3fORXHSkp6B_puCXYp-5hSu_wfE2ycAsE,12011
skimage/morphology/tests/test_convex_hull.py,sha256=VJTgBb1BgQucfdm79vVwxasw_1K6ez4iDNgOADB80W8,8860
skimage/morphology/tests/test_extrema.py,sha256=xMZ1GPgOZELD3yGX_Bx9CYOrjjGux5A8pKlNF1VgZqg,26904
skimage/morphology/tests/test_flood_fill.py,sha256=YQrhCQWlDE9nnu_G280p7t3bsUvOLD2UEmKx9uuoLhQ,9418
skimage/morphology/tests/test_footprints.py,sha256=egkde7pWzwp_NK_0lxAjQNeNC_akQ5NxuuEdoEcJT94,11775
skimage/morphology/tests/test_gray.py,sha256=u9Xq9AlQbRWQ16wn4FoDuYyVd7JKP-kMc1HvgneM9CM,16340
skimage/morphology/tests/test_isotropic.py,sha256=U-04r2oLsEJ-mh1nlSpIlYAxD0Cg4BqWZ9xS-KSZ9N8,2845
skimage/morphology/tests/test_max_tree.py,sha256=72PZorjk-37dJ_vm7jxD92pH1EnU-82IVCJOlIyjsME,20611
skimage/morphology/tests/test_misc.py,sha256=mcqCySpSnLakmn_gKFYa7jdv57CV6mjnkdjY2i5AF_s,18008
skimage/morphology/tests/test_reconstruction.py,sha256=FVzLHWnKvsxWE73lLEmnwQIIUNSkfuQ-NXXoaeLtN3c,5868
skimage/morphology/tests/test_skeletonize.py,sha256=fOa1G4ARj22l_06DvLUji3SPcuzpU8KMBZe15xt19mE,13680
skimage/morphology/tests/test_util.py,sha256=Vi6MJVYtIaWtFdDrxeK1e-hXv7Ff8WKeAfmzeK5HHTs,5660
skimage/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/__init__.py,sha256=WGYVol8x2WxB28mx35mdliJgJ_c0JHE7baSQ4Kxqtqo,184
skimage/registration/__init__.pyi,sha256=FDJiD5iCOBzWws3vx3ON2PhpoujSvUhtcCjdArGrM74,366
skimage/registration/__pycache__/__init__.cpython-311.pyc,,
skimage/registration/__pycache__/_masked_phase_cross_correlation.cpython-311.pyc,,
skimage/registration/__pycache__/_optical_flow.cpython-311.pyc,,
skimage/registration/__pycache__/_optical_flow_utils.cpython-311.pyc,,
skimage/registration/__pycache__/_phase_cross_correlation.cpython-311.pyc,,
skimage/registration/_masked_phase_cross_correlation.py,sha256=BigH4m9EEQL0p-ZrZ9YTirNuJ9u0jbYi5YmOZKGH_30,12412
skimage/registration/_optical_flow.py,sha256=dVDHwS5GHlT_ir1_OjGieNz56RCpvC6v1pJxzLgG9U4,14547
skimage/registration/_optical_flow_utils.py,sha256=aWgyZhaeXedlmzgr614oShB_ITs__sgAdnMHpVlL4P8,3680
skimage/registration/_phase_cross_correlation.py,sha256=Lg0gsrIujU2fIytSnHm64P55XlYWpVuXgklOVPKWQME,17867
skimage/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/registration/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/registration/tests/__pycache__/test_ilk.cpython-311.pyc,,
skimage/registration/tests/__pycache__/test_masked_phase_cross_correlation.cpython-311.pyc,,
skimage/registration/tests/__pycache__/test_phase_cross_correlation.cpython-311.pyc,,
skimage/registration/tests/__pycache__/test_tvl1.cpython-311.pyc,,
skimage/registration/tests/test_ilk.py,sha256=xeAUy-zHmcyXulomUGujIoB8NZ8NdM6zzfGliYcIM_M,3096
skimage/registration/tests/test_masked_phase_cross_correlation.py,sha256=-QQxqNJhL3P9OqlO6Msk_vsCezXIlirMlA_TWw3ltLc,9824
skimage/registration/tests/test_phase_cross_correlation.py,sha256=CB6_eqOa_j_GuZBZPAlHACD3kTyGBprlVZm6WpLIHJ4,8377
skimage/registration/tests/test_tvl1.py,sha256=gIIgiMW7mGkoiWr9LjEz-c3n5F7h2IW64aiqMjUKdhI,3564
skimage/restoration/__init__.py,sha256=DgJTx8pODATVzrTsgIy0RpwL4ojH-dl6ym7w312JF-4,178
skimage/restoration/__init__.pyi,sha256=mw9Cug3REvQ1R0NveVQXbSkT-JRo6uqSp0gx4eyEG8Y,1067
skimage/restoration/__pycache__/__init__.cpython-311.pyc,,
skimage/restoration/__pycache__/_cycle_spin.cpython-311.pyc,,
skimage/restoration/__pycache__/_denoise.cpython-311.pyc,,
skimage/restoration/__pycache__/_rolling_ball.cpython-311.pyc,,
skimage/restoration/__pycache__/deconvolution.cpython-311.pyc,,
skimage/restoration/__pycache__/inpaint.cpython-311.pyc,,
skimage/restoration/__pycache__/j_invariant.cpython-311.pyc,,
skimage/restoration/__pycache__/non_local_means.cpython-311.pyc,,
skimage/restoration/__pycache__/uft.cpython-311.pyc,,
skimage/restoration/__pycache__/unwrap.cpython-311.pyc,,
skimage/restoration/_cycle_spin.py,sha256=9VZkZGE9IOEnMEs3WcMeijeKvzqa61NYAsp40mr8Htk,5885
skimage/restoration/_denoise.py,sha256=_vOvvb4SZyll-WcGUDNyLMSLkbPLQIiWBns7W8i6J2s,41403
skimage/restoration/_denoise_cy.cpython-311-x86_64-linux-gnu.so,sha256=V4YZz1rhT0duoLTO3uM4YICJSvlLbsMv2UTE9a2NeH8,367568
skimage/restoration/_inpaint.cpython-311-x86_64-linux-gnu.so,sha256=aVGqUAYIFJR4UnHaMRt4A7bDVXvujrL3IdVeEuvse4g,281184
skimage/restoration/_nl_means_denoising.cpython-311-x86_64-linux-gnu.so,sha256=Zav9Q6PL0yHr4iVBm9WQg1XB7_XPNFBSVB35fvmC2dY,619480
skimage/restoration/_rolling_ball.py,sha256=rnjEy1G1IXN1uh70bpYLDwou2pIqoG7WHtKmrT_z0Co,6851
skimage/restoration/_rolling_ball_cy.cpython-311-x86_64-linux-gnu.so,sha256=1Z4fpNeBxqAzJ2-IDJK1oOD1MLJpSFmqB8kY1ipAbFM,328104
skimage/restoration/_unwrap_1d.cpython-311-x86_64-linux-gnu.so,sha256=LAmTcvuzVCHEC4FS6zVtzG-Bi3tgx5CLLw9c5P956YU,227152
skimage/restoration/_unwrap_2d.cpython-311-x86_64-linux-gnu.so,sha256=Kb7ASMZvRU2Ae2-6u8cfjqLEIjfSjRP7o1yHoAy3x-s,244608
skimage/restoration/_unwrap_3d.cpython-311-x86_64-linux-gnu.so,sha256=l3ZAZA9HORCObj6mSG8rpiF4fzCLmaN_Ny6z4kTcimQ,261152
skimage/restoration/deconvolution.py,sha256=F3RX4x-78NYUt0b_6o4sqFXejR-U1vLziefWLhKESNY,16032
skimage/restoration/inpaint.py,sha256=87oZAt6ixrbH7KfdUTOokfyUyhcnLYrr8CIpLR9jW_I,12657
skimage/restoration/j_invariant.py,sha256=7e_m9oFc2gFlY_9eM5vRJ_6KLMzrqFmkdodC-0JOmC4,12402
skimage/restoration/non_local_means.py,sha256=ftcQMALybovCQn-aJCMCsdmUtZh0BYx5dT9wAVIZFLI,7718
skimage/restoration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/restoration/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_denoise.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_inpaint.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_j_invariant.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_restoration.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_rolling_ball.cpython-311.pyc,,
skimage/restoration/tests/__pycache__/test_unwrap.cpython-311.pyc,,
skimage/restoration/tests/test_denoise.py,sha256=l97RnNfxN9xfe0ymygYpcnJ1zpFPeoxeVPweKf1rmT4,40375
skimage/restoration/tests/test_inpaint.py,sha256=TdZRykL8xCuu5d1kRT7gtXCT8N-zeswu4mn35jNY7GU,6853
skimage/restoration/tests/test_j_invariant.py,sha256=rRlL8t4SIHsczzyk1VB9GFlL_LFfo8HyuJnKckVqyTM,3273
skimage/restoration/tests/test_restoration.py,sha256=CMd-epKF5WkD4fVg9vevnrhU__OHZBeqkqd-d8F_rpw,6451
skimage/restoration/tests/test_rolling_ball.py,sha256=hIGTGLrlk4LHidMUxjkIxRBsBw7lLscA8jGAR447MfM,3071
skimage/restoration/tests/test_unwrap.py,sha256=6J0xxzISR5CvTZm6di22AlaylLbDf0z63bVZCIg7zXY,8347
skimage/restoration/uft.py,sha256=gTCw6pnKeRO0R7DqMTrOkV3H1r00qHQKoht_k0WnExs,12613
skimage/restoration/unwrap.py,sha256=BxeM0cWdKpQSMCEUR2uzlI5kbTryFtk8tkLBV0zXDn0,4830
skimage/segmentation/__init__.py,sha256=nUFS40ztKnGtESHJLFMPRN5xA1igZ00SknBK-_lxKp8,1253
skimage/segmentation/__pycache__/__init__.cpython-311.pyc,,
skimage/segmentation/__pycache__/_chan_vese.cpython-311.pyc,,
skimage/segmentation/__pycache__/_clear_border.cpython-311.pyc,,
skimage/segmentation/__pycache__/_expand_labels.cpython-311.pyc,,
skimage/segmentation/__pycache__/_felzenszwalb.cpython-311.pyc,,
skimage/segmentation/__pycache__/_join.cpython-311.pyc,,
skimage/segmentation/__pycache__/_quickshift.cpython-311.pyc,,
skimage/segmentation/__pycache__/_watershed.cpython-311.pyc,,
skimage/segmentation/__pycache__/active_contour_model.cpython-311.pyc,,
skimage/segmentation/__pycache__/boundaries.cpython-311.pyc,,
skimage/segmentation/__pycache__/morphsnakes.cpython-311.pyc,,
skimage/segmentation/__pycache__/random_walker_segmentation.cpython-311.pyc,,
skimage/segmentation/__pycache__/slic_superpixels.cpython-311.pyc,,
skimage/segmentation/_chan_vese.py,sha256=HHgn7gjE8dOqdAt6QM8zyyYZt1SE_EQDtp73c9Gd09I,13774
skimage/segmentation/_clear_border.py,sha256=d9JzobF-EdVq6rQh3IhcSNPDW9FJCWDyNUHSOu9fIgE,3989
skimage/segmentation/_expand_labels.py,sha256=Q8NYU2LYQO5tQ2rXKhg0xpKu-RMwI6WwA_LxrskYlUg,4201
skimage/segmentation/_felzenszwalb.py,sha256=wl6nKzjrWW_fJw-sFeLDq5fhGVtJlZEBD3W4yu8DbFs,2486
skimage/segmentation/_felzenszwalb_cy.cpython-311-x86_64-linux-gnu.so,sha256=uPIHivMJrUHrVBXws_AVLMuOV1fZkZ5K-l6jIhXxSc8,182360
skimage/segmentation/_join.py,sha256=jR2hcXvzWBj1cVb4ipTNhKlSf0iv8W1g4lC32ARc2L4,7134
skimage/segmentation/_quickshift.py,sha256=jPSH6-X45DTnxEA_17EMo0W2fzEoVpc3G2hY_nzlzzs,3469
skimage/segmentation/_quickshift_cy.cpython-311-x86_64-linux-gnu.so,sha256=TNm83zF9jkwFLWjYhSUJpOTkSp8hCGty8v8011mxJyo,322176
skimage/segmentation/_slic.cpython-311-x86_64-linux-gnu.so,sha256=dc9f-yBay9oIPkGpPrUC69DdSUHsRXWZQmC0E3Xerwk,340688
skimage/segmentation/_watershed.py,sha256=LEqyylS6eJ9sAVgdQAwqM7gKDhF-DFyVJUWmdQPuNUw,9813
skimage/segmentation/_watershed_cy.cpython-311-x86_64-linux-gnu.so,sha256=uD3ew28lQMXqncqbem60z3nlBMWk3AgdChvhDWC-1b8,342328
skimage/segmentation/active_contour_model.py,sha256=MV8bhOv5vTaLwYAvW1uzafYtZTbEHPkC24kKHEjQXNY,7839
skimage/segmentation/boundaries.py,sha256=-9u_NtiBPAnQBYPSNQY6ywkR2ELNlGKNSC1cVoWBgTE,10016
skimage/segmentation/morphsnakes.py,sha256=cc7Qm7qzkYUjw4q-2hI1p5yjTrdGfXaHXg71sT0gktY,14882
skimage/segmentation/random_walker_segmentation.py,sha256=eT1p12CM98AVr4HbvWyF9dvNUBI5nN74BdkpLZvi04g,21712
skimage/segmentation/slic_superpixels.py,sha256=FLukNVkXZIEaXepIzzFJjLoG0jv2xr1lZqHtaOfGXaU,16353
skimage/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/segmentation/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_active_contour_model.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_boundaries.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_chan_vese.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_clear_border.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_expand_labels.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_felzenszwalb.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_join.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_morphsnakes.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_quickshift.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_random_walker.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_slic.cpython-311.pyc,,
skimage/segmentation/tests/__pycache__/test_watershed.cpython-311.pyc,,
skimage/segmentation/tests/test_active_contour_model.py,sha256=HEtlByzNaoFJ4zm-Te6bW6vCsTr4W0cseTM0LM9QgnM,5878
skimage/segmentation/tests/test_boundaries.py,sha256=d7MUg8exUxgWuKLqyV9I_6F0nJ11sPZEJIX1cd4jFOE,5212
skimage/segmentation/tests/test_chan_vese.py,sha256=7uf4IzKfGMzmENIGtHEhXqn2e5-x5MbHmCoi-IlIyxI,3369
skimage/segmentation/tests/test_clear_border.py,sha256=aLtS_uRV8Rih3OdZT1TlmZpcO8vPvrqEJkegTaYTo4c,5501
skimage/segmentation/tests/test_expand_labels.py,sha256=OYRVdLrvrilXfykHrOua8fTmFzSL8IgtJdMpsKSUCHg,6620
skimage/segmentation/tests/test_felzenszwalb.py,sha256=Ffy_yzqAYypIim_GnqHWGFgIdkQAn042GhWrw87iJ2U,2853
skimage/segmentation/tests/test_join.py,sha256=5InTQU1SCd489FJLwL-3jEaLfgpzRVNYR0YaU2pJOxo,7198
skimage/segmentation/tests/test_morphsnakes.py,sha256=U2PfLgu1yK0OYQmOH2c9cwZGVMUZPnxbUTp1HmrhLss,4605
skimage/segmentation/tests/test_quickshift.py,sha256=Id75BasgHc5trx6Q6PZDc2T5iELEeqD8mLkwgacLKjY,2388
skimage/segmentation/tests/test_random_walker.py,sha256=eU6OLkRKpfonhzhofMxYHwa061R3xwFfLUCHtKdyceA,21603
skimage/segmentation/tests/test_slic.py,sha256=4qRp3R4bLhkLcpuLH0Yx4JWKDchCw9B84FUAXmP2wSY,18490
skimage/segmentation/tests/test_watershed.py,sha256=h4FAhNaHtSjAX6QULJ5qqtW9rrPcNL8tZWYCtkFXjTk,33360
skimage/transform/__init__.py,sha256=SjgjAu0K4Tya74fEiJSd5yITbVoH7pe4DGmDcgk4Vrk,1493
skimage/transform/__init__.pyi,sha256=uDxd4AETK9S_MLmnSXoTTAVNUXCy2idNbiWtW4IavPE,1981
skimage/transform/__pycache__/__init__.cpython-311.pyc,,
skimage/transform/__pycache__/_geometric.cpython-311.pyc,,
skimage/transform/__pycache__/_thin_plate_splines.cpython-311.pyc,,
skimage/transform/__pycache__/_warps.cpython-311.pyc,,
skimage/transform/__pycache__/finite_radon_transform.cpython-311.pyc,,
skimage/transform/__pycache__/hough_transform.cpython-311.pyc,,
skimage/transform/__pycache__/integral.cpython-311.pyc,,
skimage/transform/__pycache__/pyramids.cpython-311.pyc,,
skimage/transform/__pycache__/radon_transform.cpython-311.pyc,,
skimage/transform/_geometric.py,sha256=JvJfZewlvFj1xtyv4tNOZ78Ja_aaqS8PJs-XrT6AuC8,58708
skimage/transform/_hough_transform.cpython-311-x86_64-linux-gnu.so,sha256=2xBs5eAukHzNWR_ZUyJSvbDc91hLqJl5GnUNbOpqywg,350216
skimage/transform/_radon_transform.cpython-311-x86_64-linux-gnu.so,sha256=KlkTNgKmGaXn-4YLcfRpiIRssblOmcp5Z0TA_PyGibA,294416
skimage/transform/_thin_plate_splines.py,sha256=WZxyhkzasSY0v9tCPM8pa6eP36M1l-zbrztIT7rXTLQ,5801
skimage/transform/_warps.py,sha256=0SOXcrkW-C8zzJC8_GSpJfDrGDWSRDODTFz234WaDh4,48511
skimage/transform/_warps_cy.cpython-311-x86_64-linux-gnu.so,sha256=jafpHtH51hew-GBlKOMbtq8ZGEDFfWOgeaQXesEnuDE,309136
skimage/transform/finite_radon_transform.py,sha256=Jln0B-406LJcnpSknQui7xD-igI0-SzO57hGmCkBccw,3179
skimage/transform/hough_transform.py,sha256=z5bPOHpGFjsatTomi9_LHddR_wYjuPZVQ_UKd37S-ZY,15867
skimage/transform/integral.py,sha256=7Rh9I7C0fbuEV7Z9RR-aoZ45I3vzIsgakvq1oYApJ9Y,5096
skimage/transform/pyramids.py,sha256=s6eEuuaVfT7gm2U7oPY-IfKradesoVwTKcjFoDkEemU,13357
skimage/transform/radon_transform.py,sha256=y03ACiOrIETso2bUT9Gp4cbkMzDED0F7reL99I45rK8,20738
skimage/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/transform/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_finite_radon_transform.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_geometric.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_hough_transform.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_integral.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_pyramids.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_radon_transform.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_thin_plate_splines.cpython-311.pyc,,
skimage/transform/tests/__pycache__/test_warps.cpython-311.pyc,,
skimage/transform/tests/test_finite_radon_transform.py,sha256=Ms2EwaWZ6irxVaUU-zovgL_3tF-YiFA-r8v3UKV78Ak,316
skimage/transform/tests/test_geometric.py,sha256=juR9L4y9I5Xo7cF2lB_-NrocTolT07pbipNIcuHDj-4,34477
skimage/transform/tests/test_hough_transform.py,sha256=bEuKtpe3jEofqDVdbOJ8k6m8rrxIzZtfCi32thyn4Jk,19060
skimage/transform/tests/test_integral.py,sha256=4yL_gCYjzk5OtI7unTmBNbEu4k1KLRHiOrXx2rS42Os,2337
skimage/transform/tests/test_pyramids.py,sha256=YLylJjJkJyvE9QeAyAiPL-6w6LdN1KefFdS81waamlI,8047
skimage/transform/tests/test_radon_transform.py,sha256=gmo0Goii6SgriMUtf0Rw4PeV1ZHw0CV42Fb8UPi945c,18625
skimage/transform/tests/test_thin_plate_splines.py,sha256=UfkAFYDuLL7kBUIQ5n_S992fxqMVMtUFthzb0LEy6Cg,2742
skimage/transform/tests/test_warps.py,sha256=hoddh3wEAG0GLCDNHjFyXPwfLNxDp6pRR_aU3J5RUK0,32993
skimage/util/__init__.py,sha256=R-wuBwNEj640SbbdyXBP7UAQiWavvHAIQR5mw-UQTFM,1327
skimage/util/__pycache__/__init__.cpython-311.pyc,,
skimage/util/__pycache__/_invert.cpython-311.pyc,,
skimage/util/__pycache__/_label.cpython-311.pyc,,
skimage/util/__pycache__/_map_array.cpython-311.pyc,,
skimage/util/__pycache__/_montage.cpython-311.pyc,,
skimage/util/__pycache__/_regular_grid.cpython-311.pyc,,
skimage/util/__pycache__/_slice_along_axes.cpython-311.pyc,,
skimage/util/__pycache__/apply_parallel.cpython-311.pyc,,
skimage/util/__pycache__/arraycrop.cpython-311.pyc,,
skimage/util/__pycache__/compare.cpython-311.pyc,,
skimage/util/__pycache__/dtype.cpython-311.pyc,,
skimage/util/__pycache__/lookfor.cpython-311.pyc,,
skimage/util/__pycache__/noise.cpython-311.pyc,,
skimage/util/__pycache__/shape.cpython-311.pyc,,
skimage/util/__pycache__/unique.cpython-311.pyc,,
skimage/util/_invert.py,sha256=Yb-ML5OWLDxkrZ1KezI0SUUToJoiWmMDNiQ2bhMGYbw,2560
skimage/util/_label.py,sha256=IIZB-YW5o8kfWw6gWnaiOlKpN9QA2YaTF1k1_cvl7lg,1568
skimage/util/_map_array.py,sha256=KyrZTTtoNjcbnxKP5AwScKYVAdAw1wEMsexW7y-qFk4,6695
skimage/util/_montage.py,sha256=zDz0BAOyBIgVU080c2L_qLqWzLM8_BKxDkp5e5i_9LE,4779
skimage/util/_regular_grid.py,sha256=ZPzONVbwy_jK_QWlTqNUW2Wxi9yrvuJblTohugaHVYE,3889
skimage/util/_remap.cpython-311-x86_64-linux-gnu.so,sha256=c0udejPCCsEFA66rlHgSOb6lEO74FXNXyk80b7DCaNU,867232
skimage/util/_slice_along_axes.py,sha256=GnVxeqAjbDULUc27edh4dBHXFRvsLc0zWkdII7K-G6w,2577
skimage/util/apply_parallel.py,sha256=RmxuSK7XzT3I09W7HNpmxTeFWB6mNNXl4wlMsvZwrM0,7649
skimage/util/arraycrop.py,sha256=VY355ZEFWG0LDjdKM4Nr1EeRnp5dkfIVhhJ7fK-r-6w,2486
skimage/util/compare.py,sha256=99-dgt1POAOxXGe25O3WrU0mVEnf8VR2YTDEp8Sot48,4507
skimage/util/dtype.py,sha256=lo4Q6NjGv8B5VT-JRhIX7kIOAZYlVvl8_3XdtO55c6s,17681
skimage/util/lookfor.py,sha256=dVNQdXVwNjifvr0U7hl8lWnIkA-yx4aUQZhayBdkawE,790
skimage/util/noise.py,sha256=US93udCAXXM9Hyrua8mMfd3fnV91BL50kIv9qJ4LD08,8563
skimage/util/shape.py,sha256=_JqxvcCfpGSCi27jzpbmENBGBrkME6PtWPgBOOZch2s,7828
skimage/util/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
skimage/util/tests/__pycache__/__init__.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_apply_parallel.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_arraycrop.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_compare.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_dtype.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_invert.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_labels.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_lookfor.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_map_array.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_montage.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_random_noise.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_regular_grid.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_shape.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_slice_along_axes.cpython-311.pyc,,
skimage/util/tests/__pycache__/test_unique_rows.cpython-311.pyc,,
skimage/util/tests/test_apply_parallel.py,sha256=JJktiQVZthEx31ez46tgh31b9B8XBGXoB8fBVrRbISE,5028
skimage/util/tests/test_arraycrop.py,sha256=74HQ6L3Fr-M7SbzsSysQX5C5WJi_rkCYaT1CEPt2TUY,1848
skimage/util/tests/test_compare.py,sha256=6vw-pKwGQmoeksMI7xaXEDb6t0-fl9oEU-04n8Z2elY,3890
skimage/util/tests/test_dtype.py,sha256=1WgIQDe2AJ6mJSa2Jc51dHdBgLjGal8_cxyyr_7meTM,6538
skimage/util/tests/test_invert.py,sha256=zyGo_pKyh4lkGBYMoH2NUybt8bS8mrLCKORFhfldegg,2415
skimage/util/tests/test_labels.py,sha256=4anR0FT19CwbCUeSws27o3OB3GqBgF94FsfGvHnZQv4,1802
skimage/util/tests/test_lookfor.py,sha256=mWlprwlDVZ5wZCF5-vpqNCG3JszKzW1pbuFi_u7f-I0,305
skimage/util/tests/test_map_array.py,sha256=_sZd5Y0cOSH4vrffi0ytQv61LChB64pSrtc8yzD3FUA,2900
skimage/util/tests/test_montage.py,sha256=NtQ6oOPXBnP1IedY6PhcAJvJLH0DE11ifXU02CZCsO0,5670
skimage/util/tests/test_random_noise.py,sha256=M-AfwvDPTGx5knXRRg8Fgj76EML8dPmnkKDXFhWjFZU,7670
skimage/util/tests/test_regular_grid.py,sha256=mBlLa6recFg17Re44x25nts1ZLF9GKFs-skHc_yxPdc,980
skimage/util/tests/test_shape.py,sha256=5xNtsof9rRzi_-GfHNeyuHvhCyZE-Q3HIlZ2Y-tOqUs,4549
skimage/util/tests/test_slice_along_axes.py,sha256=3JwGWfOXd9tU1pRtVJQhB1PxFxS5p5FUDomRt9AgGeY,1681
skimage/util/tests/test_unique_rows.py,sha256=YZvcX1tHXRGyg7x9w71v7dJCi4CuS_ZZkZtkfjM2H3k,1099
skimage/util/unique.py,sha256=ctiKRbgA7Zra5-yA58gKup0fksPuPSHYcx_O_HU711E,1516
