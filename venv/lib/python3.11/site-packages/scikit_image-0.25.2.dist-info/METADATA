Metadata-Version: 2.1
Name: scikit-image
Version: 0.25.2
Summary: Image processing in Python
Maintainer-Email: scikit-image developers <<EMAIL>>
License: Files: *
         Copyright: 2009-2022 the scikit-image team
         License: BSD-3-Clause
         
         Files: doc/source/themes/scikit-image/layout.html
         Copyright: 2007-2010 the Sphinx team
         License: BSD-3-Clause
         
         Files: skimage/feature/_canny.py
                skimage/filters/edges.py
                skimage/filters/_rank_order.py
                skimage/morphology/_skeletonize.py
                skimage/morphology/tests/test_watershed.py
                skimage/morphology/watershed.py
                skimage/segmentation/heap_general.pxi
                skimage/segmentation/heap_watershed.pxi
                skimage/segmentation/_watershed.py
                skimage/segmentation/_watershed_cy.pyx
         Copyright: 2003-2009 Massachusetts Institute of Technology
                    2009-2011 Broad Institute
                    2003 <PERSON>
                    2003-2005 Peter <PERSON>. <PERSON>erveer
         License: BSD-3-Clause
         
         Files: skimage/filters/thresholding.py
                skimage/graph/_mcp.pyx
                skimage/graph/heap.pyx
         Copyright: 2009-2015 Board of Regents of the University of
                    Wisconsin-Madison, Broad Institute of MIT and Harvard,
                    and Max Planck Institute of Molecular Cell Biology and
                    Genetics
                    2009 <PERSON>
                    2009 Almar Klein
         License: BSD-2-Clause
         
         File: skimage/morphology/grayreconstruct.py
               skimage/morphology/tests/test_reconstruction.py
         Copyright: 2003-2009 Massachusetts Institute of Technology
                    2009-2011 Broad Institute
                    2003 Lee Kamentsky
         License: BSD-3-Clause
         
         File: skimage/morphology/_grayreconstruct.pyx
         Copyright: 2003-2009 Massachusetts Institute of Technology
                    2009-2011 Broad Institute
                    2003 Lee Kamentsky
                    2022 Gregory Lee (added a 64-bit integer variant for large images)
         License: BSD-3-Clause
         
         File: skimage/segmentation/_expand_labels.py
         Copyright: 2020 Broad Institute
                    2020 CellProfiler team
         License: BSD-3-Clause
         
         File: skimage/exposure/_adapthist.py
         Copyright: 1994 Karel Zuiderveld
         License: BSD-3-Clause
         
         Function: skimage/morphology/_skeletonize_various_cy.pyx:_skeletonize_loop
         Copyright: 2003-2009 Massachusetts Institute of Technology
                    2009-2011 Broad Institute
                    2003 Lee Kamentsky
         License: BSD-3-Clause
         
         Function: skimage/_shared/version_requirements.py:_check_version
         Copyright: 2013 The IPython Development Team
         License: BSD-3-Clause
         
         Function: skimage/_shared/version_requirements.py:is_installed
         Copyright: 2009-2011 Pierre Raybaut
         License: MIT
         
         File: skimage/feature/_fisher_vector.py
         Copyright: 2014 2014 Dan Oneata
         License: MIT
         
         File: skimage/_vendored/numpy_lookfor.py
         Copyright: 2005-2023, NumPy Developers
         License: BSD-3-Clause
         
         File: skimage/transform/_thin_plate_splines.py
         Copyright: 2007 Zachary Pincus
         License: BSD-3-Clause
         
         License: BSD-2-Clause
         
         Redistribution and use in source and binary forms, with or without
         modification, are permitted provided that the following conditions
         are met:
         1. Redistributions of source code must retain the above copyright
            notice, this list of conditions and the following disclaimer.
         2. Redistributions in binary form must reproduce the above copyright
            notice, this list of conditions and the following disclaimer in the
            documentation and/or other materials provided with the distribution.
         .
         THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
         ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
         LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
         A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE HOLDERS OR
         CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
         EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
         PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
         PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
         LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
         NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
         SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
         
         License: BSD-3-Clause
         
         Redistribution and use in source and binary forms, with or without
         modification, are permitted provided that the following conditions
         are met:
         1. Redistributions of source code must retain the above copyright
            notice, this list of conditions and the following disclaimer.
         2. Redistributions in binary form must reproduce the above copyright
            notice, this list of conditions and the following disclaimer in the
            documentation and/or other materials provided with the distribution.
         3. Neither the name of the University nor the names of its contributors
            may be used to endorse or promote products derived from this software
            without specific prior written permission.
         .
         THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
         ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
         LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
         A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE HOLDERS OR
         CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
         EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
         PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
         PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
         LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
         NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
         SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
         
         License: MIT
         
         Permission is hereby granted, free of charge, to any person obtaining a copy
         of this software and associated documentation files (the "Software"), to deal
         in the Software without restriction, including without limitation the rights
         to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
         copies of the Software, and to permit persons to whom the Software is
         furnished to do so, subject to the following conditions:
         
         The above copyright notice and this permission notice shall be included in all
         copies or substantial portions of the Software.
         
         THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
         IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
         FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
         AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
         LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
         OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
         SOFTWARE.
         
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: C
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Scientific/Engineering
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Operating System :: MacOS
Project-URL: homepage, https://scikit-image.org
Project-URL: documentation, https://scikit-image.org/docs/stable
Project-URL: source, https://github.com/scikit-image/scikit-image
Project-URL: download, https://pypi.org/project/scikit-image/#files
Project-URL: tracker, https://github.com/scikit-image/scikit-image/issues
Requires-Python: >=3.10
Requires-Dist: numpy>=1.24
Requires-Dist: scipy>=1.11.4
Requires-Dist: networkx>=3.0
Requires-Dist: pillow>=10.1
Requires-Dist: imageio!=2.35.0,>=2.33
Requires-Dist: tifffile>=2022.8.12
Requires-Dist: packaging>=21
Requires-Dist: lazy-loader>=0.4
Provides-Extra: build
Requires-Dist: meson-python>=0.16; extra == "build"
Requires-Dist: ninja>=********; extra == "build"
Requires-Dist: Cython>=3.0.8; extra == "build"
Requires-Dist: pythran>=0.16; extra == "build"
Requires-Dist: numpy>=2.0; extra == "build"
Requires-Dist: spin==0.13; extra == "build"
Requires-Dist: build>=1.2.1; extra == "build"
Provides-Extra: data
Requires-Dist: pooch>=1.6.0; extra == "data"
Provides-Extra: developer
Requires-Dist: pre-commit; extra == "developer"
Requires-Dist: ipython; extra == "developer"
Requires-Dist: tomli; python_version < "3.11" and extra == "developer"
Provides-Extra: docs
Requires-Dist: sphinx>=8.0; extra == "docs"
Requires-Dist: sphinx-gallery[parallel]>=0.18; extra == "docs"
Requires-Dist: numpydoc>=1.7; extra == "docs"
Requires-Dist: sphinx-copybutton; extra == "docs"
Requires-Dist: matplotlib>=3.7; extra == "docs"
Requires-Dist: dask[array]>=2023.2.0; extra == "docs"
Requires-Dist: pandas>=2.0; extra == "docs"
Requires-Dist: seaborn>=0.11; extra == "docs"
Requires-Dist: pooch>=1.6; extra == "docs"
Requires-Dist: tifffile>=2022.8.12; extra == "docs"
Requires-Dist: myst-parser; extra == "docs"
Requires-Dist: intersphinx-registry>=0.2411.14; extra == "docs"
Requires-Dist: ipywidgets; extra == "docs"
Requires-Dist: ipykernel; extra == "docs"
Requires-Dist: plotly>=5.20; extra == "docs"
Requires-Dist: kaleido==0.2.1; extra == "docs"
Requires-Dist: scikit-learn>=1.2; extra == "docs"
Requires-Dist: sphinx_design>=0.5; extra == "docs"
Requires-Dist: pydata-sphinx-theme>=0.16; extra == "docs"
Requires-Dist: PyWavelets>=1.6; extra == "docs"
Requires-Dist: pytest-doctestplus; extra == "docs"
Provides-Extra: optional
Requires-Dist: SimpleITK; extra == "optional"
Requires-Dist: astropy>=5.0; extra == "optional"
Requires-Dist: cloudpickle>=1.1.1; extra == "optional"
Requires-Dist: dask[array]>=2023.2.0; extra == "optional"
Requires-Dist: matplotlib>=3.7; extra == "optional"
Requires-Dist: pooch>=1.6.0; extra == "optional"
Requires-Dist: pyamg>=5.2; extra == "optional"
Requires-Dist: PyWavelets>=1.6; extra == "optional"
Requires-Dist: scikit-learn>=1.2; extra == "optional"
Provides-Extra: test
Requires-Dist: asv; extra == "test"
Requires-Dist: numpydoc>=1.7; extra == "test"
Requires-Dist: pooch>=1.6.0; extra == "test"
Requires-Dist: pytest>=8; extra == "test"
Requires-Dist: pytest-cov>=2.11.0; extra == "test"
Requires-Dist: pytest-localserver; extra == "test"
Requires-Dist: pytest-faulthandler; extra == "test"
Requires-Dist: pytest-doctestplus; extra == "test"
Description-Content-Type: text/markdown

# scikit-image: Image processing in Python

[![Image.sc forum](https://img.shields.io/badge/dynamic/json.svg?label=forum&url=https%3A%2F%2Fforum.image.sc%2Ftags%2Fscikit-image.json&query=%24.topic_list.tags.0.topic_count&colorB=brightgreen&suffix=%20topics&logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABPklEQVR42m3SyyqFURTA8Y2BER0TDyExZ+aSPIKUlPIITFzKeQWXwhBlQrmFgUzMMFLKZeguBu5y+//17dP3nc5vuPdee6299gohUYYaDGOyyACq4JmQVoFujOMR77hNfOAGM+hBOQqB9TjHD36xhAa04RCuuXeKOvwHVWIKL9jCK2bRiV284QgL8MwEjAneeo9VNOEaBhzALGtoRy02cIcWhE34jj5YxgW+E5Z4iTPkMYpPLCNY3hdOYEfNbKYdmNngZ1jyEzw7h7AIb3fRTQ95OAZ6yQpGYHMMtOTgouktYwxuXsHgWLLl+4x++Kx1FJrjLTagA77bTPvYgw1rRqY56e+w7GNYsqX6JfPwi7aR+Y5SA+BXtKIRfkfJAYgj14tpOF6+I46c4/cAM3UhM3JxyKsxiOIhH0IO6SH/A1Kb1WBeUjbkAAAAAElFTkSuQmCC)](https://forum.image.sc/tags/scikit-image)
[![Stackoverflow](https://img.shields.io/badge/stackoverflow-Ask%20questions-blue.svg)](https://stackoverflow.com/questions/tagged/scikit-image)
[![project chat](https://img.shields.io/badge/zulip-join_chat-brightgreen.svg)](https://skimage.zulipchat.com)

- **Website (including documentation):** [https://scikit-image.org/](https://scikit-image.org)
- **Documentation:** [https://scikit-image.org/docs/stable/](https://scikit-image.org/docs/stable/)
- **User forum:** [https://forum.image.sc/tag/scikit-image](https://forum.image.sc/tag/scikit-image)
- **Developer forum:** [https://discuss.scientific-python.org/c/contributor/skimage](https://discuss.scientific-python.org/c/contributor/skimage)
- **Source:** [https://github.com/scikit-image/scikit-image](https://github.com/scikit-image/scikit-image)

## Installation

- **pip:** `pip install scikit-image`
- **conda:** `conda install -c conda-forge scikit-image`

Also see [installing `scikit-image`](https://github.com/scikit-image/scikit-image/blob/main/INSTALL.rst).

## License

See [LICENSE.txt](https://github.com/scikit-image/scikit-image/blob/main/LICENSE.txt).

## Citation

If you find this project useful, please cite:

> Stéfan van der Walt, Johannes L. Schönberger, Juan Nunez-Iglesias,
> François Boulogne, Joshua D. Warner, Neil Yager, Emmanuelle
> Gouillart, Tony Yu, and the scikit-image contributors.
> _scikit-image: Image processing in Python_. PeerJ 2:e453 (2014)
> https://doi.org/10.7717/peerj.453
