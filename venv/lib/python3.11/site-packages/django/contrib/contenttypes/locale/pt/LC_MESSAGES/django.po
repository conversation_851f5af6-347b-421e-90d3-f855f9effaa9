# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011-2012
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2024-08-07 19:22+0000\n"
"Last-Translator: pedromcaraujo <<EMAIL>>, 2024\n"
"Language-Team: Portuguese (http://app.transifex.com/django/django/language/"
"pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipos de Conteúdo"

msgid "python model class name"
msgstr "nome da classe do model em python"

msgid "content type"
msgstr "tipo de conteúdo"

msgid "content types"
msgstr "tipos de conteúdos"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objeto do tipo de conteúdo %(ct_id)s não tem nenhum model associado"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "O objeto %(obj_id)s do tipo de conteúdo %(ct_id)s não existe."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objetos %(ct_name)s não têm o método get_absolute_url()."
