# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013,2015
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Georgian (http://www.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Content Types"
msgstr "კონტენტის ტიპები"

msgid "python model class name"
msgstr "python-ის მოდელის კლასის სახელი"

msgid "content type"
msgstr "კონტენტის ტიპი"

msgid "content types"
msgstr "კონტენტის ტიპები"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "კონტენტის ტიპის %(ct_id)s ობიექტს არ გააჩნია ასოცირებული მოდელი"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "კონტენტის ტიპის %(ct_id)s ობიექტი %(obj_id)s არ არსებობს"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s ობიექტებს არ გააჩნიათ მეთოდი get_absolute_url()"
