# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011,2013-2014,2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-17 22:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: German (http://www.transifex.com/django/django/language/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Inhaltstypen"

msgid "python model class name"
msgstr "Python Modell-Klassenname"

msgid "content type"
msgstr "Inhaltstyp"

msgid "content types"
msgstr "Inhaltstypen"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objekt des Inhaltstyps %(ct_id)s hat kein dazugehöriges Modell"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Objekt %(obj_id)s des Inhaltstyps %(ct_id)s ist nicht vorhanden"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr " %(ct_name)s Objekte haben keine get_absolute_url ()-Methode"
