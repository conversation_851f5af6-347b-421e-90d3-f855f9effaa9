# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-07-24 18:32+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkmen (http://www.transifex.com/django/django/language/"
"tk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tk\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Gaý<PERSON><PERSON> gö<PERSON>ükdirmeler"

msgid "site"
msgstr "sahypa"

msgid "redirect from"
msgstr "gönükdirilen salgy"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Bu domen ady hasaba almazdan, doly ýol bolmaly. Mysal: “/wakalar/gozleg/”."

msgid "redirect to"
msgstr "gönükdirilen salgy"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"(ýokarydaky ýaly) absolýut yzarlama ýa-da \"https://\" bilen başlanýan "
"sahypa gönükdirmesi bolup biler."

msgid "redirect"
msgstr "gönükdirme"

msgid "redirects"
msgstr "gönükdirmeler"
