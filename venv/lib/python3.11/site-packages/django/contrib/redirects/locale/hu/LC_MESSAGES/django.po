# This file is distributed under the same license as the Django package.
#
# Translators:
# And<PERSON><PERSON>Szentkirályi, 2016,2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-01 15:20+0000\n"
"Last-Translator: And<PERSON><PERSON>Szentkirályi\n"
"Language-Team: Hungarian (http://www.transifex.com/django/django/language/"
"hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Átirányítások"

msgid "site"
msgstr "honlap"

msgid "redirect from"
msgstr "átirányítva innen"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Abszolút útvonal legyen, a domain név nélkül. Például: \"/cikk/keres/\"."

msgid "redirect to"
msgstr "átirányítva ide"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Lehet abszolút útvonal is (mint feljebb), vagy teljes URL, amely \"https://"
"\"-sel kezdődik."

msgid "redirect"
msgstr "átirányítás"

msgid "redirects"
msgstr "átirányít"
