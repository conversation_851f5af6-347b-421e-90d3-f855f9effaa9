# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2014-2015
# Ta<PERSON>ya <PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-07-02 13:57+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese (http://www.transifex.com/django/django/language/"
"ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Redirects"
msgstr "リダイレクト"

msgid "site"
msgstr "サイト"

msgid "redirect from"
msgstr "リダイレクト元"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "“/events/search/” のように、ドメイン名を除いた絶対パスにします。 "

msgid "redirect to"
msgstr "リダイレクト先"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"これは上のような絶対パスにも「 https:// 」のようなスキームで始まる完全な URL "
"にもすることができます。"

msgid "redirect"
msgstr "リダイレクト"

msgid "redirects"
msgstr "リダイレクト"
