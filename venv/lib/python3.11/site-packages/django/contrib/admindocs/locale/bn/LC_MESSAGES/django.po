# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
# Ta<PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Bengali (http://www.transifex.com/django/django/language/"
"bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr ""

msgid "Home"
msgstr "নীড়পাতা"

msgid "Documentation"
msgstr "সহায়িকা"

msgid "Bookmarklets"
msgstr "বুকমার্কলেট"

msgid "Documentation bookmarklets"
msgstr "সহায়িকা বুকমার্কলেট"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "এই পাতার সহায়িকা"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "যেকোন পাতা থেকে ভিউ সহায়িকাতে নিয়ে যান।"

msgid "Tags"
msgstr "ট্যাগগুলো"

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr "ফিল্টারগুলো"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "মডেলগুলো"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "ভিউগুলো"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr "দয়া করে docutils ইনস্টল করুন"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr "মডেল: %(name)s"

msgid "Fields"
msgstr ""

msgid "Field"
msgstr "ফিল্ড"

msgid "Type"
msgstr "টাইপ"

msgid "Description"
msgstr "বিবরন"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr "মডেল ডকুমেন্টেশন"

msgid "Model groups"
msgstr "মডেল গ্রুপ"

msgid "Templates"
msgstr "টেমপ্লেটগুলো"

#, python-format
msgid "Template: %(name)s"
msgstr "টেমপ্লেট: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "টেমপ্লেট: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr ""

msgid "Back to Documentation"
msgstr "ডকুমেন্টেশনে ফেরত যান"

msgid "Template filters"
msgstr "টেমপ্লেট ফিল্টার"

msgid "Template filter documentation"
msgstr "টেমপ্লেট ফিল্টার ডকুমেন্টেশন"

msgid "Built-in filters"
msgstr "বিল্ট-ইন ফিল্টার"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr "টেমপ্লেট ট্যাগ"

msgid "Template tag documentation"
msgstr "টেমপ্লেট ট্যাগ ডকুমেন্টেশন"

msgid "Built-in tags"
msgstr "বিল্ট-ইন ট্যাগ"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr "ভিউ: %(name)s"

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr "টেমপ্লেটসমূহ:"

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr "ভিউ ডকুমেন্টেশন"

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr "ফাঁকা নেমস্পেস"

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "ট্যাগঃ"

msgid "filter:"
msgstr "ফিল্টারঃ"

msgid "view:"
msgstr "ভিউঃ"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(app_label)r এ্যপে %(model_name)r মডেলটি পাওয়া যায়নি"

msgid "model:"
msgstr "মডেলঃ"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "`%(app_label)s.%(data_type)s` সম্পর্কিত অবজেক্ট"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "`%(app_label)s.%(object_name)s` সম্পর্কিত অবজেক্ট সমূহ"

#, python-format
msgid "all %s"
msgstr "সকল %s"

#, python-format
msgid "number of %s"
msgstr "%s সংখ্যা"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ইউআরএল -এর মত নয়"
