# This file is distributed under the same license as the Django package.
#
# Translators:
# All<PERSON>on A<PERSON> <<EMAIL>>, 2014
# andrewsmed<PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# semente, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-17 05:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Administrative Documentation"
msgstr "Documentação Administrativa"

msgid "Home"
msgstr "Início"

msgid "Documentation"
msgstr "Documentação"

msgid "Bookmarklets"
msgstr "Itens de bookmark"

msgid "Documentation bookmarklets"
msgstr "Documentação de itens de bookmark"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar bookmarklets, arraste o link para a sua barra de favoritos, ou "
"clique com o botão direito no link e o adicione aos seus favoritos. Agora "
"você pode selecionar o bookmarklet a partir de qualquer página do site."

msgid "Documentation for this page"
msgstr "Documentação para esta página"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Leva você de qualquer página para a documentação da view que gera tal página."

msgid "Tags"
msgstr "Tags"

msgid "List of all the template tags and their functions."
msgstr "Lista de todas as tags de template e suas funções."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Os filtros são ações que podem ser aplicadas às variáveis ​​em um template "
"para alterar a saída."

msgid "Models"
msgstr "Models"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modelos são descrições de todos os objetos no sistema e seus campos "
"associados. Cada modelo tem uma lista de campos que podem ser acessados ​​como "
"variáveis ​​no template"

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada página no site público é gerada por uma view. A view define qual "
"template será usado para gerar a página e quais objetos estarão disponíveis "
"para este template."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Ferramentas para o seu navegador para acessar rapidamente a funcionalidade "
"de administração."

msgid "Please install docutils"
msgstr "Por favor, instale o docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"O sistema de documentação de administração exige a biblioteca <a href="
"\"%(link)s\">docutils</a> do Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor, peça para os seus administradores para instalar o <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descrição"

msgid "Methods with arguments"
msgstr "Métodos com argumentos"

msgid "Method"
msgstr "Método"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Voltar para Documentação do Model"

msgid "Model documentation"
msgstr "Documentação do model"

msgid "Model groups"
msgstr "Grupos de models"

msgid "Templates"
msgstr "Templates"

#, python-format
msgid "Template: %(name)s"
msgstr "Template: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Template: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Caminho de busca para o template <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(não existe)"

msgid "Back to Documentation"
msgstr "Voltar para Documentação"

msgid "Template filters"
msgstr "Filtros do template"

msgid "Template filter documentation"
msgstr "Documentação do filtro do template"

msgid "Built-in filters"
msgstr "Filtros built-in"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para usar esses filtros, coloque <code>%(code)s</code> no seu template antes "
"de usar o filtro."

msgid "Template tags"
msgstr "Tags de template"

msgid "Template tag documentation"
msgstr "Documentação da tag de template"

msgid "Built-in tags"
msgstr "Tags built-in"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para usar estas tags, coloque o código <code>%(code)s</code> no seu template "
"antes de usar a tag."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Templates:"

msgid "Back to View documentation"
msgstr "Voltar para a Documentação das Views"

msgid "View documentation"
msgstr "Ver documentação"

msgid "Jump to namespace"
msgstr "Ir para namespace"

msgid "Empty namespace"
msgstr "Namespace vazio"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views por namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Views por namespace vazio"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"View função: <code>%(full_name)s</code>. Nome: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "App %(app_label)r não encontrado"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r não encontrado na aplicação %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "o objeto `%(app_label)s.%(data_type)s` relacionado"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objetos `%(app_label)s.%(object_name)s` relacionados"

#, python-format
msgid "all %s"
msgstr "todos %s"

#, python-format
msgid "number of %s"
msgstr "número de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s não aparenta ser um objeto urlpattern"
