# This file is distributed under the same license as the Django package.
# 
# Translators:
# <PERSON>, 2023-2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-08-07 20:19+0000\n"
"Last-Translator: <PERSON>, 2023-2024\n"
"Language-Team: Serbian (Latin) (http://app.transifex.com/django/django/language/sr@latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: contrib/admindocs/apps.py:7
msgid "Administrative Documentation"
msgstr "Administrativna dokumentacija"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:6
#: contrib/admindocs/templates/admin_doc/index.html:6
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:6
#: contrib/admindocs/templates/admin_doc/model_detail.html:14
#: contrib/admindocs/templates/admin_doc/model_index.html:8
#: contrib/admindocs/templates/admin_doc/template_detail.html:6
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:7
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:7
#: contrib/admindocs/templates/admin_doc/view_detail.html:6
#: contrib/admindocs/templates/admin_doc/view_index.html:7
msgid "Home"
msgstr "Početna"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:7
#: contrib/admindocs/templates/admin_doc/index.html:7
#: contrib/admindocs/templates/admin_doc/index.html:10
#: contrib/admindocs/templates/admin_doc/index.html:14
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:7
#: contrib/admindocs/templates/admin_doc/missing_docutils.html:14
#: contrib/admindocs/templates/admin_doc/model_detail.html:15
#: contrib/admindocs/templates/admin_doc/model_index.html:9
#: contrib/admindocs/templates/admin_doc/template_detail.html:7
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:8
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:8
#: contrib/admindocs/templates/admin_doc/view_detail.html:7
#: contrib/admindocs/templates/admin_doc/view_index.html:8
msgid "Documentation"
msgstr "Dokumentacija"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:8
#: contrib/admindocs/templates/admin_doc/index.html:29
msgid "Bookmarklets"
msgstr "Bukmarkleti"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:11
msgid "Documentation bookmarklets"
msgstr "Bukmarkleti dokumentacije"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:15
msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr "Da biste instalirali obeleživače, prevucite vezu na traku sa alatkama za obeleživače ili kliknite desnim tasterom miša na vezu i dodajte je u obeleživače. Sada možete da izaberete bookmarklet sa bilo koje stranice na sajtu."

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:22
msgid "Documentation for this page"
msgstr "Dokumentacija za ovu stranicu"

#: contrib/admindocs/templates/admin_doc/bookmarklets.html:23
msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Vodi od bilo koje stranice do dokumentaicje pogleda koji je generisao tu stranicu."

#: contrib/admindocs/templates/admin_doc/index.html:17
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:9
msgid "Tags"
msgstr "Tagovi"

#: contrib/admindocs/templates/admin_doc/index.html:18
msgid "List of all the template tags and their functions."
msgstr "Lista svih oznaka šablona i njihovih funkcija."

#: contrib/admindocs/templates/admin_doc/index.html:20
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:9
msgid "Filters"
msgstr "Filteri"

#: contrib/admindocs/templates/admin_doc/index.html:21
msgid ""
"Filters are actions which can be applied to variables in a template to alter"
" the output."
msgstr "Filteri su radnje koje se mogu primeniti na promenljive u šablonu da bi se promenio izlaz."

#: contrib/admindocs/templates/admin_doc/index.html:23
#: contrib/admindocs/templates/admin_doc/model_detail.html:16
#: contrib/admindocs/templates/admin_doc/model_index.html:10
#: contrib/admindocs/templates/admin_doc/model_index.html:14
msgid "Models"
msgstr "Modeli"

#: contrib/admindocs/templates/admin_doc/index.html:24
msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr "Modeli su opisi svih objekata u sistemu i njihovih povezanih polja. Svaki model ima listu polja kojima se može pristupiti kao promenljive šablona"

#: contrib/admindocs/templates/admin_doc/index.html:26
#: contrib/admindocs/templates/admin_doc/view_detail.html:8
#: contrib/admindocs/templates/admin_doc/view_index.html:9
#: contrib/admindocs/templates/admin_doc/view_index.html:12
msgid "Views"
msgstr "Vjuevi"

#: contrib/admindocs/templates/admin_doc/index.html:27
msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr "Svaku stranicu na javnom sajtu generiše pogled. Pogled definiše koji šablon se koristi za generisanje stranice i koji objekti su dostupni tom šablonu."

#: contrib/admindocs/templates/admin_doc/index.html:30
msgid "Tools for your browser to quickly access admin functionality."
msgstr "Alatke za vaš pretraživač za brzi pristup funkcijama administratora."

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:10
msgid "Please install docutils"
msgstr "Molimo instalirajte docutils"

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:17
#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr "Sistem administrativne dokumentacije zahteva Pajton <a href=\"%(link)s\">docutils</a> biblioteku."

#: contrib/admindocs/templates/admin_doc/missing_docutils.html:19
#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Zamolite svoje administratore da instaliraju <a href=\"%(link)s\">docutils</a>."

#: contrib/admindocs/templates/admin_doc/model_detail.html:21
#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

#: contrib/admindocs/templates/admin_doc/model_detail.html:30
msgid "Fields"
msgstr "Polja"

#: contrib/admindocs/templates/admin_doc/model_detail.html:35
msgid "Field"
msgstr "Polje"

#: contrib/admindocs/templates/admin_doc/model_detail.html:36
msgid "Type"
msgstr "Tip"

#: contrib/admindocs/templates/admin_doc/model_detail.html:37
#: contrib/admindocs/templates/admin_doc/model_detail.html:60
msgid "Description"
msgstr "Opis"

#: contrib/admindocs/templates/admin_doc/model_detail.html:53
msgid "Methods with arguments"
msgstr "Metoda sa argumentima"

#: contrib/admindocs/templates/admin_doc/model_detail.html:58
msgid "Method"
msgstr "Metod"

#: contrib/admindocs/templates/admin_doc/model_detail.html:59
msgid "Arguments"
msgstr "Argumenti"

#: contrib/admindocs/templates/admin_doc/model_detail.html:76
msgid "Back to Model documentation"
msgstr "Nazad na dokumentaciju o Modelima"

#: contrib/admindocs/templates/admin_doc/model_index.html:18
msgid "Model documentation"
msgstr "Dokumentacija o Modelima"

#: contrib/admindocs/templates/admin_doc/model_index.html:43
msgid "Model groups"
msgstr "Grupe modela"

#: contrib/admindocs/templates/admin_doc/template_detail.html:8
msgid "Templates"
msgstr "Šabloni"

#: contrib/admindocs/templates/admin_doc/template_detail.html:13
#, python-format
msgid "Template: %(name)s"
msgstr "Šablon: %(name)s"

#: contrib/admindocs/templates/admin_doc/template_detail.html:16
#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Šablon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#: contrib/admindocs/templates/admin_doc/template_detail.html:19
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Putanja za traženje šablona <q>%(name)s</q>:"

#: contrib/admindocs/templates/admin_doc/template_detail.html:22
msgid "(does not exist)"
msgstr "(ne postoji)"

#: contrib/admindocs/templates/admin_doc/template_detail.html:26
msgid "Back to Documentation"
msgstr "Nazad na dokumentaciju"

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:12
msgid "Template filters"
msgstr "Filteri šablona"

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:16
msgid "Template filter documentation"
msgstr "Dokumentacija filtera šablona"

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:22
#: contrib/admindocs/templates/admin_doc/template_filter_index.html:43
msgid "Built-in filters"
msgstr "Ugrađeni filteri"

#: contrib/admindocs/templates/admin_doc/template_filter_index.html:23
#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr "Da biste koristili ove filtere, stavite  <code>%(code)s</code> u svoj šablon pre upotrebe filtera."

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:12
msgid "Template tags"
msgstr "Oznake šablona"

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:16
msgid "Template tag documentation"
msgstr "Dokumentacija oznake šablona"

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:22
#: contrib/admindocs/templates/admin_doc/template_tag_index.html:43
msgid "Built-in tags"
msgstr "Ugrađene oznake"

#: contrib/admindocs/templates/admin_doc/template_tag_index.html:23
#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr "Da biste koristili ove oznake, stavite <code>%(code)s</code> u svoj šablon pre upotrebe oznake."

#: contrib/admindocs/templates/admin_doc/view_detail.html:12
#, python-format
msgid "View: %(name)s"
msgstr "Pogled: %(name)s"

#: contrib/admindocs/templates/admin_doc/view_detail.html:23
msgid "Context:"
msgstr "Kontekst:"

#: contrib/admindocs/templates/admin_doc/view_detail.html:28
msgid "Templates:"
msgstr "Šabloni:"

#: contrib/admindocs/templates/admin_doc/view_detail.html:32
msgid "Back to View documentation"
msgstr "Nazad na dokumentaciju o pogledima"

#: contrib/admindocs/templates/admin_doc/view_index.html:16
msgid "View documentation"
msgstr "Dokumentacija o pogledima"

#: contrib/admindocs/templates/admin_doc/view_index.html:22
msgid "Jump to namespace"
msgstr "Skoči na imenski prostor"

#: contrib/admindocs/templates/admin_doc/view_index.html:27
msgid "Empty namespace"
msgstr "Prazan imenski prostor"

#: contrib/admindocs/templates/admin_doc/view_index.html:40
#, python-format
msgid "Views by namespace %(name)s"
msgstr "Pogledi po imenskom prostoru %(name)s"

#: contrib/admindocs/templates/admin_doc/view_index.html:42
msgid "Views by empty namespace"
msgstr "Pogledi po praznom imenskom prostoru"

#: contrib/admindocs/templates/admin_doc/view_index.html:49
#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>.\n"
msgstr "\n    Funkcija pogleda: <code>%(full_name)s</code>. Ime: <code>%(url_name)s</code>.\n"

#: contrib/admindocs/views.py:72 contrib/admindocs/views.py:73
#: contrib/admindocs/views.py:75
msgid "tag:"
msgstr "tag:"

#: contrib/admindocs/views.py:103 contrib/admindocs/views.py:104
#: contrib/admindocs/views.py:106
msgid "filter:"
msgstr "filter:"

#: contrib/admindocs/views.py:162 contrib/admindocs/views.py:163
#: contrib/admindocs/views.py:165
msgid "view:"
msgstr "vju:"

#: contrib/admindocs/views.py:192
#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplikacija %(app_label)r nije pronađena"

#: contrib/admindocs/views.py:196
#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r nije pronađen u aplikaciji %(app_label)r"

#: contrib/admindocs/views.py:201 contrib/admindocs/views.py:202
#: contrib/admindocs/views.py:217 contrib/admindocs/views.py:240
#: contrib/admindocs/views.py:245 contrib/admindocs/views.py:260
#: contrib/admindocs/views.py:301 contrib/admindocs/views.py:306
msgid "model:"
msgstr "model:"

#: contrib/admindocs/views.py:213
#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "povezani objekti klase `%(app_label)s.%(data_type)s`"

#: contrib/admindocs/views.py:233 contrib/admindocs/views.py:293
#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "klase `%(app_label)s.%(object_name)s`"

#: contrib/admindocs/views.py:240 contrib/admindocs/views.py:301
#, python-format
msgid "all %s"
msgstr "svi povezani objekti %s"

#: contrib/admindocs/views.py:245 contrib/admindocs/views.py:306
#, python-format
msgid "number of %s"
msgstr "broj povezanih objekata %s"

#: contrib/admindocs/views.py:398
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ne izgleda kao „urlpattern“ objekat"
