# This file is distributed under the same license as the Django package.
#
# Translators:
# Ah<PERSON> Emre Aladağ <<EMAIL>>, 2013
# BouRock, 2015-2016,2019,2021
# BouRock, 2014
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-16 19:32+0000\n"
"Last-Translator: BouRock\n"
"Language-Team: Turkish (http://www.transifex.com/django/django/language/"
"tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Administrative Documentation"
msgstr "Yönetimsel Belgeler"

msgid "Home"
msgstr "Giriş"

msgid "Documentation"
msgstr "Belgeler"

msgid "Bookmarklets"
msgstr "Kod içeren yer işaretşeri"

msgid "Documentation bookmarklets"
msgstr "Belge kod içeren yer işaretleri"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Kod içeren yer işaretlerini yüklemek için bağlantıyı yer imleri araç "
"çubuğuna sürükleyin ya da bağlantıya sağ tıklayın ve yer imlerinize ekleyin. "
"Artık sitedeki herhangi bir sayfadan kod içeren yer işaretini seçebilirsiniz."

msgid "Documentation for this page"
msgstr "Bu sayfa için belgeler"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Sizi, bu sayfayı oluşturan görünüm için herhangi bir sayfadan  belgelere "
"atlatır."

msgid "Tags"
msgstr "Etiketler"

msgid "List of all the template tags and their functions."
msgstr "Tüm şablon etiketlerinin ve işlevlerinin listesi."

msgid "Filters"
msgstr "Süzgeçler"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Süzgeçler, çıktıyı değiştirmek için bir şablondaki değişkenlere "
"uygulanabilen eylemlerdir."

msgid "Models"
msgstr "Modeller"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeller, sistemdeki nesnelerin ve ilişkilendirilmiş alanlarının tümünün "
"tanımlarıdır. Her model, şablon değişkenleri olarak erişilebilen alanların "
"bir listesine sahiptir."

msgid "Views"
msgstr "Görünümler"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Ortak sitedeki her sayfa, bir görünüm tarafından oluşturulur. Görünüm, "
"sayfayı oluşturmak için hangi şablonun kullanılacağını ve bu şablon için "
"hangi nesnelerin kullanılabilir olacağını tanımlar."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Tarayıcınızın yönetici işlevselliğine hızlıca erişebilmesi için araçlar"

msgid "Please install docutils"
msgstr "Lütfen docutils’i yükleyin"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Yönetici belge sistemi, Python’un <a href=\"%(link)s\">docutils</a> "
"kütüphanesini gerektirir."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Lütfen yöneticilerinizden <a href=\"%(link)s\">docutils</a> yüklemesini "
"isteyin."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Alanlar"

msgid "Field"
msgstr "Alan"

msgid "Type"
msgstr "Tür"

msgid "Description"
msgstr "Açıklama"

msgid "Methods with arguments"
msgstr "Bağımsız değişkenleri olan yöntemler"

msgid "Method"
msgstr "Yöntem"

msgid "Arguments"
msgstr "Bağımsız değişkenler"

msgid "Back to Model documentation"
msgstr "Model belgelerine geri dön"

msgid "Model documentation"
msgstr "Model belgeleri"

msgid "Model groups"
msgstr "Model grupları"

msgid "Templates"
msgstr "Şablonlar"

#, python-format
msgid "Template: %(name)s"
msgstr "Şablon: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Şablon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "<q>%(name)s</q> şablonu için arama yolu:"

msgid "(does not exist)"
msgstr "(mevcut değil)"

msgid "Back to Documentation"
msgstr "Belgelere Geri Dön"

msgid "Template filters"
msgstr "Şablon süzgeçleri"

msgid "Template filter documentation"
msgstr "Şablon süzgeci belgeleri"

msgid "Built-in filters"
msgstr "Yerleşik süzgeçler"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Bu süzgeçleri kullanmak için, süzgeci kullanmadan önce şablonunuzun içine "
"<code>%(code)s</code> yerleştirin."

msgid "Template tags"
msgstr "Şablon etiketleri"

msgid "Template tag documentation"
msgstr "Şablon etiketi belgeleri"

msgid "Built-in tags"
msgstr "Yerleşik etiketler"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Bu etiketleri kullanmak için, etiketi kullanmadan önce şablonunuzun içine "
"<code>%(code)s</code> yerleştirin."

#, python-format
msgid "View: %(name)s"
msgstr "Görünüm: %(name)s"

msgid "Context:"
msgstr "Bağlam:"

msgid "Templates:"
msgstr "Şablonlar:"

msgid "Back to View documentation"
msgstr "Görünüm belgelerine geri dön"

msgid "View documentation"
msgstr "Belgeleri görüntüle"

msgid "Jump to namespace"
msgstr "İsim alanına atla"

msgid "Empty namespace"
msgstr "Boş isim alanı"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "%(name)s isim alanına göre görünümler"

msgid "Views by empty namespace"
msgstr "Boş isim alanına göre görünümler"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Görünüm işlevi: <code>%(full_name)s</code>. Adı: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiket:"

msgid "filter:"
msgstr "süzgeç:"

msgid "view:"
msgstr "görünüm:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r uygulaması bulunamadı"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(app_label)r uygulamasında %(model_name)r modeli bulunamadı"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "İlgili `%(app_label)s.%(data_type)s` nesnesi"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "ilgili `%(app_label)s.%(object_name)s` nesneleri"

#, python-format
msgid "all %s"
msgstr "tüm %s"

#, python-format
msgid "number of %s"
msgstr "%s sayısı"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s, bir urlpattern nesnesi olarak görünmüyor"
