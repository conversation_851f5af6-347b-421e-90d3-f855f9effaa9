{% extends "admin/base_site.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; {% translate 'Filters' %}
</div>
{% endblock %}
{% block title %}{% translate 'Template filters' %}{% endblock %}

{% block content %}

<h1>{% translate 'Template filter documentation' %}</h1>

<div id="content-main">
{% regroup filters|dictsort:"library" by library as filter_libraries %}
{% for library in filter_libraries %}
<div class="module">
    <h2>{% firstof library.grouper _("Built-in filters") %}</h2>
    {% if library.grouper %}<p class="small quiet">{% blocktranslate with code="{"|add:"% load "|add:library.grouper|add:" %"|add:"}" %}To use these filters, put <code>{{ code }}</code> in your template before using the filter.{% endblocktranslate %}</p><hr>{% endif %}
    {% for filter in library.list|dictsort:"name" %}
    <h3 id="{{ library.grouper|default:"built_in" }}-{{ filter.name }}">{{ filter.name }}</h3>
    {{ filter.title }}
    {{ filter.body }}
    {% if not forloop.last %}<hr>{% endif %}
    {% endfor %}
</div>
{% endfor %}
</div>

{% endblock %}

{% block sidebar %}

<div id="content-related">

{% regroup filters|dictsort:"library" by library as filter_libraries %}
{% for library in filter_libraries %}
<div class="module">
    <h2>{% firstof library.grouper _("Built-in filters") %}</h2>
    <ul>
    {% for filter in library.list|dictsort:"name" %}
        <li><a href="#{{ library.grouper|default:"built_in" }}-{{ filter.name }}">{{ filter.name }}</a></li>
    {% endfor %}
    </ul>
</div>
{% endfor %}

</div>

{% endblock %}
