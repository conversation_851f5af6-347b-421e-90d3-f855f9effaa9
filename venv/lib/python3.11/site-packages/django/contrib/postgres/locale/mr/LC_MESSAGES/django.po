# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>mkar Parab, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2024-01-19 09:22+0000\n"
"Last-Translator: Omkar Parab, 2024\n"
"Language-Team: Marathi (http://app.transifex.com/django/django/language/"
"mr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mr\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr ""

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr ""

msgid "Nested arrays must have the same length."
msgstr ""

msgid "Map of strings to strings/nulls"
msgstr ""

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "JSON डेटा लोड करू शकलो नाही. "

msgid "Input must be a JSON dictionary."
msgstr ""

msgid "Enter two valid values."
msgstr "दोन वैध मूल्ये प्रविष्ट करा."

msgid "The start of the range must not exceed the end of the range."
msgstr ""

msgid "Enter two whole numbers."
msgstr "दोन पूर्ण संख्या प्रविष्ट करा."

msgid "Enter two numbers."
msgstr "दोन संख्या प्रविष्ट करा."

msgid "Enter two valid date/times."
msgstr "दोन वैध दिनांक/वेळा प्रविष्ट करा."

msgid "Enter two valid dates."
msgstr "दोन वैध दिनांका प्रविष्ट करा"

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr ""

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
