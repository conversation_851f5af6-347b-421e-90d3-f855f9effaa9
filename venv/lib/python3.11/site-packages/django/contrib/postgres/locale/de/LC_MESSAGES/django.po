# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2015-2018,2020
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: German (http://www.transifex.com/django/django/language/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL-Erweiterungen"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Element %(nth)s im Array konnte nicht validiert werden:"

msgid "Nested arrays must have the same length."
msgstr "Verschachtelte Arrays müssen die gleiche Länge haben."

msgid "Map of strings to strings/nulls"
msgstr "Zuordnung von Zeichenketten zu Zeichenketten/NULLs"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Der Wert für „%(key)s“ ist keine Zeichenkette oder NULL."

msgid "Could not load JSON data."
msgstr "Konnte JSON-Daten nicht laden."

msgid "Input must be a JSON dictionary."
msgstr "Eingabe muss ein JSON-Dictionary sein."

msgid "Enter two valid values."
msgstr "Bitte zwei gültige Werte eingeben."

msgid "The start of the range must not exceed the end of the range."
msgstr "Der Anfang des Wertbereichs darf nicht das Ende überschreiten."

msgid "Enter two whole numbers."
msgstr "Bitte zwei ganze Zahlen eingeben."

msgid "Enter two numbers."
msgstr "Bitte zwei Zahlen eingeben."

msgid "Enter two valid date/times."
msgstr "Bitte zwei gültige Datum/Uhrzeit-Werte eingeben."

msgid "Enter two valid dates."
msgstr "Bitte zwei gültige Kalenderdaten eingeben."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Liste enthält %(show_value)d Element, es sollte aber nicht mehr als "
"%(limit_value)d enthalten."
msgstr[1] ""
"Liste enthält %(show_value)d Elemente, es sollte aber nicht mehr als "
"%(limit_value)d enthalten."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Liste enthält %(show_value)d Element, es sollte aber nicht weniger als "
"%(limit_value)d enthalten."
msgstr[1] ""
"Liste enthält %(show_value)d Elemente, es sollte aber nicht weniger als "
"%(limit_value)d enthalten."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Einige Werte fehlen: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Einige unbekannte Werte wurden eingegeben: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Bitte sicherstellen, dass die obere Grenze des Bereichs nicht größer als "
"%(limit_value)s ist."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Bitte sicherstellen, dass die untere Grenze des Bereichs nicht kleiner als "
"%(limit_value)s ist."
