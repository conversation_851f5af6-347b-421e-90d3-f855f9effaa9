.dj_map_wrapper {
    position: relative;
    float: left;
}
html[dir="rtl"] .dj_map_wrapper {
    float: right;
}

.switch-type {
    background-repeat: no-repeat;
    cursor: pointer;
    top: 0.5em;
    width: 22px;
    height: 20px;
}

.type-Point {
    background-image: url("../img/draw_point_off.svg");
    right: 5px;
}
.type-Point.type-active {
    background-image: url("../img/draw_point_on.svg");
}

.type-LineString {
    background-image: url("../img/draw_line_off.svg");
    right: 30px;
}
.type-LineString.type-active {
    background-image: url("../img/draw_line_on.svg");
}

.type-Polygon {
    background-image: url("../img/draw_polygon_off.svg");
    right: 55px;
}
.type-Polygon.type-active {
    background-image: url("../img/draw_polygon_on.svg");
}
