# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# <PERSON>, 2014-2015
# <PERSON>lia Volochii <<EMAIL>>, 2021,2023,2025
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014,2021
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON>, 2016-2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON><PERSON> Volochii <<EMAIL>>, 2021,2023,2025\n"
"Language-Team: Ukrainian (http://app.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Personal info"
msgstr "Особиста інформація"

msgid "Permissions"
msgstr "Дозволи"

msgid "Important dates"
msgstr "Важливі дати"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(name)s об'єкт з первинним ключем %(key)r не існує."

msgid "Conflicting form data submitted. Please try again."
msgstr "Надіслано суперечливі дані форми. Спробуйте ще раз."

msgid "Password changed successfully."
msgstr "Пароль успішно змінено."

msgid "Password-based authentication was disabled."
msgstr "Автентифікацію на основі пароля вимкнено."

#, python-format
msgid "Change password: %s"
msgstr "Змінити пароль: %s"

#, python-format
msgid "Set password: %s"
msgstr "Встановити пароль: %s"

msgid "Authentication and Authorization"
msgstr "Аутентифікація та авторизація"

msgid "password"
msgstr "пароль"

msgid "last login"
msgstr "останній вхід"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Невірний формат пароля або невідомий алгоритм хешування."

msgid "No password set."
msgstr "Пароль не встановлено."

msgid "Reset password"
msgstr "Скинути пароль"

msgid "Set password"
msgstr "Встановити пароль"

msgid "The two password fields didn’t match."
msgstr "Паролі не збігаються"

msgid "Password"
msgstr "Пароль"

msgid "Password confirmation"
msgstr "Підтвердження пароля"

msgid "Enter the same password as before, for verification."
msgstr "Введіть той же пароль, що і раніше, для підтвердження."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Чи зможе користувач здійснити автентифікацію за допомогою пароля. Якщо "
"вимкнено, все ще може існувати можливість автентифікації іншими шляхами, як-"
"от Single Sign-On чи LDAP."

msgid "Password-based authentication"
msgstr "Автентифікація на основі пароля"

msgid "Enabled"
msgstr "Увімкнено"

msgid "Disabled"
msgstr "Вимкнено"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Необроблені паролі не зберігаються, тому немає можливості побачити пароль "
"користувача."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Увімкніть автентифікацію на основі пароля для цього користувача, встановивши "
"пароль."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Будь ласка, введіть правильні %(username)s та пароль. Зауважте, що обидва "
"поля чутливі до регістру."

msgid "This account is inactive."
msgstr "Цей запис користувача не активний."

msgid "Email"
msgstr "Email"

msgid "New password"
msgstr "Новий пароль"

msgid "New password confirmation"
msgstr "Новий пароль (підтвердження)"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""
"Старий пароль було введено неправильно. Будь ласка, введіть його знову."

msgid "Old password"
msgstr "Старий пароль"

msgid "algorithm"
msgstr "алгоритм"

msgid "iterations"
msgstr "ітерації"

msgid "salt"
msgstr "сіль"

msgid "hash"
msgstr "хеш"

msgid "variety"
msgstr "різноманітність"

msgid "version"
msgstr "версія"

msgid "memory cost"
msgstr "витрати пам’яті"

msgid "time cost"
msgstr "витрати часу"

msgid "parallelism"
msgstr "паралелізм"

msgid "work factor"
msgstr "робочий фактор"

msgid "checksum"
msgstr "контрольна сума"

msgid "block size"
msgstr "розмір блоку"

msgid "name"
msgstr "ім'я"

msgid "content type"
msgstr "тип вмісту"

msgid "codename"
msgstr "код"

msgid "permission"
msgstr "дозвіл"

msgid "permissions"
msgstr "дозволи"

msgid "group"
msgstr "група"

msgid "groups"
msgstr "групи"

msgid "superuser status"
msgstr "статус суперкористувача"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "Визначає, що цей користувач має всі дозволи без їх точного зазначення."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Групи, до яких належить користувач. Користувач отримає всі дозволи, що "
"вказані в кожній з його груп."

msgid "user permissions"
msgstr "дозволи користувача"

msgid "Specific permissions for this user."
msgstr "Особливі права доступу для цього користувача."

msgid "username"
msgstr "ім'я користувача"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Необхідно: 150 або менше символів. тільки букви, цифри та  знаки @/./+/-/_."

msgid "A user with that username already exists."
msgstr "Користувач з таким ім'ям вже існує."

msgid "first name"
msgstr "ім'я"

msgid "last name"
msgstr "прізвище"

msgid "email address"
msgstr "email адреса"

msgid "staff status"
msgstr "статус персоналу"

msgid "Designates whether the user can log into this admin site."
msgstr "Визначає, чи може користувач увійти до цього сайту адміністрування."

msgid "active"
msgstr "активний"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Визначає, чи можна цього користувача вважати діючим. Заберіть галочку, "
"замість видалення запису користувача."

msgid "date joined"
msgstr "дата приєднання"

msgid "user"
msgstr "користувач"

msgid "users"
msgstr "користувачі"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Ваш пароль повинен містити як мінімум %(min_length)d символ"
msgstr[1] "Ваш пароль повинен містити як мінімум %(min_length)d символи"
msgstr[2] "Ваш пароль повинен містити як мінімум %(min_length)d символів"
msgstr[3] "Ваш пароль повинен містити як мінімум %(min_length)d символів"

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Пароль надто схожий на %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Пароль не може бути надто схожим на іншу особисту інформацію."

msgid "This password is too common."
msgstr "Пароль надто відомий."

msgid "Your password can’t be a commonly used password."
msgstr "Пароль не може бути одним із дуже поширених."

msgid "This password is entirely numeric."
msgstr "Цей пароль повністю складається із цифр."

msgid "Your password can’t be entirely numeric."
msgstr "Пароль не може складається лише із цифр."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Скидання пароля на %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Введіть коректне ім'я користувача. Значення цього поля може складатися лише "
"з малих літер a‒z і великих A‒Z, цифр, а також з символів @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Введіть коректне ім'я користувача. Значення цього поля може складатися лише "
"з літер, цифр, а також з символів: @/./+/-/_."

msgid "Logged out"
msgstr "Вихід"

msgid "Password reset"
msgstr "Перевстановлення паролю"

msgid "Password reset sent"
msgstr "Скидання пароля відправлено"

msgid "Enter new password"
msgstr "Введіть новий пароль"

msgid "Password reset unsuccessful"
msgstr "Пароль не перевстановлено"

msgid "Password reset complete"
msgstr "Пароль перевстановлено"

msgid "Password change"
msgstr "Зміна паролю"

msgid "Password change successful"
msgstr "Пароль успішно змінено"
