# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015-2016,2018,2020-2021,2023-2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2011,2013
# <AUTHOR> <EMAIL>, 2013-2016
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, "
"2015-2016,2018,2020-2021,2023-2024\n"
"Language-Team: Indonesian (http://app.transifex.com/django/django/language/"
"id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Personal info"
msgstr "Informasi pribadi"

msgid "Permissions"
msgstr "Hak akses"

msgid "Important dates"
msgstr "Tanggal penting"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "Objek %(name)s dengan primary key %(key)r tidak ditemukan."

msgid "Conflicting form data submitted. Please try again."
msgstr "Data yang diajukan konflik. Silahkan coba kembali."

msgid "Password changed successfully."
msgstr "Sandi berhasil diubah."

msgid "Password-based authentication was disabled."
msgstr "Autentifikasi berdasarkan-sandi telah ditiadakan."

#, python-format
msgid "Change password: %s"
msgstr "Ganti sandi: %s"

#, python-format
msgid "Set password: %s"
msgstr "Setel sandi: %s"

msgid "Authentication and Authorization"
msgstr "Autentikasi dan Otorisasi"

msgid "password"
msgstr "sandi"

msgid "last login"
msgstr "masuk terakhir"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Format sandi tidak valid atau algoritma hash yang tidak dikenal."

msgid "No password set."
msgstr "Belum ada sandi yang disetel."

msgid "Reset password"
msgstr "Setel kembali sandi"

msgid "Set password"
msgstr "Setel sandi"

msgid "The two password fields didn’t match."
msgstr "Dua bidang sandi tidak cocok."

msgid "Password"
msgstr "Sandi"

msgid "Password confirmation"
msgstr "Konfirmasi sandi"

msgid "Enter the same password as before, for verification."
msgstr "Masukkan sandi yang sama seperti sebelumnya, untuk verifikasi."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Apakah pengguna dapat mengautentifikasi menggunakan sandi atau tidak. Jika "
"ditiadakan, mereka mungkin masih dapat autentidikasi menggunakan backend "
"lain, seperti Single Sign-On atau LDAP."

msgid "Password-based authentication"
msgstr "Autentidikasi berdasarkan-sandi"

msgid "Enabled"
msgstr "Diadakan"

msgid "Disabled"
msgstr "Ditiadakan"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Sandi mentah tidak disimpan, sehingga tidak mungkin melihat sandi pengguna."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Mengadakan autentifikasi berdasarkan-sandi untuk pengguna ini dengan "
"menyetel sandi."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Masukkan nama pengguna %(username)s dan sandi yang benar. Huruf besar/kecil "
"pada bidang ini berpengaruh."

msgid "This account is inactive."
msgstr "Akun ini tidak aktif."

msgid "Email"
msgstr "Email"

msgid "New password"
msgstr "Sandi baru"

msgid "New password confirmation"
msgstr "Konfirmasi sandi baru"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Sandi lama Anda yang dimasukkan salah. Masukkan sekali lagi."

msgid "Old password"
msgstr "Sandi lama"

msgid "algorithm"
msgstr "algoritme"

msgid "iterations"
msgstr "iterasi"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "hash"

msgid "variety"
msgstr "keanekaragaman"

msgid "version"
msgstr "versi"

msgid "memory cost"
msgstr "biaya memori"

msgid "time cost"
msgstr "biaya waktu"

msgid "parallelism"
msgstr "sifat paralel"

msgid "work factor"
msgstr "faktor kerja"

msgid "checksum"
msgstr "ceksum"

msgid "block size"
msgstr "ukuran blok"

msgid "name"
msgstr "nama"

msgid "content type"
msgstr "jenis isi"

msgid "codename"
msgstr "namasandi"

msgid "permission"
msgstr "hak akses"

msgid "permissions"
msgstr "hak akses"

msgid "group"
msgstr "grup"

msgid "groups"
msgstr "grup"

msgid "superuser status"
msgstr "status superuser"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Menentukan apakah pengguna memiliki semua hak akses tanpa perlu diberikan "
"secara manual."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Grup tempat pengguna ini dikelompokkan. Pengguna akan mendapatkan semua hak "
"akses yang diberikan pada grup mereka."

msgid "user permissions"
msgstr "hak akses pengguna"

msgid "Specific permissions for this user."
msgstr "Hak akses khusus untuk pengguna ini."

msgid "username"
msgstr "nama pengguna"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "Wajib. 150 karakter atau sedikit. Hanya huruf, angka, dan @/./+/-/_."

msgid "A user with that username already exists."
msgstr "Seorang pengguna dengan nama pengguna tersebut sudah ada."

msgid "first name"
msgstr "nama depan"

msgid "last name"
msgstr "nama belakang"

msgid "email address"
msgstr "alamat email"

msgid "staff status"
msgstr "status staf"

msgid "Designates whether the user can log into this admin site."
msgstr "Menentukan apakah pengguna berhak masuk ke situs administrasi ini."

msgid "active"
msgstr "aktif"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Menentukan apakah pengguna dianggap aktif. Hapus pilihan ini tanpa perlu "
"menghapus akunnya."

msgid "date joined"
msgstr "tanggal daftar"

msgid "user"
msgstr "pengguna"

msgid "users"
msgstr "pengguna"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Kata sandi Anda harus memuat setidaknya %(min_length)d karakter."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Kata sandi terlalu mirip dengan %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Sandi anda tidak dapat terlalu mirip terhadap informasi pribadi anda."

msgid "This password is too common."
msgstr "Kata sandi ini terlalu umum."

msgid "Your password can’t be a commonly used password."
msgstr "Sandi anda tidak dapat berupa sandi umum digunakan."

msgid "This password is entirely numeric."
msgstr "Kata sandi ini seluruhnya terdiri dari angka."

msgid "Your password can’t be entirely numeric."
msgstr "Sandi anda tidak bisa sepenuhnya numerik."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Penyetelan ulang sandi di %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Masukkan nama pengguna sah. Nilai ini mungkin menggandung tanpa tekanan "
"huruf kecil a-z dan huruf besar A-Z, angka dan karakter @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Masukkan nama pengguna valid. Nilai ini hanya boleh mengandung karakter, "
"angka, dan karakter @/./+/-/_."

msgid "Logged out"
msgstr "Keluar"

msgid "Password reset"
msgstr "Setel ulang sandi"

msgid "Password reset sent"
msgstr "Penyetelan ulang kata sandi telah dikirim"

msgid "Enter new password"
msgstr "Masukkan sandi baru"

msgid "Password reset unsuccessful"
msgstr "Penyetelan ulang sandi gagal"

msgid "Password reset complete"
msgstr "Penyetelan ulang sandi selesai"

msgid "Password change"
msgstr "Ubah sandi"

msgid "Password change successful"
msgstr "Pengubahan sandi berhasil"
