# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> (Punjabi) (http://www.transifex.com/django/django/"
"language/pa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pa\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "ਤਕਨੀਕੀ ਚੋਣਾਂ"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "ਟਾਈਟਲ"

msgid "content"
msgstr "ਸਮੱਗਰੀ"

msgid "enable comments"
msgstr "ਟਿੱਪਣੀਆਂ ਚਾਲੂ"

msgid "template name"
msgstr "ਟੈਪਲੇਟ ਨਾਂ"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""

msgid "registration required"
msgstr "ਰਜਿਸਟਰੇਸ਼ਨ ਲੋੜੀਦੀ ਹੈ"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

msgid "sites"
msgstr ""

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
