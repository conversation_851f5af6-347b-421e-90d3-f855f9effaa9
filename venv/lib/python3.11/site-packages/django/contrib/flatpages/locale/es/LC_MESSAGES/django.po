# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011-2012
# <PERSON>, 2015
# <PERSON>, 2014
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-09-25 17:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (http://www.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opciones avanzadas"

msgid "Flat Pages"
msgstr "Páginas estáticas"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Ejemplo: “/about/contact/”. Asegúrese de tener barras al principio y al "
"final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor solo puede contener letras, números, puntos, guiones bajos o "
"medios, barras o tildes."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Ejemplo: \"/about/contact”. Asegúrese de tener una barra al principio."

msgid "URL is missing a leading slash."
msgstr "A la URL le falta la barra inicial."

msgid "URL is missing a trailing slash."
msgstr "A la URL le falta la barra final."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr " En el sitio %(site)s ya hay una pagina estática con la url %(url)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "contenido"

msgid "enable comments"
msgstr "habilitar comentarios"

msgid "template name"
msgstr "nombre de plantilla"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Ejemplo: \"flatpages/contact_page.html\". Si no se proporciona, el sistema "
"utilizará \"flatpages/default.html\"."

msgid "registration required"
msgstr "Se requiere registro"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Si está marcado, sólo los usuarios registrados podrán ver la página."

msgid "sites"
msgstr "sitios"

msgid "flat page"
msgstr "página estática"

msgid "flat pages"
msgstr "páginas estáticas"
