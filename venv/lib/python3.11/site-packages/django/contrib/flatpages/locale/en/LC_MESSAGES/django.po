# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/flatpages/admin.py:12
msgid "Advanced options"
msgstr ""

#: contrib/flatpages/apps.py:7
msgid "Flat Pages"
msgstr ""

#: contrib/flatpages/forms.py:9 contrib/flatpages/models.py:9
msgid "URL"
msgstr ""

#: contrib/flatpages/forms.py:12
msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""

#: contrib/flatpages/forms.py:15
msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

#: contrib/flatpages/forms.py:29
msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""

#: contrib/flatpages/forms.py:42
msgid "URL is missing a leading slash."
msgstr ""

#: contrib/flatpages/forms.py:47
msgid "URL is missing a trailing slash."
msgstr ""

#: contrib/flatpages/forms.py:64
#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

#: contrib/flatpages/models.py:10
msgid "title"
msgstr ""

#: contrib/flatpages/models.py:11
msgid "content"
msgstr ""

#: contrib/flatpages/models.py:12
msgid "enable comments"
msgstr ""

#: contrib/flatpages/models.py:14
msgid "template name"
msgstr ""

#: contrib/flatpages/models.py:18
msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""

#: contrib/flatpages/models.py:23
msgid "registration required"
msgstr ""

#: contrib/flatpages/models.py:24
msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

#: contrib/flatpages/models.py:27
msgid "sites"
msgstr ""

#: contrib/flatpages/models.py:31
msgid "flat page"
msgstr ""

#: contrib/flatpages/models.py:32
msgid "flat pages"
msgstr ""
