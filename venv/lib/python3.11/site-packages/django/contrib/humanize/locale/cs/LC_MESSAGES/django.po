# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2011,2014
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-18 23:35+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "Humanize"
msgstr "Polidštění"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] " %(value)s milion"
msgstr[1] " %(value)s miliony"
msgstr[2] " %(value)s milionů"
msgstr[3] " %(value)s milionů"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliarda"
msgstr[1] "%(value)s miliardy"
msgstr[2] "%(value)s miliard"
msgstr[3] "%(value)s miliard"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s bilion"
msgstr[1] "%(value)s biliony"
msgstr[2] "%(value)s bilionů"
msgstr[3] "%(value)s bilionů"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s biliarda"
msgstr[1] "%(value)s biliardy"
msgstr[2] "%(value)s biliard"
msgstr[3] "%(value)s biliard"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trilion"
msgstr[1] "%(value)s triliony"
msgstr[2] "%(value)s trilionů"
msgstr[3] "%(value)s trilionů"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s triliarda"
msgstr[1] "%(value)s triliardy"
msgstr[2] "%(value)s triliard"
msgstr[3] "%(value)s triliard"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s kvadrilion"
msgstr[1] "%(value)s kvadriliony"
msgstr[2] "%(value)s kvadrilionů"
msgstr[3] "%(value)s kvadrilionů"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kvadriliarda"
msgstr[1] "%(value)s kvadriliardy"
msgstr[2] "%(value)s kvadriliard"
msgstr[3] "%(value)s kvadriliard"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kvintilion"
msgstr[1] "%(value)s kvintiliony"
msgstr[2] "%(value)s kvintilionů"
msgstr[3] "%(value)s kvintilionů"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kvintiliarda"
msgstr[1] "%(value)s kvintiliardy"
msgstr[2] "%(value)s kvintiliard"
msgstr[3] "%(value)s kvintiliard"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googoly"
msgstr[2] "%(value)s googolů"
msgstr[3] "%(value)s googolů"

msgid "one"
msgstr "jedna"

msgid "two"
msgstr "dvě"

msgid "three"
msgstr "tři"

msgid "four"
msgstr "čtyři"

msgid "five"
msgstr "pět"

msgid "six"
msgstr "šest"

msgid "seven"
msgstr "sedm"

msgid "eight"
msgstr "osm"

msgid "nine"
msgstr "devět"

msgid "today"
msgstr "dnes"

msgid "tomorrow"
msgstr "zítra"

msgid "yesterday"
msgstr "včera"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "před %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "před hodinou"
msgstr[1] "před %(count)s hodinami"
msgstr[2] "o %(count)s hodin dříve"
msgstr[3] "před %(count)s hodinami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "před minutou"
msgstr[1] "před %(count)s minutami"
msgstr[2] "o %(count)s minuty dříve"
msgstr[3] "před %(count)s minutami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "před sekundou"
msgstr[1] "před %(count)s sekundami"
msgstr[2] "o %(count)s sekundy dříve"
msgstr[3] "před %(count)s sekundami"

msgid "now"
msgstr "nyní"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "za sekundu"
msgstr[1] "za %(count)s sekundy"
msgstr[2] "za %(count)s sekundy"
msgstr[3] "za %(count)s sekund"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "za minutu"
msgstr[1] "za %(count)s minuty"
msgstr[2] "za %(count)s minuty"
msgstr[3] "za %(count)s minut"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "za hodinu"
msgstr[1] "za %(count)s hodiny"
msgstr[2] "za %(count)s hodiny"
msgstr[3] "za %(count)s hodin"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "za %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rokem"
msgstr[1] "%(num)d lety"
msgstr[2] "%(num)d rokem"
msgstr[3] "%(num)d lety"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d měsícem"
msgstr[1] "%(num)d měsíci"
msgstr[2] "%(num)d měsícem"
msgstr[3] "%(num)d měsíci"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d týdnem"
msgstr[1] "%(num)d týdny"
msgstr[2] "%(num)d týdny"
msgstr[3] "%(num)d týdny"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dnem"
msgstr[1] "%(num)d dny"
msgstr[2] "%(num)d dny"
msgstr[3] "%(num)d dny"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hodinou"
msgstr[1] "%(num)d hodinami"
msgstr[2] "%(num)d hodinami"
msgstr[3] "%(num)d hodinami"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minutou"
msgstr[1] "%(num)d minutami"
msgstr[2] "%(num)d minutami"
msgstr[3] "%(num)d minutami"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rok"
msgstr[1] "%(num)d roky"
msgstr[2] "%(num)d let"
msgstr[3] "%(num)d let"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d měsíc"
msgstr[1] "%(num)d měsíce"
msgstr[2] "%(num)d měsíců"
msgstr[3] "%(num)d měsíců"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d týden"
msgstr[1] "%(num)d týdny"
msgstr[2] "%(num)d týdnů"
msgstr[3] "%(num)d týdnů"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d den"
msgstr[1] "%(num)d dny"
msgstr[2] "%(num)d dní"
msgstr[3] "%(num)d dní"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hodina"
msgstr[1] "%(num)d hodiny"
msgstr[2] "%(num)d hodin"
msgstr[3] "%(num)d hodin"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuta"
msgstr[1] "%(num)d minuty"
msgstr[2] "%(num)d minut"
msgstr[3] "%(num)d minut"
