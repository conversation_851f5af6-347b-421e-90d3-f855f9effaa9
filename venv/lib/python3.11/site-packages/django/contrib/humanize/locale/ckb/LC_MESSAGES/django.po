# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2023-04-24 18:40+0000\n"
"Last-Translator: Swara <<EMAIL>>, 2022\n"
"Language-Team: Central Kurdish (http://www.transifex.com/django/django/"
"language/ckb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ckb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "بەمرۆڤایەتیکردن"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "‫{}st"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "‫{}nd"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "‫{}rd"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "‫{}th"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "‫{}th"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s ملیۆن"
msgstr[1] "%(value)s ملیۆن"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "‫%(value)s بلیۆن"
msgstr[1] "‫%(value)s بلیۆن"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "‫%(value)s تریلۆن"
msgstr[1] "‫%(value)s تریلۆن"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "‫%(value)s کوادرلیۆن"
msgstr[1] "‫%(value)s کوادرلیۆن"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "‫%(value)s کوینترلیۆن"
msgstr[1] "‫%(value)s کوینترلیۆن"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "‫%(value)s شەش تریلۆن"
msgstr[1] "‫%(value)s شەش تریلۆن"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "‫%(value)s سێپتریلۆن"
msgstr[1] "‫%(value)s سێپتریلۆن"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "‫%(value)s ئۆکتریلیۆن"
msgstr[1] "‫%(value)s ئۆکتریلیۆن"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "‫%(value)s نۆنتریلۆن"
msgstr[1] "‫%(value)s نۆنتریلۆن"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "‫%(value)s دێستریلیۆن"
msgstr[1] "‫%(value)s دێستریلیۆن"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "‫%(value)s گوگۆڵ"
msgstr[1] "‫%(value)s گوگۆڵ"

msgid "one"
msgstr "یەک"

msgid "two"
msgstr "دوو"

msgid "three"
msgstr "سێ"

msgid "four"
msgstr "چوار"

msgid "five"
msgstr "پێنج"

msgid "six"
msgstr "شەش"

msgid "seven"
msgstr "حەوت"

msgid "eight"
msgstr "هەشت"

msgid "nine"
msgstr "نۆ"

msgid "today"
msgstr "ئەمڕۆ"

msgid "tomorrow"
msgstr "سبەینێ"

msgid "yesterday"
msgstr "دوێنێ"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s پێشتر"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "کاتژمێرێک پێشتر"
msgstr[1] "‫%(count)s کاتژمێر پێشتر"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "خولەکێک پێشتر"
msgstr[1] "‫%(count)s خولەک پێشتر"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "چرکەیەک پێشتر"
msgstr[1] "‫%(count)s چرکە پێشتر"

msgid "now"
msgstr "ئێستا"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "چرکەیەک لە ئێستاوە"
msgstr[1] "‫%(count)s چرکە لە ئێستاوە"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "خولەکێک لە ئێستاوە"
msgstr[1] "‫%(count)s خولەک لە ئێستاوە"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "کاتژمێرێک لە ئێستاوە"
msgstr[1] "‫%(count)s کاتژمێر لە ئێستاوە"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s لە ئێستاوە"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "‫%(num)d ساڵ"
msgstr[1] "‫%(num)d ساڵ"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "‫%(num)d مانگ"
msgstr[1] "‫%(num)d مانگ"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "‫%(num)d هەفتە"
msgstr[1] "‫%(num)d هەفتە"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "‫%(num)d ڕۆژ"
msgstr[1] "‫%(num)d ڕۆژ"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "‫%(num)d کاتژمێر"
msgstr[1] "‫%(num)d کاتژمێر"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "‫%(num)d خولەک"
msgstr[1] "‫%(num)d خولەک"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "‫%(num)d ساڵ"
msgstr[1] "‫%(num)d ساڵ"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "‫%(num)d مانگ"
msgstr[1] "‫%(num)d مانگ"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "‫%(num)d هەفتە"
msgstr[1] "‫%(num)d هەفتە"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "‫%(num)d ڕۆژ"
msgstr[1] "‫%(num)d ڕۆژ"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "‫%(num)d کاتژمێر"
msgstr[1] "‫%(num)d کاتژمێر"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "‫%(num)d خولەک"
msgstr[1] "‫%(num)d خولەک"
