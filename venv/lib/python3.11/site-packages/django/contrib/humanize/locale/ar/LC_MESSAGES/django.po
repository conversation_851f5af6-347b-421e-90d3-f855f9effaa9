# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2020-2021
# <PERSON><PERSON><PERSON>, 2014
# <PERSON>ya<PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-10-15 21:36+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Arabic (http://www.transifex.com/django/django/language/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

msgid "Humanize"
msgstr "عمل صفة بشرية"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s مليون"
msgstr[1] "%(value)s مليون"
msgstr[2] "%(value)s مليون"
msgstr[3] "%(value)s ملايين"
msgstr[4] "%(value)s مليون"
msgstr[5] "%(value)s مليون"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s مليار"
msgstr[1] "%(value)s مليار"
msgstr[2] "%(value)s مليار"
msgstr[3] "%(value)s مليار"
msgstr[4] "%(value)s مليار"
msgstr[5] "%(value)s مليار"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s ترليون"
msgstr[1] "%(value)s ترليون"
msgstr[2] "%(value)s ترليون"
msgstr[3] "%(value)s ترليون"
msgstr[4] "%(value)s ترليون"
msgstr[5] "%(value)s ترليون"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s كوادرليون"
msgstr[1] "%(value)s كوادرليون"
msgstr[2] "%(value)s كوادرليون"
msgstr[3] "%(value)s كوادرليون"
msgstr[4] "%(value)s كوادرليون"
msgstr[5] "%(value)s كوادرليون"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s كوينتيليون"
msgstr[1] "%(value)s كوينتيليون"
msgstr[2] "%(value)s كوينتيليون"
msgstr[3] "%(value)s كوينتيليون"
msgstr[4] "%(value)s كوينتيليون"
msgstr[5] "%(value)s كوينتيليون"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s سكستيليون"
msgstr[1] "%(value)s سكستيليون"
msgstr[2] "%(value)s سكستيليون"
msgstr[3] "%(value)s سكستيليون"
msgstr[4] "%(value)s سكستيليون"
msgstr[5] "%(value)s سكستيليون"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s سبتيليون"
msgstr[1] "%(value)s سبتيليون"
msgstr[2] "%(value)s سبتيليون"
msgstr[3] "%(value)s سبتيليون"
msgstr[4] "%(value)s سبتيليون"
msgstr[5] "%(value)s سبتيليون"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s أقتيليون"
msgstr[1] "%(value)s أقتيليون"
msgstr[2] "%(value)s أقتيليون"
msgstr[3] "%(value)s أقتيليون"
msgstr[4] "%(value)s أقتيليون"
msgstr[5] "%(value)s أقتيليون"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s نانليون"
msgstr[1] "%(value)s نانليون"
msgstr[2] "%(value)s نانليون"
msgstr[3] "%(value)s نانليون"
msgstr[4] "%(value)s نانليون"
msgstr[5] "%(value)s نانليون"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s ديسيليون"
msgstr[1] "%(value)s ديسيليون"
msgstr[2] "%(value)s ديسيليون"
msgstr[3] "%(value)s ديسيليون"
msgstr[4] "%(value)s ديسيليون"
msgstr[5] "%(value)s ديسيليون"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s جوجول"
msgstr[1] "%(value)s جوجول"
msgstr[2] "%(value)s جوجول"
msgstr[3] "%(value)s جوجول"
msgstr[4] "%(value)s جوجول"
msgstr[5] "%(value)s جوجول"

msgid "one"
msgstr "واحد"

msgid "two"
msgstr "إثنان"

msgid "three"
msgstr "ثلالثة"

msgid "four"
msgstr "أربعة"

msgid "five"
msgstr "خمسة"

msgid "six"
msgstr "ستة"

msgid "seven"
msgstr "سبعة"

msgid "eight"
msgstr "ثمانية"

msgid "nine"
msgstr "تسعة"

msgid "today"
msgstr "اليوم"

msgid "tomorrow"
msgstr "غداً"

msgid "yesterday"
msgstr "أمس"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s مضت"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "منذ %(count)s ساعة"
msgstr[1] "منذ ساعة"
msgstr[2] "منذ %(count)s ساعة"
msgstr[3] "منذ %(count)s ساعة"
msgstr[4] "منذ %(count)s ساعة"
msgstr[5] "منذ %(count)s ساعة"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "منذ %(count)s دقيقة"
msgstr[1] "منذ دقيقة"
msgstr[2] "منذ %(count)s دقيقة"
msgstr[3] "منذ %(count)s دقيقة"
msgstr[4] "منذ %(count)s دقيقة"
msgstr[5] "منذ %(count)s دقيقة"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "منذ %(count)s ثانية"
msgstr[1] "منذ ثانية"
msgstr[2] "منذ %(count)s ثانيتين"
msgstr[3] "منذ %(count)s ثواني"
msgstr[4] "منذ %(count)s ثانية"
msgstr[5] "منذ %(count)s ثانية"

msgid "now"
msgstr "الآن"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "%(count)s ثواني من الآن"
msgstr[1] "منذ ثانية من الآن"
msgstr[2] "%(count)s ثواني من الآن"
msgstr[3] "%(count)s ثواني من الآن"
msgstr[4] "%(count)s ثواني من الآن"
msgstr[5] "%(count)s ثواني من الآن"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "%(count)s دقائق من الآن"
msgstr[1] "منذ دقيقة من الآن"
msgstr[2] "%(count)s دقائق من الآن"
msgstr[3] "%(count)s دقائق من الآن"
msgstr[4] "%(count)s دقائق من الآن"
msgstr[5] "%(count)s دقائق من الآن"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "%(count)s ساعات من الآن"
msgstr[1] "منذ ساعة من الآن"
msgstr[2] "%(count)s ساعات من الآن"
msgstr[3] "%(count)s ساعات من الآن"
msgstr[4] "%(count)s ساعات من الآن"
msgstr[5] "%(count)s ساعات من الآن"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s من الآن"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d سنة"
msgstr[1] "%(num)d سنة"
msgstr[2] "%(num)d سنتين"
msgstr[3] "%(num)d سنوات"
msgstr[4] "%(num)d سنوات"
msgstr[5] "%(num)d سنوات"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d شهر"
msgstr[1] "%(num)d شهر"
msgstr[2] "%(num)d شهرين"
msgstr[3] "%(num)d أشهر"
msgstr[4] "%(num)d أشهر"
msgstr[5] "%(num)d أشهر"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d أسبوع"
msgstr[1] "%(num)d أسبوع"
msgstr[2] "%(num)d أسبوعين"
msgstr[3] "%(num)d أسابيع"
msgstr[4] "%(num)d أسبوع"
msgstr[5] "%(num)d أسابيع"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d يوم"
msgstr[1] "%(num)d يوم"
msgstr[2] "%(num)d يومين"
msgstr[3] "%(num)d أيام"
msgstr[4] "%(num)d يوم"
msgstr[5] "%(num)d أيام"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ساعة"
msgstr[1] "%(num)d ساعة"
msgstr[2] "%(num)d ساعتين"
msgstr[3] "%(num)d ساعات"
msgstr[4] "%(num)d ساعة"
msgstr[5] "%(num)d ساعات"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d دقيقة"
msgstr[1] "%(num)d دقيقة"
msgstr[2] "%(num)d دقيقتين"
msgstr[3] "%(num)d دقائق"
msgstr[4] "%(num)d دقيقة"
msgstr[5] "%(num)d دقيقة"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d سنة"
msgstr[1] "%(num)d سنة"
msgstr[2] "%(num)d سنتين"
msgstr[3] "%(num)d سنوات"
msgstr[4] "%(num)d سنة"
msgstr[5] "%(num)d سنوات"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d شهر"
msgstr[1] "%(num)d شهر"
msgstr[2] "%(num)d شهرين"
msgstr[3] "%(num)d أشهر"
msgstr[4] "%(num)d شهر"
msgstr[5] "%(num)d أشهر"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d أسبوع"
msgstr[1] "%(num)d أسبوع"
msgstr[2] "%(num)d أسبوعين"
msgstr[3] "%(num)d أسابيع"
msgstr[4] "%(num)d أسبوع"
msgstr[5] "%(num)d أسابيع"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d يوم"
msgstr[1] "%(num)d يوم"
msgstr[2] "%(num)d يومين"
msgstr[3] "%(num)d أيام"
msgstr[4] "%(num)d يوم"
msgstr[5] "%(num)d أيام"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ساعة"
msgstr[1] "%(num)d ساعة"
msgstr[2] "%(num)d ساعتين"
msgstr[3] "%(num)d ساعات"
msgstr[4] "%(num)d ساعة"
msgstr[5] "%(num)d ساعات"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d دقيقة"
msgstr[1] "%(num)d دقيقة"
msgstr[2] "%(num)d دقيقتين"
msgstr[3] "%(num)d دقائق"
msgstr[4] "%(num)d دقيقة"
msgstr[5] "%(num)d دقيقة"
