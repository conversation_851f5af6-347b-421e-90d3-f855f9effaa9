
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-05-19T12:40:46+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "b49155ea7eaec698a0357fb9bc1147533b566916",
 "version": "2.1.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
