import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Segment, Symbol
from django.conf import settings
from PIL import Image, ImageDraw, ImageFont
import time
import numpy as np

def create_visible_bubbles(drawing_id):
    try:
        drawing = Drawing.objects.get(id=drawing_id)
        print(f"Creating visible bubbles for drawing {drawing_id}: {drawing.original_filename}")

        # Load the original drawing image
        image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
        if not os.path.exists(image_path):
            print(f"Original image not found at {image_path}")
            return False

        print(f"Loading original image from {image_path}")

        # Open the image with PIL
        img = Image.open(image_path)
        width, height = img.size
        print(f"Image dimensions: {width}x{height}")

        # Create a new image with the same dimensions
        new_img = img.copy()
        draw = ImageDraw.Draw(new_img)

        # Try to load a font, fall back to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 60)  # Even larger font for visibility
            print("Using Arial font")
        except IOError:
            try:
                # Try system fonts
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 60)
                print("Using Helvetica font")
            except IOError:
                font = ImageFont.load_default()
                print("Using default font")

        # Get all segments for this drawing
        segments = Segment.objects.filter(drawing=drawing)

        # Define the segment order (2, 1, 3, 4, ...)
        segment_order = []

        # First, try to find segments with numbers 2, 1, 3, 4
        for segment_number in [2, 1, 3, 4]:
            segment = segments.filter(segment_number=segment_number).first()
            if segment:
                segment_order.append(segment)

        # Add any remaining segments in their natural order
        for segment in segments:
            if segment not in segment_order:
                segment_order.append(segment)

        # Get all symbols from all segments
        all_symbols = []
        for segment in segment_order:
            symbols = Symbol.objects.filter(segment=segment, is_marked=True)
            for symbol in symbols:
                x_offset = segment.segment_x_offset or 0
                y_offset = segment.segment_y_offset or 0
                abs_x = x_offset + symbol.x_coordinate
                abs_y = y_offset + symbol.y_coordinate
                all_symbols.append({
                    'id': symbol.id,
                    'value': symbol.value,
                    'x': abs_x,
                    'y': abs_y,
                    'segment': segment.segment_number
                })

        print(f"Found {len(all_symbols)} symbols across all segments")

        # Calculate the scaling factor to fit all symbols within the image
        if all_symbols:
            min_x = min(s['x'] for s in all_symbols)
            max_x = max(s['x'] for s in all_symbols)
            min_y = min(s['y'] for s in all_symbols)
            max_y = max(s['y'] for s in all_symbols)

            print(f"Symbol coordinate ranges: X: {min_x} to {max_x}, Y: {min_y} to {max_y}")

            # Calculate scaling factor to fit all symbols within the image
            # with some padding (0.9 = 90% of the image)
            scale_x = (width * 0.9) / (max_x - min_x) if max_x > min_x else 1
            scale_y = (height * 0.9) / (max_y - min_y) if max_y > min_y else 1
            scale = min(scale_x, scale_y)

            # Calculate offset to center the symbols
            offset_x = (width - (max_x - min_x) * scale) / 2 - min_x * scale
            offset_y = (height - (max_y - min_y) * scale) / 2 - min_y * scale

            print(f"Using scale factor: {scale}, offsets: ({offset_x}, {offset_y})")

            # Draw each symbol with its sequential number
            for i, symbol in enumerate(all_symbols, 1):
                # Calculate the scaled position
                scaled_x = symbol['x'] * scale + offset_x
                scaled_y = symbol['y'] * scale + offset_y

                print(f"Symbol {i}: {symbol['value']} at original ({symbol['x']}, {symbol['y']}), scaled to ({scaled_x}, {scaled_y})")

                # Draw a circle for the symbol - make it larger and more visible
                circle_radius = 50
                draw.ellipse(
                    [(scaled_x - circle_radius, scaled_y - circle_radius),
                     (scaled_x + circle_radius, scaled_y + circle_radius)],
                    fill="blue",
                    outline="blue",
                    width=8
                )

                # Draw the symbol number
                try:
                    # For newer PIL versions
                    left, top, right, bottom = draw.textbbox((0, 0), str(i), font=font)
                    text_width = right - left
                    text_height = bottom - top
                except AttributeError:
                    # Fallback for older PIL versions
                    try:
                        text_width, text_height = draw.textsize(str(i), font=font)
                    except:
                        text_width, text_height = 30, 30  # Default fallback values

                draw.text(
                    (scaled_x - text_width/2, scaled_y - text_height/2),
                    str(i),
                    fill="white",
                    font=font
                )

        # Save the finalized image
        output_path = os.path.join(settings.MEDIA_ROOT, f"finalized_{drawing_id}.png")
        new_img.save(output_path)
        print(f"Saved finalized image with visible bubbles to {output_path}")

        # Update the drawing record
        drawing.finalized_image = f"finalized_{drawing_id}.png"
        drawing.save()
        print(f"Updated drawing record with new finalized image path")

        return True
    except Exception as e:
        print(f"Error creating visible bubbles: {e}")
        import traceback
        traceback.print_exc()
        return False

# Create visible bubbles for drawing 28
success = create_visible_bubbles(28)
print(f"Creation completed: {'Success' if success else 'Failed'}")
