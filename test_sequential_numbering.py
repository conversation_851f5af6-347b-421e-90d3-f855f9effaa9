import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing

# We'll directly use the functions from api/utils.py
# First, let's define the functions we need
from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
from api.models import Segment, Symbol
import os

def generate_finalized_drawing(drawing):
    """
    Generate a finalized drawing with numbered bubbles.
    The numbering follows the specified segment order: 2, 1, 3, 4.
    """
    # Load the original drawing image
    image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
    if not os.path.exists(image_path):
        return None

    # Open the image with PIL
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)

    # Try to load a font, fall back to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 40)  # Larger font size for better visibility
    except IOError:
        try:
            # Try system fonts
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 40)
        except IOError:
            font = ImageFont.load_default()

    # Get all segments for this drawing
    segments = Segment.objects.filter(drawing=drawing)

    # Define the segment order (2, 1, 3, 4, ...)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    # Initialize a counter for symbol numbering across all segments
    symbol_counter = 1

    # Process each segment in the specified order (2, 1, 3, 4)
    for segment in segment_order:
        # Get all marked symbols for this segment
        symbols = Symbol.objects.filter(segment=segment, is_marked=True)

        # Calculate the offset for this segment
        x_offset = segment.segment_x_offset or 0
        y_offset = segment.segment_y_offset or 0

        # Draw each symbol with its sequential number
        for symbol in symbols:
            # Calculate the absolute position in the full drawing
            abs_x = x_offset + symbol.x_coordinate
            abs_y = y_offset + symbol.y_coordinate

            # Draw a circle for the symbol - larger blue bubble
            circle_radius = 50  # Larger radius for better visibility
            draw.ellipse(
                [(abs_x - circle_radius, abs_y - circle_radius),
                 (abs_x + circle_radius, abs_y + circle_radius)],
                outline="blue",
                width=5  # Thicker outline
            )

            # Add inner circle for emphasis
            inner_radius = int(circle_radius * 0.8)
            draw.ellipse(
                [(abs_x - inner_radius, abs_y - inner_radius),
                 (abs_x + inner_radius, abs_y + inner_radius)],
                outline="blue",
                width=2
            )

            # Draw the symbol number
            # PIL.ImageDraw.Draw.textsize is deprecated, use textbbox or textlength instead
            try:
                # For newer PIL versions
                left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                text_width = right - left
                text_height = bottom - top
            except AttributeError:
                # Fallback for older PIL versions
                try:
                    text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                except:
                    text_width, text_height = 10, 10  # Default fallback values

            # Draw white text with segment number
            draw.text(
                (abs_x - text_width/2, abs_y - text_height/2),
                str(symbol_counter),
                fill="white",
                font=font
            )

            # Increment the counter
            symbol_counter += 1

    # Save the finalized image
    output_path = os.path.join(settings.MEDIA_ROOT, f"finalized_{drawing.id}.png")
    img.save(output_path)

    # Return the relative path
    return f"finalized_{drawing.id}.png"

# Get a drawing that has symbols
drawing = Drawing.objects.filter(processed=True).first()
if drawing:
    print(f"Found drawing: {drawing.id} - {drawing.original_filename}")

    # Print segment information
    from api.models import Segment, Symbol
    segments = Segment.objects.filter(drawing=drawing)

    # Define the segment order (2, 1, 3, 4, ...)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    print(f"Processing segments in order: {[s.segment_number for s in segment_order]}")

    # Count symbols in each segment
    for segment in segment_order:
        symbol_count = Symbol.objects.filter(segment=segment, is_marked=True).count()
        print(f"Segment {segment.segment_number}: {symbol_count} marked symbols")

    # Generate the finalized image
    finalized_image_path = generate_finalized_drawing(drawing)
    if finalized_image_path:
        print(f"Generated finalized image: {finalized_image_path}")
        drawing.finalized_image = finalized_image_path
        drawing.save()
        print("Drawing updated with new finalized image")
    else:
        print("Failed to generate finalized image")
else:
    print("No processed drawings found")
