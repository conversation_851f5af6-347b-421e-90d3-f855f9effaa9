
        return Response({
            'message': 'Similarity confirmation recorded',
            'similarity': SegmentSimilaritySerializer(similarity).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Finalize Drawing
@api_view(['POST'])
def finalize_drawing(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)

    # Mark drawing as finalized
    drawing.finalized = True
    drawing.save()

    # Generate the finalized image with numbered bubbles
    from api.utils import generate_measurement_excel
    from api.utils import generate_finalized_drawing

    # Generate and save the finalized image
    finalized_image_path = generate_finalized_drawing(drawing)
    if finalized_image_path:
        drawing.finalized_image = finalized_image_path

    # Generate and save the measurements Excel file
    measurements_excel_path = generate_measurement_excel(drawing)
    if measurements_excel_path:
        drawing.measurements_excel = measurements_excel_path

    # Save the drawing with updated file paths
    drawing.save()

    # Log finalization
    AnalysisLog.objects.create(
        drawing=drawing,
        message="Drawing finalized with numbered bubbles and measurements Excel file"
    )

    return Response({
