from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.http import HttpResponse
from django.shortcuts import render
import os

def react_app_view(request):
    """Serve the React app for all non-API routes"""
    try:
        # Path to the React build index.html
        index_path = os.path.join(settings.BASE_DIR, 'frontend', 'dist', 'index.html')
        with open(index_path, 'r', encoding='utf-8') as f:
            return HttpResponse(f.read(), content_type='text/html')
    except FileNotFoundError:
        return HttpResponse(
            '<h1>React App Not Found</h1><p>Please build the React app first by running <code>npm run build</code> in the frontend directory.</p>',
            content_type='text/html',
            status=404
        )

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
    # Catch-all pattern for React app (must be last)
    re_path(r'^.*$', react_app_view, name='react-app'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
