import React, { useState, useRef, useEffect } from 'react';
import { FaDownload, FaSpinner, FaImage } from 'react-icons/fa';
import axios from 'axios';
import './FinalizedDrawingGenerator.scss';

const FinalizedDrawingGenerator = ({ drawingId, bubbleSize = 60 }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [drawing, setDrawing] = useState(null);
  const [allSymbols, setAllSymbols] = useState([]);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [generating, setGenerating] = useState(false);
  
  const containerRef = useRef(null);
  const imageRef = useRef(null);
  const canvasRef = useRef(null);

  // Fetch drawing and all symbols data
  useEffect(() => {
    const fetchData = async () => {
      if (!drawingId) return;
      
      try {
        setLoading(true);
        setError(null);

        // Fetch drawing details
        const drawingResponse = await axios.get(`/api/results/${drawingId}/`);
        setDrawing(drawingResponse.data.drawing);

        // Fetch all segments and their symbols
        const segments = drawingResponse.data.segments || [];
        const allSymbolsData = [];
        let symbolCounter = 1;

        // Process segments in the correct order (2, 1, 3, 4, ...)
        const segmentOrder = [];
        
        // First, try to find segments with numbers 2, 1, 3, 4
        for (const segmentNumber of [2, 1, 3, 4]) {
          const segment = segments.find(s => s.segment_number === segmentNumber);
          if (segment) {
            segmentOrder.push(segment);
          }
        }
        
        // Add any remaining segments in their natural order
        for (const segment of segments) {
          if (!segmentOrder.includes(segment)) {
            segmentOrder.push(segment);
          }
        }

        // Process each segment in the specified order
        for (const segment of segmentOrder) {
          try {
            const segmentResponse = await axios.get(`/api/segment/${segment.id}/`);
            const symbols = segmentResponse.data.symbols || [];
            
            // Filter only marked symbols and add segment offset
            const markedSymbols = symbols
              .filter(symbol => symbol.is_marked)
              .map(symbol => ({
                ...symbol,
                // Calculate absolute position in the full drawing
                absoluteX: (segment.segment_x_offset || 0) + symbol.x_coordinate,
                absoluteY: (segment.segment_y_offset || 0) + symbol.y_coordinate,
                sequentialNumber: symbolCounter++,
                segmentNumber: segment.segment_number
              }));
            
            allSymbolsData.push(...markedSymbols);
          } catch (err) {
            console.error(`Error fetching segment ${segment.id}:`, err);
          }
        }

        setAllSymbols(allSymbolsData);
        console.log(`Loaded ${allSymbolsData.length} symbols for finalized drawing`);
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load drawing data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [drawingId]);

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  // Generate and download the finalized image
  const generateFinalizedImage = async () => {
    if (!imageLoaded || !imageRef.current || !containerRef.current) {
      setError('Image not ready for generation');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      const image = imageRef.current;
      const container = containerRef.current;
      
      // Create canvas with the same dimensions as the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size to match the original image dimensions
      canvas.width = image.naturalWidth;
      canvas.height = image.naturalHeight;
      
      // Calculate scale factors
      const scaleX = image.naturalWidth / image.offsetWidth;
      const scaleY = image.naturalHeight / image.offsetHeight;
      
      // Draw the original image
      ctx.drawImage(image, 0, 0);
      
      // Draw bubbles
      allSymbols.forEach(symbol => {
        const x = symbol.absoluteX * scaleX;
        const y = symbol.absoluteY * scaleY;
        const radius = (bubbleSize / 2) * scaleX; // Scale bubble size
        
        // Draw outer circle (blue background)
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        ctx.fillStyle = '#007bff'; // Blue color
        ctx.fill();
        ctx.strokeStyle = '#0056b3';
        ctx.lineWidth = Math.max(1, bubbleSize * 0.05 * scaleX);
        ctx.stroke();
        
        // Draw inner circle with white outline
        const innerRadius = radius * 0.9;
        ctx.beginPath();
        ctx.arc(x, y, innerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = '#007bff';
        ctx.fill();
        ctx.strokeStyle = 'white';
        ctx.lineWidth = Math.max(1, bubbleSize * 0.02 * scaleX);
        ctx.stroke();
        
        // Draw text
        const fontSize = Math.max(20, bubbleSize * 1.2 * scaleX); // Large font size
        ctx.font = `bold ${fontSize}px Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Draw text stroke (black outline)
        const strokeWidth = Math.max(1, bubbleSize * 0.02 * scaleX);
        ctx.strokeStyle = 'black';
        ctx.lineWidth = strokeWidth * 2;
        ctx.strokeText(symbol.sequentialNumber.toString(), x, y);
        
        // Draw main text (white)
        ctx.fillStyle = 'white';
        ctx.fillText(symbol.sequentialNumber.toString(), x, y);
      });
      
      // Convert canvas to blob and download
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `finalized_drawing_${drawingId}_bubbles_${bubbleSize}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        setGenerating(false);
      }, 'image/png', 1.0);
      
    } catch (err) {
      console.error('Error generating finalized image:', err);
      setError('Failed to generate finalized image');
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="finalized-generator-loading">
        <FaSpinner className="spinner" />
        <span>Loading drawing data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="finalized-generator-error">
        <span>{error}</span>
      </div>
    );
  }

  if (!drawing) {
    return null;
  }

  return (
    <div className="finalized-drawing-generator">
      <div className="generator-header">
        <h3>Frontend Finalized Drawing Generator</h3>
        <p>Generate high-quality finalized drawing with perfect bubble text visibility</p>
      </div>
      
      <div 
        ref={containerRef}
        className="drawing-preview-container"
        style={{ position: 'relative', display: 'inline-block' }}
      >
        <img
          ref={imageRef}
          src={drawing.image}
          alt="Original Drawing"
          className="preview-image"
          onLoad={handleImageLoad}
          style={{ maxWidth: '100%', height: 'auto' }}
        />
        
        {/* Render frontend bubbles as overlay */}
        {imageLoaded && allSymbols.map(symbol => {
          // Calculate position relative to the displayed image
          const image = imageRef.current;
          if (!image) return null;
          
          const scaleX = image.offsetWidth / image.naturalWidth;
          const scaleY = image.offsetHeight / image.naturalHeight;
          
          const displayX = symbol.absoluteX * scaleX;
          const displayY = symbol.absoluteY * scaleY;
          
          const markerSize = bubbleSize;
          const fontSize = Math.max(12, Math.floor(markerSize * 0.6));
          
          return (
            <div
              key={`preview-bubble-${symbol.id}`}
              className="preview-bubble"
              style={{
                position: 'absolute',
                left: `${displayX}px`,
                top: `${displayY}px`,
                width: `${markerSize}px`,
                height: `${markerSize}px`,
                backgroundColor: '#007bff',
                border: '2px solid white',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: `${fontSize}px`,
                transform: 'translate(-50%, -50%)',
                zIndex: 10,
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
              }}
            >
              {symbol.sequentialNumber}
            </div>
          );
        })}
      </div>
      
      <div className="generator-actions">
        <button
          className="btn btn-primary"
          onClick={generateFinalizedImage}
          disabled={!imageLoaded || generating || allSymbols.length === 0}
        >
          {generating ? (
            <>
              <FaSpinner className="spinner" />
              Generating...
            </>
          ) : (
            <>
              <FaImage />
              Generate & Download High-Quality Image
            </>
          )}
        </button>
        
        <div className="symbol-count">
          {allSymbols.length} symbols will be numbered
        </div>
      </div>
    </div>
  );
};

export default FinalizedDrawingGenerator;
