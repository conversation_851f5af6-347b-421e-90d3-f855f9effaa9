.finalized-drawing-generator {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  border: 2px solid #e3f2fd;

  .generator-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      color: #007bff;
      margin-bottom: 8px;
      font-weight: 600;
      font-size: 1.4rem;
    }

    p {
      color: #6c757d;
      margin: 0;
      font-size: 14px;
      font-style: italic;
    }
  }

  .drawing-preview-container {
    text-align: center;
    margin-bottom: 20px;
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
    position: relative;

    .preview-image {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .preview-bubble {
      pointer-events: none;
      user-select: none;
      box-shadow: 0 3px 6px rgba(0, 123, 255, 0.4);
      
      // Ensure text is crisp and readable
      font-family: 'Arial', sans-serif;
      line-height: 1;
      
      // Add subtle animation
      transition: transform 0.2s ease;
      
      // Enhanced text rendering
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      
      &:hover {
        transform: translate(-50%, -50%) scale(1.1);
      }
    }
  }

  .bubble-size-control {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    .bubble-size-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #495057;

        .bubble-icon {
          color: #007bff;
        }
      }

      .bubble-size-value {
        .bubble-preview {
          background: #007bff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 12px;
          min-width: 20px;
          min-height: 20px;
        }
      }
    }

    .bubble-size-slider-container {
      display: flex;
      align-items: center;
      gap: 15px;

      .bubble-size-slider {
        flex: 1;
        height: 6px;
        border-radius: 3px;
        background: #dee2e6;
        outline: none;
        -webkit-appearance: none;

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #007bff;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        &::-moz-range-thumb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #007bff;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      }

      .btn-apply {
        padding: 8px 16px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: #218838;
        }

        &:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }

        .spinner-icon {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .download-actions {
    text-align: center;

    .btn {
      padding: 14px 28px;
      border-radius: 8px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      text-decoration: none;

      &.btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #0056b3, #004085);
          transform: translateY(-3px);
          box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
        }

        &:active:not(:disabled) {
          transform: translateY(-1px);
        }

        &:disabled {
          background: #6c757d;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
          opacity: 0.6;
        }
      }

      &.btn-secondary {
        background: #6c757d;
        color: white;

        &:hover {
          background: #5a6268;
          transform: translateY(-2px);
        }
      }

      .spinner {
        animation: spin 1s linear infinite;
      }
    }

    .symbol-count {
      margin-top: 15px;
      color: #007bff;
      font-size: 14px;
      font-weight: 600;
      background: rgba(0, 123, 255, 0.1);
      padding: 8px 16px;
      border-radius: 20px;
      display: inline-block;
    }
  }

  .finalized-generator-loading,
  .finalized-generator-error {
    text-align: center;
    padding: 40px 20px;

    .spinner {
      animation: spin 1s linear infinite;
      margin-right: 8px;
      color: #007bff;
      font-size: 1.2rem;
    }

    span {
      color: #6c757d;
      font-weight: 500;
      font-size: 16px;
    }
  }

  .finalized-generator-error {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    
    span {
      color: #dc3545;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .finalized-drawing-generator {
    padding: 15px;
    margin: 15px 0;

    .generator-header {
      h3 {
        font-size: 1.2rem;
      }
    }

    .drawing-preview-container {
      padding: 15px;
    }

    .generator-actions {
      .btn {
        padding: 12px 24px;
        font-size: 14px;
      }
    }
  }
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .finalized-drawing-generator {
    .preview-bubble {
      // Ensure crisp rendering on high DPI displays
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
}

// Print styles
@media print {
  .finalized-drawing-generator {
    .generator-actions {
      display: none;
    }
    
    .drawing-preview-container {
      border: none;
      background: white;
    }
  }
}
