import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Segment, Symbol

# Get the drawing
drawing_id = 28
drawing = Drawing.objects.get(id=drawing_id)
print(f"Checking drawing {drawing_id}: {drawing.original_filename}")

# Get all segments
segments = Segment.objects.filter(drawing=drawing)
print(f"Total segments: {segments.count()}")

# Check marked symbols in each segment
total_marked = 0
for segment in segments:
    marked_symbols = Symbol.objects.filter(segment=segment, is_marked=True)
    print(f"Segment {segment.segment_number}: {marked_symbols.count()} marked symbols")
    for symbol in marked_symbols:
        print(f"  - Symbol {symbol.id}: {symbol.value} at ({symbol.x_coordinate}, {symbol.y_coordinate})")
    total_marked += marked_symbols.count()

print(f"Total marked symbols: {total_marked}")

# Check if the finalized image exists
import os
from django.conf import settings
finalized_image_path = os.path.join(settings.MEDIA_ROOT, f"finalized_{drawing_id}.png")
if os.path.exists(finalized_image_path):
    print(f"Finalized image exists at {finalized_image_path}")
    # Check file size
    size = os.path.getsize(finalized_image_path)
    print(f"File size: {size} bytes")
else:
    print(f"Finalized image does not exist at {finalized_image_path}")
