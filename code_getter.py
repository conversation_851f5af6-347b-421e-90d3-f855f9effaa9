import os
from datetime import datetime

def create_master_file(root_dir, output_file="master_code_file.txt"):
    """
    Recursively reads all files in a Django project directory and creates a master file
    containing all code with file paths.
    
    Args:
        root_dir (str): Root directory of the project
        output_file (str): Name of the output master file
    """
    # List of file extensions to include
    code_extensions = {
        '.py',    # Python files
        '.html',  # HTML template files
        '.css',   # CSS files
        '.js',    # JavaScript files
        '.json',  # JSON files
        '.yaml',  # YAML configuration files
        '.md',    # Markdown files for documentation
    }
    
    # List of directories to exclude
    exclude_dirs = {
        '__pycache__',
        'migrations',
        '.git',
        'venv',
        'env',
        '.idea',
        '.vscode',
        'node_modules',
        'staticfiles',  # If you have collected static files
    }
    
    with open(output_file, 'w', encoding='utf-8') as master_file:
        # Write header with timestamp
        master_file.write(f"Django Project Code Archive\n")
        master_file.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        master_file.write("="*50 + "\n\n")
        
        # Walk through directory tree
        for root, dirs, files in os.walk(root_dir):
            # Remove excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                file_path = os.path.join(root, file)
                _, ext = os.path.splitext(file)
                
                # Skip files that don't have the extensions we're looking for
                if ext not in code_extensions:
                    continue
                    
                try:
                    with open(file_path, 'r', encoding='utf-8') as code_file:
                        # Write file path header
                        master_file.write(f"FILE: {file_path}\n")
                        master_file.write("-"*50 + "\n\n")
                        
                        # Write file contents
                        master_file.write(code_file.read())
                        master_file.write("\n\n")
                        master_file.write("="*50 + "\n\n")
                except Exception as e:
                    master_file.write(f"Error reading {file_path}: {str(e)}\n\n")

if __name__ == "__main__":
    # Get the current directory as root
    project_root = os.getcwd()
    
    try:
        create_master_file(project_root)
        print(f"Master code file has been created successfully!")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

